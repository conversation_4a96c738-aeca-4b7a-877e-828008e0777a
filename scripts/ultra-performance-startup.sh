#!/bin/bash

# =====================================================
# 水费系统极致性能启动脚本
# 包含JVM优化、系统优化、监控配置等
# =====================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查Java版本
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未在PATH中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
    log_info "Java版本: $JAVA_VERSION"
    
    # 检查系统资源
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    CPU_CORES=$(nproc)
    
    log_info "系统内存: ${TOTAL_MEM}MB"
    log_info "CPU核心数: $CPU_CORES"
    
    # 检查磁盘空间
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 80 ]; then
        log_warn "磁盘使用率过高: ${DISK_USAGE}%"
    fi
}

# 系统优化
optimize_system() {
    log_info "应用系统优化配置..."
    
    # 设置文件描述符限制
    ulimit -n 65536
    log_info "设置文件描述符限制: 65536"
    
    # 设置进程数限制
    ulimit -u 32768
    log_info "设置进程数限制: 32768"
    
    # 设置内存锁定限制
    ulimit -l unlimited
    log_info "设置内存锁定限制: unlimited"
    
    # TCP优化（需要root权限）
    if [ "$EUID" -eq 0 ]; then
        echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
        echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf
        echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
        sysctl -p
        log_info "应用TCP优化配置"
    else
        log_warn "非root用户，跳过TCP优化配置"
    fi
}

# 计算JVM参数
calculate_jvm_params() {
    log_info "计算JVM优化参数..."
    
    # 计算堆内存大小（系统内存的70%）
    HEAP_SIZE=$((TOTAL_MEM * 70 / 100))
    if [ $HEAP_SIZE -gt 32768 ]; then
        HEAP_SIZE=32768  # 最大32GB
    fi
    
    # 计算新生代大小（堆内存的30%）
    YOUNG_SIZE=$((HEAP_SIZE * 30 / 100))
    
    # 计算MetaSpace大小
    METASPACE_SIZE=512
    
    # 计算直接内存大小
    DIRECT_MEMORY=$((HEAP_SIZE / 4))
    
    log_info "堆内存大小: ${HEAP_SIZE}MB"
    log_info "新生代大小: ${YOUNG_SIZE}MB"
    log_info "MetaSpace大小: ${METASPACE_SIZE}MB"
    log_info "直接内存大小: ${DIRECT_MEMORY}MB"
}

# 构建JVM参数
build_jvm_args() {
    log_info "构建JVM优化参数..."
    
    JVM_ARGS=""
    
    # 内存配置
    JVM_ARGS="$JVM_ARGS -Xms${HEAP_SIZE}m"
    JVM_ARGS="$JVM_ARGS -Xmx${HEAP_SIZE}m"
    JVM_ARGS="$JVM_ARGS -Xmn${YOUNG_SIZE}m"
    JVM_ARGS="$JVM_ARGS -XX:MetaspaceSize=${METASPACE_SIZE}m"
    JVM_ARGS="$JVM_ARGS -XX:MaxMetaspaceSize=$((METASPACE_SIZE * 2))m"
    JVM_ARGS="$JVM_ARGS -XX:MaxDirectMemorySize=${DIRECT_MEMORY}m"
    
    # 垃圾收集器配置 - 使用G1GC
    JVM_ARGS="$JVM_ARGS -XX:+UseG1GC"
    JVM_ARGS="$JVM_ARGS -XX:MaxGCPauseMillis=100"
    JVM_ARGS="$JVM_ARGS -XX:G1HeapRegionSize=16m"
    JVM_ARGS="$JVM_ARGS -XX:G1NewSizePercent=30"
    JVM_ARGS="$JVM_ARGS -XX:G1MaxNewSizePercent=40"
    JVM_ARGS="$JVM_ARGS -XX:G1MixedGCCountTarget=8"
    JVM_ARGS="$JVM_ARGS -XX:InitiatingHeapOccupancyPercent=45"
    
    # 性能优化
    JVM_ARGS="$JVM_ARGS -XX:+UnlockExperimentalVMOptions"
    JVM_ARGS="$JVM_ARGS -XX:+UseStringDeduplication"
    JVM_ARGS="$JVM_ARGS -XX:+OptimizeStringConcat"
    JVM_ARGS="$JVM_ARGS -XX:+UseCompressedOops"
    JVM_ARGS="$JVM_ARGS -XX:+UseCompressedClassPointers"
    
    # 编译优化
    JVM_ARGS="$JVM_ARGS -XX:+TieredCompilation"
    JVM_ARGS="$JVM_ARGS -XX:TieredStopAtLevel=4"
    JVM_ARGS="$JVM_ARGS -XX:CompileThreshold=1000"
    
    # 线程优化
    JVM_ARGS="$JVM_ARGS -XX:+UseBiasedLocking"
    JVM_ARGS="$JVM_ARGS -XX:BiasedLockingStartupDelay=0"
    
    # 系统属性
    JVM_ARGS="$JVM_ARGS -Djava.awt.headless=true"
    JVM_ARGS="$JVM_ARGS -Dfile.encoding=UTF-8"
    JVM_ARGS="$JVM_ARGS -Duser.timezone=Asia/Shanghai"
    JVM_ARGS="$JVM_ARGS -Djava.security.egd=file:/dev/./urandom"
    
    # 网络优化
    JVM_ARGS="$JVM_ARGS -Djava.net.preferIPv4Stack=true"
    JVM_ARGS="$JVM_ARGS -Dnetworkaddress.cache.ttl=300"
    
    # 日志配置
    JVM_ARGS="$JVM_ARGS -Xloggc:logs/gc.log"
    JVM_ARGS="$JVM_ARGS -XX:+PrintGCDetails"
    JVM_ARGS="$JVM_ARGS -XX:+PrintGCTimeStamps"
    JVM_ARGS="$JVM_ARGS -XX:+PrintGCDateStamps"
    JVM_ARGS="$JVM_ARGS -XX:+UseGCLogFileRotation"
    JVM_ARGS="$JVM_ARGS -XX:NumberOfGCLogFiles=10"
    JVM_ARGS="$JVM_ARGS -XX:GCLogFileSize=100M"
    
    # 错误处理
    JVM_ARGS="$JVM_ARGS -XX:+HeapDumpOnOutOfMemoryError"
    JVM_ARGS="$JVM_ARGS -XX:HeapDumpPath=logs/heapdump.hprof"
    JVM_ARGS="$JVM_ARGS -XX:ErrorFile=logs/hs_err_pid%p.log"
    
    # JFR配置（Java 11+）
    if [[ "$JAVA_VERSION" > "11" ]]; then
        JVM_ARGS="$JVM_ARGS -XX:+FlightRecorder"
        JVM_ARGS="$JVM_ARGS -XX:StartFlightRecording=duration=60s,filename=logs/flight-recording.jfr"
    fi
    
    log_info "JVM参数构建完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p temp
    mkdir -p data
    
    log_info "目录创建完成"
}

# 启动应用
start_application() {
    log_info "启动水费系统应用..."
    
    # 应用参数
    APP_ARGS=""
    APP_ARGS="$APP_ARGS --spring.profiles.active=ultra-performance"
    APP_ARGS="$APP_ARGS --server.port=8080"
    APP_ARGS="$APP_ARGS --logging.config=classpath:logback-ultra-performance.xml"
    
    # 构建完整启动命令
    JAVA_CMD="java $JVM_ARGS -jar waterfee-application.jar $APP_ARGS"
    
    log_info "启动命令: $JAVA_CMD"
    
    # 启动应用
    if [ "$1" = "background" ]; then
        nohup $JAVA_CMD > logs/application.log 2>&1 &
        APP_PID=$!
        echo $APP_PID > waterfee.pid
        log_info "应用已在后台启动，PID: $APP_PID"
    else
        exec $JAVA_CMD
    fi
}

# 监控系统资源
monitor_resources() {
    log_info "启动资源监控..."
    
    # 创建监控脚本
    cat > monitor.sh << 'EOF'
#!/bin/bash
while true; do
    echo "$(date '+%Y-%m-%d %H:%M:%S') - CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%, MEM: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}'), DISK: $(df -h / | awk 'NR==2 {print $5}')"
    sleep 30
done
EOF
    
    chmod +x monitor.sh
    
    if [ "$1" = "background" ]; then
        nohup ./monitor.sh > logs/resource-monitor.log 2>&1 &
        echo $! > monitor.pid
        log_info "资源监控已启动"
    fi
}

# 性能调优建议
performance_tips() {
    log_info "性能调优建议:"
    echo "1. 定期监控GC日志: tail -f logs/gc.log"
    echo "2. 监控应用日志: tail -f logs/application.log"
    echo "3. 查看JVM状态: jstat -gc \$(cat waterfee.pid) 5s"
    echo "4. 生成线程转储: jstack \$(cat waterfee.pid) > logs/thread-dump.txt"
    echo "5. 生成堆转储: jmap -dump:format=b,file=logs/heap-dump.hprof \$(cat waterfee.pid)"
    echo "6. 监控端点: http://localhost:8080/actuator/metrics"
    echo "7. Prometheus指标: http://localhost:8080/actuator/prometheus"
}

# 主函数
main() {
    log_info "开始水费系统极致性能优化启动..."
    
    # 检查参数
    MODE=${1:-"foreground"}
    
    # 执行优化步骤
    check_system
    optimize_system
    calculate_jvm_params
    build_jvm_args
    create_directories
    
    # 启动应用
    start_application $MODE
    
    # 启动监控
    if [ "$MODE" = "background" ]; then
        monitor_resources $MODE
        performance_tips
        log_info "应用启动完成，运行在后台模式"
    fi
}

# 信号处理
cleanup() {
    log_info "正在关闭应用..."
    
    if [ -f waterfee.pid ]; then
        PID=$(cat waterfee.pid)
        kill -TERM $PID
        rm -f waterfee.pid
    fi
    
    if [ -f monitor.pid ]; then
        PID=$(cat monitor.pid)
        kill -TERM $PID
        rm -f monitor.pid
    fi
    
    log_info "应用已关闭"
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

# 显示帮助信息
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "水费系统极致性能启动脚本"
    echo ""
    echo "用法: $0 [模式]"
    echo ""
    echo "模式:"
    echo "  foreground  前台运行（默认）"
    echo "  background  后台运行"
    echo ""
    echo "示例:"
    echo "  $0                    # 前台运行"
    echo "  $0 background         # 后台运行"
    echo "  $0 --help            # 显示帮助"
    exit 0
fi

# 执行主函数
main $1
