import type { ${BusinessName}VO, ${BusinessName}Form, ${BusinessName}Query } from '#/api/${moduleName}/${businessName}/model.d';
import type { ID, IDS, PageQuery } from '#/api/common';
import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

enum Api {
  root = '/${moduleName}/${businessName}',
  list = '/${moduleName}/${businessName}/list'
}

/**
 * ${functionName}导出
 * @param data data
 * @returns void
 */
export function ${BusinessName}Export(data: Partial<${BusinessName}Form>) {
  return commonExport(`${Api.root}/export`, data);
}

/**
 * 查询${functionName}列表
 * @param params 查询参数
 * @returns ${functionName}列表
 */
export function list${BusinessName}(params?: ${BusinessName}Query & PageQuery) {
  return requestClient.get<${BusinessName}VO>(Api.list, { params });
}

/**
 * 查询${functionName}详细
 * @param ${pkColumn.javaField} ${functionName}ID
 * @returns ${functionName}信息
 */
export function get${BusinessName}(${pkColumn.javaField}: string | number) {
  return requestClient.get<${BusinessName}Form>(`${Api.root}/${${pkColumn.javaField}}`);
}

/**
 * 新增${functionName}
 * @param data 新增数据
 * @returns void
 */
export function add${BusinessName}(data: ${BusinessName}Form) {
  return requestClient.postWithMsg<void>(Api.root, data);
}

/**
 * 修改${functionName}
 * @param data 修改数据
 * @returns void
 */
export function update${BusinessName}(data: ${BusinessName}Form) {
  return requestClient.putWithMsg<void>(Api.root, data);
}

/**
 * 删除${functionName}
 * @param ${pkColumn.javaField} ${functionName}ID或ID数组
 * @returns void
 */
export function del${BusinessName}(${pkColumn.javaField}: string | number | Array<string | number>) {
  return requestClient.deleteWithMsg<void>(`${Api.root}/${${pkColumn.javaField}}`);
}
