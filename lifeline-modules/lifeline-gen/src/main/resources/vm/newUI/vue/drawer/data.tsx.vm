import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';
import { getPopupContainer } from '@vben/utils';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
#foreach($column in $columns)
#if($column.query)
#set($dictType=$column.dictType)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.htmlType == "input" || $column.htmlType == "textarea")
  {
    component: 'Input',
    fieldName: '${column.javaField}',
    label: '${comment}',
  },
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
  {
    component: 'Select',
    componentProps: {
      getPopupContainer,
      options: getDictOptions('${dictType}'),
    },
    fieldName: '${column.javaField}',
    label: '${comment}',
  },
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
  {
    component: 'Select',
    fieldName: '${column.javaField}',
    label: '${comment}',
  },
#elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
  {
    component: 'DatePicker',
    fieldName: '${column.javaField}',
    label: '${comment}',
  },
#elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
  {
    component: 'RangePicker',
    fieldName: '${column.javaField}',
    label: '${comment}',
  },
#end
#end
#end
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
#foreach($column in $columns)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.pk)
  {
    title: '${comment}',
    field: '${javaField}',
  },
#elseif($column.list && $column.htmlType == "datetime")
  {
    title: '${comment}',
    field: '${javaField}',
  },
#elseif($column.list && $column.htmlType == "imageUpload")
  {
    title: '${comment}',
    field: '${javaField}',
    slots: {
      default: ({ row }) => {
        return <img src={row.${javaField}Url} style="width: 50px; height: 50px" />;
      },
    },
  },
#elseif($column.list && $column.dictType && "" != $column.dictType)
  {
    title: '${comment}',
    field: '${javaField}',
    slots: {
      default: ({ row }) => {
        return renderDict(row.${javaField}, '${column.dictType}');
      },
    },
  },
#elseif($column.list && "" != $javaField)
  {
    title: '${comment}',
    field: '${javaField}',
  },
#end
#end
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: '${pkColumn.javaField}',
    label: '主键',
  },
#foreach($column in $columns)
#if(($column.insert || $column.edit) && !$column.pk)
#set($field=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#set($dictType=$column.dictType)
#if($column.htmlType == "input")
  {
    component: 'Input',
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "imageUpload")
  {
    component: 'Upload',
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "fileUpload")
  {
    component: 'Upload',
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "editor")
  {
    component: 'RichTextarea',
    componentProps: {
      width: '100%',
    },
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "select" && "" != $dictType)
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('${dictType}'),
    },
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "select" && $dictType)
  {
    component: 'Select',
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "checkbox" && "" != $dictType)
  {
    component: 'CheckboxGroup',
    componentProps: {
      options: getDictOptions('${dictType}'),
    },
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "checkbox" && $dictType)
  {
    component: 'CheckboxGroup',
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "radio" && "" != $dictType)
  {
    component: 'RadioGroup',
    componentProps: {
      buttonStyle: 'solid',
      options: getDictOptions('${dictType}'),
      optionType: 'button',
    },
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "radio" && $dictType)
  {
    component: 'RadioGroup',
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "datetime")
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#elseif($column.htmlType == "textarea")
  {
    component: 'Textarea',
    fieldName: '${field}',
    label: '${comment}',
    #if($column.required)rules: 'required',#end
  },
#end
#end
#end
];
