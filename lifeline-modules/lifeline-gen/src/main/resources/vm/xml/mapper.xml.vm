<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${packageName}.mapper.${ClassName}Mapper">
#set($result = ${ClassName} + "Result")
    <resultMap id="$result" autoMapping="true" type="${packageName}.domain.${ClassName}">
#foreach ($column in $columns)
#if($column.isPk())
        <id property="${column.javaField}" column="${column.columnName}"/>
#end
#end
    </resultMap>

    <resultMap id="${ClassName}ResultVo" autoMapping="true" type="${packageName}.domain.vo.${ClassName}Vo">
#foreach ($column in $columns)
#if($column.isPk())
        <id property="${column.javaField}" column="${column.columnName}"/>
#end
#end
    </resultMap>

    <sql id="select${ClassName}Vo">
        select#foreach($column in $columns) ${dbName}.$column.columnName#if($foreach.hasNext),#end#end from $tableName $dbName
    </sql>
    <select id="queryList" resultMap="${ClassName}ResultVo">
        <include refid="select${ClassName}Vo"/>
        <where>
#set($string = 'String')
#foreach($column in $columns)
#set($queryType=$column.queryType)
#set($javaField="query." + $column.javaField)
#set($javaType=$column.javaType)
#set($columnName=$column.columnName)
#if($columnName == 'del_flag')
            and $dbName.$columnName =#if($javaType == $string) '0'#else 0#end
#end
#if($column.isQuery())
#if($column.queryType == "EQ")
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName = #{$javaField}</if>
#elseif($queryType == "NE")
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName != #{$javaField}</if>
#elseif($queryType == "GT")
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName &gt; #{$javaField}</if>
#elseif($queryType == "GE")
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName &gt;= #{$javaField}</if>
#elseif($queryType == "LT")
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName &lt; #{$javaField}</if>
#elseif($queryType == "LE")
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName &lt;= #{$javaField}</if>
#elseif($queryType.equalsIgnoreCase("LIKE"))
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName like concat(concat('%', #{$javaField}), '%')</if>
#elseif($queryType.equalsIgnoreCase("LEFT_LIKE"))
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName like concat('%', #{$javaField})</if>
#elseif($queryType.equalsIgnoreCase("RIGHT_LIKE"))
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName like concat(#{$javaField}, '%')</if>
#elseif($queryType.equalsIgnoreCase("NOT_LIKE"))
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName not like concat(concat('%', #{$javaField}), '%')</if>
#elseif($queryType.equalsIgnoreCase("IS_NULL"))
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName is null</if>
#elseif($queryType.equalsIgnoreCase("IS_NOT_NULL"))
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end"> and $dbName.$columnName is not null</if>
#elseif($queryType.equalsIgnoreCase("IN") && $javaType == $string)
            <if test="$javaField != null#if($javaType == $string) and $javaField.trim() != ''#end">
                <foreach collection="${javaField}.split(',')" index="index" item="item" open="and $dbName.$columnName in (" separator="," close=")" nullable="true">
                    #{item}
                </foreach>
            </if>
#elseif($queryType.equalsIgnoreCase("IN"))
            <foreach collection="$javaField" index="index" item="item" open="and $dbName.$columnName in (" separator="," close=")" nullable="true">
                #{item}
            </foreach>
#elseif($queryType.equalsIgnoreCase("BETWEEN"))
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            <if test="params.begin$AttrName != null and params.end$AttrName != null">and $dbName.$columnName between #{params.begin${AttrName}} and #{params.end${AttrName}}</if>
#end
#end
#end
        </where>
    </select>
</mapper>
