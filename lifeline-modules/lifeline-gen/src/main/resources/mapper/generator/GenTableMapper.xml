<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.gen.mapper.GenTableMapper">

    <!-- 多结构嵌套自动映射需带上每个实体的主键id 否则映射会失败 -->
    <resultMap type="org.dromara.gen.domain.GenTable" id="GenTableResult">
        <id property="tableId" column="table_id" />
        <collection property="columns" javaType="java.util.List" resultMap="GenTableColumnResult" />
    </resultMap>

    <resultMap type="org.dromara.gen.domain.GenTableColumn" id="GenTableColumnResult">
        <id property="columnId" column="column_id"/>
    </resultMap>

    <sql id="genSelect">
        SELECT t.table_id, t.data_name, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,
               c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort
        FROM gen_table t
            LEFT JOIN gen_table_column c ON t.table_id = c.table_id
    </sql>

    <select id="selectGenTableById" parameterType="Long" resultMap="GenTableResult">
        <include refid="genSelect"/>
        where t.table_id = #{tableId} order by c.sort
    </select>

    <select id="selectGenTableByName" parameterType="String" resultMap="GenTableResult">
        <include refid="genSelect"/>
        where t.table_name = #{tableName} order by c.sort
    </select>

    <select id="selectGenTableAll" parameterType="String" resultMap="GenTableResult">
        <include refid="genSelect"/>
        order by c.sort
    </select>

    <select id="selectTableNameList" resultType="java.lang.String">
        select table_name from gen_table where data_name = #{dataName,jdbcType=VARCHAR}
    </select>
</mapper>
