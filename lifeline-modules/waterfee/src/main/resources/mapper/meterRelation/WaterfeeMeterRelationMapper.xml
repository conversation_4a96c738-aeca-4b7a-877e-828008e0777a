<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterRelationMapper">

    <!-- 查询某个水表的子表ID -->
    <select id="selectChildMeterIds" resultType="java.lang.Long">
        SELECT child_meter_id
        FROM waterfee_meter_relation
        WHERE parent_meter_id = #{meterId}
        AND del_flag = '0'
    </select>

    <!-- 查询某个水表的父表ID -->
    <select id="selectParentMeterIds" resultType="java.lang.Long">
        SELECT parent_meter_id
        FROM waterfee_meter_relation
        WHERE child_meter_id = #{meterId}
        AND del_flag = '0'
    </select>

    <!-- 查询水表信息（用于构建树） -->
    <select id="selectMeterById" resultType="org.dromara.waterfee.meter.domain.WaterfeeMeter">
        SELECT *
        FROM waterfee_meter
        WHERE meter_id = #{id}
        AND del_flag = '0'
    </select>

    <!-- 查询所有未删除的水表关系 -->
    <select id="selectAllRelations" resultType="org.dromara.waterfee.meterRelation.domain.WaterfeeMeterRelation">
        SELECT *
        FROM waterfee_meter_relation
        WHERE del_flag = '0'
    </select>

</mapper>
