<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meter.mapper.IntelligentMeterMapper">

    <resultMap type="org.dromara.waterfee.meter.domain.vo.IntelligentMeterVo" id="IntelligentMeterVoResult">
        <id property="meterId" column="meter_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterName" column="meter_name"/>
        <result property="meterType" column="meter_type"/>
        <result property="caliber" column="caliber"/>
        <result property="status" column="status"/>
        <result property="installAddress" column="install_address"/>
        <result property="installTime" column="install_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="org.dromara.waterfee.meter.domain.vo.MeterChangeRecordVo" id="MeterChangeRecordResult">
        <result property="changeReason" column="change_reason"/>
        <result property="changeTime" column="change_time"/>
        <result property="newReading" column="new_reading"/>
        <result property="oldMeterReading" column="old_meter_reading"/>
    </resultMap>


    <sql id="selectIntelligentMeterVo">
        select meter_id, meter_no, meter_name, meter_type, caliber, install_address, install_time, status, remark
        from waterfee_intelligent_meter
    </sql>

    <select id="selectVoList" resultMap="IntelligentMeterVoResult">
        select meter_id, meter_no, meter_name, meter_type, caliber, status, install_address, install_time, remark
        from waterfee_meter
        where meter_type = 2
        <if test="meterNo != null and meterNo != ''">
            and meter_no like concat('%', #{meterNo}, '%')
        </if>
        <if test="meterName != null and meterName != ''">
            and meter_name like concat('%', #{meterName}, '%')
        </if>
        <if test="caliber != null and caliber != ''">
            and caliber = #{caliber}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        order by create_time desc
    </select>

    <select id="selectLastChangeRecord" parameterType="Long" resultMap="MeterChangeRecordResult">
        select change_reason, change_time, new_reading, old_meter_reading
        from waterfee_meter_change_record
        where meter_id = #{meterId}
        order by change_time desc
        limit 1
    </select>


</mapper>
