<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.dromara.waterfee.meter.mapper.WaterfeeMeterAlertMapper">

    <!-- WaterfeeMeterAlert ResultMap -->
    <resultMap type="org.dromara.waterfee.meter.domain.WaterfeeMeterAlert" id="WaterfeeMeterAlertResult">
        <id property="alertId" column="alert_id"/>
        <result property="meterId" column="meter_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="userId" column="user_id"/>
        <result property="businessAreaId" column="business_area_id"/>
        <result property="alertType" column="alert_type"/>
        <result property="alertContent" column="alert_content"/>
        <result property="alertLevel" column="alert_level"/>
        <result property="alertStatus" column="alert_status"/>
        <result property="alertTime" column="alert_time"/>
        <result property="processTime" column="process_time"/>
        <result property="processUser" column="process_user"/>
        <result property="processResult" column="process_result"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- WaterfeeMeterAlertVo ResultMap -->
    <resultMap type="org.dromara.waterfee.meter.domain.vo.WaterfeeMeterAlertVo" id="WaterfeeMeterAlertVoResult"
               extends="WaterfeeMeterAlertResult">
        <result property="userName" column="user_name"/>
        <result property="userNo" column="user_no"/>
        <result property="businessAreaName" column="business_area_name"/>
        <result property="waterAddress" column="water_address"/>
        <result property="caliber" column="caliber"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="communicationMode" column="communication_mode"/>
    </resultMap>

    <!-- 公共SQL片段 -->
    <sql id="selectWaterfeeMeterAlertVo">
        SELECT
        a.alert_id, a.meter_id, a.meter_no, a.user_id, a.business_area_id, a.alert_type,
        a.alert_content, a.alert_level, a.alert_status, a.alert_time, a.process_time,
        a.process_user, a.process_result, a.remark, a.tenant_id,
        a.create_time, a.update_time, a.del_flag,
        u.user_name, u.user_no, ba.area_name AS business_area_name,
        m.install_address AS water_address, m.caliber,
        m.manufacturer, m.communication_mode
        FROM waterfee_meter_alert a
        LEFT JOIN waterfee_user u ON a.user_id = u.user_id
        LEFT JOIN waterfee_area ba ON a.business_area_id = ba.area_id
        LEFT JOIN waterfee_meter m ON a.meter_id = m.meter_id
        WHERE a.del_flag = '0'
    </sql>

    <!-- 根据ID查询告警信息 -->
    <select id="selectAlertById" parameterType="java.lang.Long" resultMap="WaterfeeMeterAlertResult">
        SELECT
        alert_id, meter_id, meter_no, user_id, business_area_id, alert_type,
        alert_content, alert_level, alert_status, alert_time, process_time,
        process_user, process_result, remark, tenant_id, create_time, update_time, del_flag
        FROM
        waterfee_meter_alert
        WHERE
        alert_id = #{alertId}
        AND del_flag = '0'
    </select>

    <!-- 根据ID查询告警Vo信息 -->
    <select id="selectAlertVoById" parameterType="Long" resultMap="WaterfeeMeterAlertVoResult">
        <include refid="selectWaterfeeMeterAlertVo"/>
        AND a.alert_id = #{alertId}
    </select>

    <!-- 查询告警Vo列表 -->
    <select id="selectAlertVoList" resultMap="WaterfeeMeterAlertVoResult">
        <include refid="selectWaterfeeMeterAlertVo"/>
        <choose>
            <when test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
                ${ew.customSqlSegment}
            </when>
            <otherwise>
                <if test="_parameter.containsKey('tenantId')">
                    AND a.tenant_id = #{tenantId}
                </if>
                ORDER BY a.alert_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询告警Vo列表 -->
    <select id="selectAlertVoPage" resultMap="WaterfeeMeterAlertVoResult">
        <include refid="selectWaterfeeMeterAlertVo"/>
        <choose>
            <when test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
                ${ew.customSqlSegment}
            </when>
            <otherwise>
                <if test="_parameter.containsKey('tenantId')">
                    AND a.tenant_id = #{tenantId}
                </if>
                ORDER BY a.alert_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
