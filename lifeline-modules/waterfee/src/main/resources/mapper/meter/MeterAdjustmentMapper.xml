<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meter.mapper.MeterAdjustmentMapper">

    <resultMap type="org.dromara.waterfee.meter.domain.WaterfeeMeter" id="WaterfeeMeterResult">
        <id property="meterId" column="meter_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterType" column="meter_type"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="meterCategory" column="meter_category"/>
        <result property="meterClassification" column="meter_classification"/>
        <result property="measurementPurpose" column="measurement_purpose"/>
        <result property="caliber" column="caliber"/>
        <result property="accuracy" column="accuracy"/>
        <result property="initialReading" column="initial_reading"/>
        <result property="installDate" column="install_date"/>
        <result property="businessAreaId" column="business_area_id"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="sortNo" column="sort_no"/>
        <result property="installAddress" column="install_address"/>
        <result property="meterRatio" column="meter_ratio"/>
        <result property="communicationMode" column="communication_mode"/>
        <result property="valveControl" column="valve_control"/>
        <result property="imei" column="imei"/>
        <result property="imsi" column="imsi"/>
        <result property="iotPlatform" column="iot_platform"/>
        <result property="prepaid" column="prepaid"/>
        <result property="userId" column="user_id"/>
        <result property="userNo" column="user_no"/>
        <result property="waterNature" column="water_nature"/>
        <result property="priceName" column="price_name"/>
        <result property="penaltyEnabled" column="penalty_enabled"/>
        <result property="penaltyType" column="penalty_type"/>
        <result property="penaltyValue" column="penalty_value"/>
        <result property="additionalFeesEnabled" column="additional_fees_enabled"/>
        <result property="additionalFeesItems" column="additional_fees_items"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <!-- 批量更新水表所属表册和区域 -->
    <update id="updateMeterBook">
        UPDATE waterfee_meter
        SET meter_book_id = #{targetBookId},
        business_area_id = #{targetAreaId},
        update_time = SYSDATE()
        WHERE meter_id IN
        <foreach collection="meterIds" item="meterId" open="(" separator="," close=")">
            #{meterId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 批量更新水表排序号 - 修改为使用CASE WHEN语法 -->
    <update id="updateMeterSort">
        UPDATE waterfee_meter
        <set>
            sort_no =
            <foreach collection="meterSortItems" item="item" separator=" " open="CASE meter_id " close=" END">
                WHEN #{item.meterId} THEN #{item.sortNo}
            </foreach>,
            update_time = SYSDATE()
        </set>
        WHERE meter_id IN
        <foreach collection="meterSortItems" item="item" open="(" separator="," close=")">
            #{item.meterId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 获取表册中的最大排序号 -->
    <select id="getMaxSortNo" resultType="java.lang.Integer">
        SELECT MAX(sort_no)
        FROM waterfee_meter
        WHERE meter_book_id = #{bookId}
        AND del_flag = '0'
    </select>

    <!-- 查询表册中的水表列表（按排序号排序） -->
    <select id="selectMetersByBookId" resultMap="WaterfeeMeterResult">
        SELECT *
        FROM waterfee_meter
        WHERE meter_book_id = #{bookId}
        AND del_flag = '0'
        ORDER BY sort_no ASC, meter_id ASC
    </select>
</mapper>
