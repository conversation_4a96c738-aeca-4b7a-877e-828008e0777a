<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterReading.mapper.WaterfeeMeterReadingManualMapper">

    <resultMap type="org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingManual"
               id="WaterfeeMeterReadingManualResult">
        <result property="manualId" column="manual_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="currentReading" column="current_reading"/>
        <result property="readingTime" column="reading_time"/>
        <result property="reason" column="reason"/>
        <result property="operator" column="operator"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="meterBookId" column="meter_book_id"/>
    </resultMap>

    <resultMap type="org.dromara.waterfee.meterReading.domain.vo.WaterfeeMeterReadingManualVo"
               id="WaterfeeMeterReadingManualVoResult">
        <result property="manualId" column="manual_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="userNo" column="user_no"/>
        <result property="userName" column="user_name"/>
        <result property="businessAreaName" column="business_area_name"/>
        <result property="meterBookName" column="meter_book_name"/>
        <result property="lastReading" column="last_reading"/>
        <result property="lastReadingTime" column="last_reading_time"/>
        <result property="currentReading" column="current_reading"/>
        <result property="oldMeterStopReading" column="old_meter_stop_reading"/>
        <result property="waterUsage" column="water_usage"/>
        <result property="readingTime" column="reading_time"/>
        <result property="reason" column="reason"/>
        <result property="operator" column="operator"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 查询抄表补录列表，包含关联信息 -->
    <select id="selectManualListWithRelated" resultMap="WaterfeeMeterReadingManualVoResult">
        SELECT
        m.manual_id,
        m.meter_no,
        u.user_no,
        u.user_name,
        ba.area_name AS business_area_name,
        mb.book_name AS meter_book_name,
        r.last_reading,
        r.last_reading_time,
        m.current_reading,
        r.old_meter_stop_reading,
        r.water_usage,
        m.reading_time,
        m.reason,
        m.operator,
        m.create_time
        FROM
        waterfee_meter_reading_manual m
        LEFT JOIN
        waterfee_meter_reading_record r ON m.manual_id = r.manual_id AND r.source_type = 'manual'
        LEFT JOIN
        waterfee_meter wm ON m.meter_no = wm.meter_no
        LEFT JOIN
        waterfee_user u ON wm.user_id = u.user_id
        LEFT JOIN
        waterfee_meter_book mb ON wm.meter_book_id = mb.id
        LEFT JOIN
        waterfee_area ba ON mb.area_id = ba.area_id
        WHERE
        m.del_flag = '0'
        <if test="meterNo != null and meterNo != ''">
            AND m.meter_no = #{meterNo}
        </if>
        <if test="beginTime != null and endTime != null">
            AND m.reading_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime != null and endTime == null">
            AND m.reading_time >= #{beginTime}
        </if>
        <if test="beginTime == null and endTime != null">
            AND m.reading_time &lt;= #{endTime}
        </if>
        ORDER BY m.create_time DESC
    </select>

    <!-- 查询抄表补录详细信息，包含关联信息 -->
    <select id="selectManualDetailWithRelated" resultMap="WaterfeeMeterReadingManualVoResult">
        SELECT
        m.manual_id,
        m.meter_no,
        u.user_no,
        u.user_name,
        ba.area_name AS business_area_name,
        mb.book_name AS meter_book_name,
        r.last_reading,
        r.last_reading_time,
        m.current_reading,
        r.old_meter_stop_reading,
        r.water_usage,
        m.reading_time,
        m.reason,
        m.operator,
        m.create_time
        FROM
        waterfee_meter_reading_manual m
        LEFT JOIN
        waterfee_meter_reading_record r ON m.manual_id = r.manual_id AND r.source_type = 'manual'
        LEFT JOIN
        waterfee_meter wm ON m.meter_no = wm.meter_no
        LEFT JOIN
        waterfee_user u ON wm.user_id = u.user_id
        LEFT JOIN
        waterfee_meter_book mb ON wm.meter_book_id = mb.id
        LEFT JOIN
        waterfee_area ba ON mb.area_id = ba.area_id
        WHERE
        m.manual_id = #{manualId}
        AND m.del_flag = '0'
    </select>
    <!-- 分页查询抄表补录列表，包含关联信息 -->
    <select id="selectVoPage" resultMap="WaterfeeMeterReadingManualVoResult">
        SELECT
        m.manual_id,
        m.meter_no,
        u.user_no,
        u.user_name,
        ba.area_name AS business_area_name,
        mb.book_name AS meter_book_name,
        r.last_reading,
        r.last_reading_time,
        m.current_reading,
        r.old_meter_stop_reading,
        r.water_usage,
        m.reading_time,
        m.reason,
        m.operator,
        m.create_time
        FROM
        waterfee_meter_reading_manual m
        LEFT JOIN
        waterfee_meter_reading_record r ON m.manual_id = r.manual_id AND r.source_type = 'manual'
        LEFT JOIN
        waterfee_meter wm ON m.meter_no = wm.meter_no
        LEFT JOIN
        waterfee_user u ON wm.user_id = u.user_id
        LEFT JOIN
        waterfee_meter_book mb ON wm.meter_book_id = mb.id
        LEFT JOIN
        waterfee_area ba ON mb.area_id = ba.area_id
        ${ew.customSqlSegment}
    </select>
</mapper>
