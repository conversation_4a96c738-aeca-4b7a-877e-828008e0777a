<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterReading.mapper.WaterfeeReadingTaskMapper">

    <resultMap type="org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask" id="WaterfeeReadingTaskResult">
        <id property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="businessAreaId" column="business_area_id"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="readerId" column="reader_id"/>
        <result property="readerName" column="reader_name"/>
        <result property="readingMethod" column="reading_method"/>
        <result property="readingCycle" column="reading_cycle"/>
        <result property="readingDay" column="reading_day"/>
        <result property="baseDay" column="base_day"/>
        <result property="isCycle" column="is_cycle"/>
        <result property="taskStatus" column="task_status"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="lastExecuteTime" column="last_execute_time"/>
        <result property="nextExecuteTime" column="next_execute_time"/>
        <result property="bookUserNum" column="book_user_num"/>
        <result property="planReadingNum" column="plan_reading_num"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="isAudited" column="is_audited"/>
        <result property="actualReadingNum" column="actual_reading_num"/>
        <result property="readingMonth" column="reading_month"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditorName" column="auditor_name"/>
    </resultMap>

    <resultMap type="org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo" id="WaterfeeReadingTaskVoResult"
               extends="WaterfeeReadingTaskResult">
        <result property="businessAreaName" column="business_area_name"/>
        <result property="meterBookName" column="meter_book_name"/>
    </resultMap>

    <sql id="selectWaterfeeReadingTaskVo">
        select t.task_id, t.task_name, t.business_area_id, t.meter_book_id, t.reader_id, t.reader_name,
        t.reading_method, t.reading_cycle, t.reading_day, t.base_day, t.is_cycle, t.task_status,
        t.start_date, t.end_date, t.last_execute_time, t.next_execute_time, t.remark, t.tenant_id,
        t.create_time, t.update_time, t.del_flag,
        a.area_name as business_area_name, b.book_name as meter_book_name
        from waterfee_meter_reading_task t
        left join waterfee_area a on t.business_area_id = a.area_id
        left join waterfee_meter_book b on t.meter_book_id = b.id
    </sql>

    <select id="selectTaskVoById" parameterType="Long" resultMap="WaterfeeReadingTaskVoResult">
        select t.task_id, t.task_name, t.business_area_id, t.meter_book_id, t.reader_id, t.reader_name,
        t.reading_method, t.reading_cycle, t.reading_day, t.base_day, t.is_cycle, t.task_status,
        t.start_date, t.end_date, t.last_execute_time, t.next_execute_time, t.book_user_num, t.plan_reading_num,
        t.remark, t.tenant_id, t.create_time, t.update_time, t.del_flag,
        a.area_name as business_area_name, b.book_name as meter_book_name
        from waterfee_meter_reading_task t
        left join waterfee_area a on t.business_area_id = a.area_id
        left join waterfee_meter_book b on t.meter_book_id = b.id
        where t.del_flag = '0' and t.task_id = #{taskId}
    </select>

    <select id="selectTaskVoList" resultMap="WaterfeeReadingTaskVoResult">
        select t.task_id, t.task_name, t.business_area_id, t.meter_book_id, t.reader_id, t.reader_name,
        t.reading_method, t.reading_cycle, t.reading_day, t.base_day, t.is_cycle, t.task_status,
        t.start_date, t.end_date, t.last_execute_time, t.next_execute_time, t.book_user_num, t.plan_reading_num,
        t.remark, t.tenant_id, t.create_time, t.update_time, t.del_flag,
        a.area_name as business_area_name, b.book_name as meter_book_name
        from waterfee_meter_reading_task t
        left join waterfee_area a on t.business_area_id = a.area_id
        left join waterfee_meter_book b on t.meter_book_id = b.id
        ${ew.customSqlSegment}
        order by t.create_time desc
    </select>

    <select id="selectTaskVoPage" resultMap="WaterfeeReadingTaskVoResult">
        select t.task_id, t.task_name, t.business_area_id, t.meter_book_id, t.reader_id, t.reader_name,
        t.reading_method, t.reading_cycle, t.reading_day, t.base_day, t.is_cycle, t.task_status,
        t.start_date, t.end_date, t.last_execute_time, t.next_execute_time, t.book_user_num, t.plan_reading_num,
        t.remark, t.tenant_id, t.create_time, t.update_time, t.del_flag, t.is_audited,t.actual_reading_num,
        t.reading_month, t.audit_time, t.auditor_name,
        a.area_name as business_area_name, b.book_name as meter_book_name
        from waterfee_meter_reading_task t
        left join waterfee_area a on t.business_area_id = a.area_id
        left join waterfee_meter_book b on t.meter_book_id = b.id
        ${ew.customSqlSegment}
        order by t.create_time desc
    </select>

    <select id="selectTaskById" resultMap="WaterfeeReadingTaskResult">
        select t.task_id, t.task_name, t.business_area_id, t.meter_book_id, t.reader_id, t.reader_name,
        t.reading_method, t.reading_cycle, t.reading_day, t.base_day, t.is_cycle, t.task_status,
        t.start_date, t.end_date, t.last_execute_time, t.next_execute_time, t.book_user_num, t.plan_reading_num,
        t.remark, t.tenant_id, t.create_time, t.update_time, t.del_flag, t.is_audited,t.actual_reading_num,
        t.reading_month, t.audit_time, t.auditor_name,
        a.area_name as business_area_name, b.book_name as meter_book_name
        from waterfee_meter_reading_task t
        left join waterfee_area a on t.business_area_id = a.area_id
        left join waterfee_meter_book b on t.meter_book_id = b.id
        where t.task_id = #{taskId}
        order by t.create_time desc
    </select>

</mapper>
