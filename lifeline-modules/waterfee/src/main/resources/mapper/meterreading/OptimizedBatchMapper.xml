<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterReading.mapper.OptimizedBatchMapper">

    <!-- 抄表记录结果映射 -->
    <resultMap id="MeterReadingRecordResult" type="org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord">
        <id property="recordId" column="record_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="meterType" column="meter_type"/>
        <result property="taskId" column="task_id"/>
        <result property="sourceType" column="source_type"/>
        <result property="readingTime" column="reading_time"/>
        <result property="lastReadingTime" column="last_reading_time"/>
        <result property="currentReading" column="current_reading"/>
        <result property="lastReading" column="last_reading"/>
        <result property="waterUsage" column="water_usage"/>
        <result property="isAudited" column="is_audited"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 水表信息结果映射 -->
    <resultMap id="WaterfeeMeterResult" type="org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo">
        <id property="meterId" column="meter_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="meterType" column="meter_type"/>
        <result property="initialReading" column="initial_reading"/>
        <result property="installDate" column="install_date"/>
        <result property="userNo" column="user_no"/>
    </resultMap>

    <!-- 账单信息结果映射 -->
    <resultMap id="WaterfeeBillResult" type="org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo">
        <id property="billId" column="bill_id"/>
        <result property="billNumber" column="bill_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="userNo" column="user_no"/>
        <result property="userName" column="user_name"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="billStatus" column="bill_status"/>
        <result property="consumptionVolume" column="consumption_volume"/>
        <result property="billingPeriodStart" column="billing_period_start"/>
        <result property="billingPeriodEnd" column="billing_period_end"/>
    </resultMap>

    <!-- 批量插入抄表记录 -->
    <insert id="batchInsertReadingRecords" parameterType="java.util.List">
        INSERT INTO waterfee_meter_reading_record (
        meter_no, meter_book_id, meter_type, task_id, source_type,
        reading_time, last_reading_time, current_reading, last_reading,
        water_usage, is_audited, create_time, del_flag
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
            #{record.meterNo},
            #{record.meterBookId},
            #{record.meterType},
            #{record.taskId},
            #{record.sourceType},
            #{record.readingTime},
            #{record.lastReadingTime},
            #{record.currentReading},
            #{record.lastReading},
            #{record.waterUsage},
            #{record.isAudited},
            NOW(),
            '0'
            )
        </foreach>
    </insert>

    <!-- 批量查询水表信息 -->
    <select id="batchSelectMetersByNos" resultMap="WaterfeeMeterResult">
        SELECT
        meter_id, meter_no, meter_book_id, meter_type,
        initial_reading, install_date, user_no, status
        FROM waterfee_meter
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND del_flag = '0'
        ORDER BY meter_no
    </select>

    <!-- 批量查询最新抄表记录 -->
    <select id="batchSelectLatestRecords" resultMap="MeterReadingRecordResult">
        SELECT r1.*
        FROM waterfee_meter_reading_record r1
        INNER JOIN (
        SELECT meter_no, MAX(reading_time) as max_time, MAX(record_id) as max_id
        FROM waterfee_meter_reading_record
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND del_flag = '0'
        GROUP BY meter_no
        ) r2 ON r1.meter_no = r2.meter_no
        AND r1.reading_time = r2.max_time
        AND r1.record_id = r2.max_id
        WHERE r1.del_flag = '0'
        ORDER BY r1.meter_no
    </select>

    <!-- 批量查询账单信息 -->
    <select id="batchSelectBillsByIds" resultMap="WaterfeeBillResult">
        SELECT
        bill_id, bill_number, customer_id,
        total_amount, bill_status, consumption_volume,
        billing_period_start, billing_period_end
        FROM waterfee_bills
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
        ORDER BY bill_id
    </select>

    <!-- 批量更新抄表记录状态 -->
    <update id="batchUpdateRecordStatus">
        UPDATE waterfee_meter_reading_record
        SET is_audited = #{status},
        audit_time = NOW(),
        update_time = NOW()
        WHERE record_id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 批量查询用户信息 -->
    <select id="batchSelectUserInfoByMeterNos" resultType="java.util.Map">
        SELECT
        m.meter_no,
        u.user_id,
        u.user_no,
        u.user_name,
        u.use_water_nature as user_type,
        u.address
        FROM waterfee_meter m
        INNER JOIN waterfee_user u ON m.user_id = u.user_id
        WHERE m.meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND m.del_flag = '0' AND u.del_flag = '0'
        ORDER BY m.meter_no
    </select>

    <!-- 批量查询价格配置 -->
    <select id="batchSelectPriceConfigByUserTypes" resultType="java.util.Map">
        SELECT
        water_use_type as user_type,
        id as price_config_id,
        name as price_config_name,
        calculation_method
        FROM waterfee_price_config
        WHERE water_use_type IN
        <foreach collection="userTypes" item="userType" open="(" separator="," close=")">
            #{userType}
        </foreach>
        AND del_flag = '0'
        ORDER BY water_use_type
    </select>

    <!-- 批量检查季度重复记录 -->
    <select id="batchCheckQuarterDuplicates" resultType="java.lang.String">
        SELECT DISTINCT meter_no
        FROM waterfee_meter_reading_record
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND task_id = #{taskId}
        AND reading_time BETWEEN #{quarterStart} AND #{quarterEnd}
        AND del_flag = '0'
    </select>

    <!-- 批量统计抄表记录 -->
    <select id="batchStatisticsRecords" resultType="java.util.Map">
        SELECT
        meter_no,
        COUNT(*) as total_count,
        SUM(CASE WHEN is_audited = '1' THEN 1 ELSE 0 END) as audited_count,
        SUM(CASE WHEN is_audited = '0' THEN 1 ELSE 0 END) as pending_count,
        MAX(reading_time) as latest_reading_time
        FROM waterfee_meter_reading_record
        WHERE meter_no IN
        <foreach collection="meterNos" item="meterNo" open="(" separator="," close=")">
            #{meterNo}
        </foreach>
        AND del_flag = '0'
        GROUP BY meter_no
        ORDER BY meter_no
    </select>

    <!-- 批量查询账单审核状态 -->
    <select id="batchSelectBillAuditStatus" resultType="java.util.Map">
        SELECT
        bill_id,
        bill_status,
        billing_issue_date,
        billing_due_date
        FROM waterfee_bills
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
        ORDER BY bill_id
    </select>

    <!-- 批量更新账单状态 -->
    <update id="batchUpdateBillStatus">
        UPDATE waterfee_bills
        SET bill_status = #{status},
        update_time = NOW(),
        update_by = #{updateBy}
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 批量查询客户余额 -->
    <select id="batchSelectCustomerBalance" resultType="java.util.Map">
        SELECT
        user_id as customer_id,
        balance as account_balance,
        0 as frozen_amount,
        balance as available_balance
        FROM waterfee_user
        WHERE user_id IN
        <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
        AND del_flag = '0'
        ORDER BY user_id
    </select>

    <!-- 批量插入支付记录 -->
    <insert id="batchInsertPaymentRecords" parameterType="java.util.List">
        INSERT INTO waterfee_payment_detail (
        payment_detail_id, user_id, bill_id, payment_amount, payment_method,
        payment_status, payment_time, create_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
            #{record.paymentDetailId},
            #{record.userId},
            #{record.billId},
            #{record.paymentAmount},
            #{record.paymentMethod},
            #{record.paymentStatus},
            #{record.paymentTime},
            NOW()
            )
        </foreach>
    </insert>

    <!-- 批量查询智能表读数（基于水表表模拟） -->
    <select id="batchSelectIntelligentMeterReadings" resultType="java.util.Map">
        SELECT
        meter_id,
        meter_no,
        initial_reading as current_reading,
        install_date as reading_time,
        'GOOD' as signal_strength,
        '85%' as battery_level
        FROM waterfee_meter
        WHERE meter_id IN
        <foreach collection="meterIds" item="meterId" open="(" separator="," close=")">
            #{meterId}
        </foreach>
        AND meter_type = '2'
        AND del_flag = '0'
        ORDER BY meter_id
    </select>

</mapper>
