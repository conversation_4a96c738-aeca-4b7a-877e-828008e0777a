<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.community.mapper.CommunityMapper">
    
    <resultMap type="org.dromara.waterfee.community.domain.Community" id="CommunityResult">
        <id     property="id"           column="id"            />
        <result property="communityNo"   column="community_no"   />
        <result property="communityName" column="community_name" />
        <result property="areaId"        column="area_id"        />
        <result property="areaName"      column="area_name"      />
        <result property="address"       column="address"        />
        <result property="description"   column="description"    />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCommunityVo">
        select c.id, c.community_no, c.community_name, c.area_id, a.area_name, c.address, c.description, c.create_by, c.create_time, c.update_by, c.update_time
        from wf_community c
        left join wf_area a on c.area_id = a.id
    </sql>

    <select id="selectCommunityList" parameterType="Community" resultMap="CommunityResult">
        <include refid="selectCommunityVo"/>
        <where>  
            <if test="communityNo != null  and communityNo != ''">
                AND c.community_no like concat('%', #{communityNo}, '%')
            </if>
            <if test="communityName != null  and communityName != ''">
                AND c.community_name like concat('%', #{communityName}, '%')
            </if>
            <if test="areaId != null ">
                AND c.area_id = #{areaId}
            </if>
        </where>
        order by c.create_time desc
    </select>
    
    <select id="selectCommunityAll" resultMap="CommunityResult">
        <include refid="selectCommunityVo"/>
        order by c.create_time desc
    </select>
    
    <select id="selectCommunityById" parameterType="Long" resultMap="CommunityResult">
        <include refid="selectCommunityVo"/>
        where c.id = #{id}
    </select>
    
    <select id="selectCommunityByCommunityNo" parameterType="String" resultMap="CommunityResult">
        <include refid="selectCommunityVo"/>
        where c.community_no = #{communityNo}
    </select>
        
    <insert id="insertCommunity" parameterType="Community">
        insert into wf_community
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="communityNo != null and communityNo != ''">community_no,</if>
            <if test="communityName != null and communityName != ''">community_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="communityNo != null and communityNo != ''">#{communityNo},</if>
            <if test="communityName != null and communityName != ''">#{communityName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCommunity" parameterType="Community">
        update wf_community
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityNo != null and communityNo != ''">community_no = #{communityNo},</if>
            <if test="communityName != null and communityName != ''">community_name = #{communityName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommunityById" parameterType="Long">
        delete from wf_community where id = #{id}
    </delete>

    <delete id="deleteCommunityByIds" parameterType="Long">
        delete from wf_community where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="countCommunityById" resultType="Long">
        select count(*) from waterfee_community where del_flag = '0' and id = #{id}
    </select>
    
</mapper>
