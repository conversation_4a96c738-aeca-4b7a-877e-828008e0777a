<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageArSplitMapper">
    <resultMap id="WaterfeeChargeManageArSplitResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArSplit">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManageArSplitResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArSplitVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManageArSplitVo">
        select wcmas.id, wcmas.bill_id, wcmas.project_code, wcmas.department_id, wcmas.split_amount, wcmas.remark, wcmas.tenant_id, wcmas.create_by, wcmas.create_time, wcmas.update_by, wcmas.update_time, wcmas.del_flag from waterfee_charge_manage_ar_split wcmas
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManageArSplitResultVo">
        <include refid="selectWaterfeeChargeManageArSplitVo"/>
        <where>
            <if test="query.billId != null"> and wcmas.bill_id = #{query.billId}</if>
            <if test="query.projectCode != null and query.projectCode != ''"> and wcmas.project_code = #{query.projectCode}</if>
            <if test="query.departmentId != null"> and wcmas.department_id = #{query.departmentId}</if>
            <if test="query.splitAmount != null"> and wcmas.split_amount = #{query.splitAmount}</if>
            and wcmas.del_flag = '0'
        </where>
    </select>
</mapper>
