<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.template.mapper.WaterfeeTemplateMapper">

    <resultMap type="org.dromara.waterfee.template.domain.vo.WaterfeeTemplateVo" id="WaterfeeTemplateResult">
        <result property="templateId"               column="template_id"                />
        <result property="templateName"             column="template_name"              />
        <result property="templateType"             column="template_type"              />
        <result property="templateCategory"         column="template_category"          />
        <result property="fileName"                 column="file_name"                  />
        <result property="fileUrl"                  column="file_url"                   />
        <result property="isEnabled"                column="is_enabled"                 />
        <result property="templateDescription"      column="template_description"       />
        <result property="uploadBy"                 column="upload_by"                  />
        <result property="uploadTime"               column="upload_time"                />
        <result property="enabledBy"                column="enabled_by"                 />
        <result property="enabledTime"              column="enabled_time"               />
        <result property="version"                  column="version"                    />
        <result property="remark"                   column="remark"                     />
        <result property="createTime"               column="create_time"                />
        <result property="updateTime"               column="update_time"                />
    </resultMap>

    <sql id="selectWaterfeeTemplateVo">
        select template_id, template_name, template_type, template_category, file_name, file_url, is_enabled, template_description, upload_by, upload_time,
               enabled_by, enabled_time, version, remark, create_time, update_time
        from waterfee_template
    </sql>

<!--    <select id="selectWaterfeeTemplateList" parameterType="org.dromara.waterfee.template.domain.bo.WaterfeeTemplateBo" resultMap="WaterfeeTemplateResult">-->
<!--        <include refid="selectWaterfeeTemplateVo"/>-->
<!--        <where>-->
<!--            del_flag = '0'-->
<!--            <if test="templateName != null and templateName != ''">-->
<!--                AND template_name like concat('%', #{templateName}, '%')-->
<!--            </if>-->
<!--            <if test="templateType != null and templateType != ''">-->
<!--                AND template_type = #{templateType}-->
<!--            </if>-->
<!--            <if test="templateCategory != null and templateCategory != ''">-->
<!--                AND template_category = #{templateCategory}-->
<!--            </if>-->
<!--            <if test="isEnabled != null and isEnabled != ''">-->
<!--                AND is_enabled = #{isEnabled}-->
<!--            </if>-->
<!--            <if test="uploadBy != null and uploadBy != ''">-->
<!--                AND upload_by = #{uploadBy}-->
<!--            </if>-->
<!--            <if test="params.beginTime != null and params.beginTime != ''">-->
<!--                AND create_time &gt;= #{params.beginTime}-->
<!--            </if>-->
<!--            <if test="params.endTime != null and params.endTime != ''">-->
<!--                AND create_time &lt;= #{params.endTime}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by is_enabled desc, create_time desc-->
<!--    </select>-->

</mapper>
