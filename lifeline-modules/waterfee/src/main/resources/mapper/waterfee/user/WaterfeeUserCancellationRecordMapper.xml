<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.WaterfeeUserCancellationRecordMapper">
    <resultMap id="WaterfeeUserCancellationRecordResult" autoMapping="true"
               type="org.dromara.waterfee.user.domain.WaterfeeUserCancellationRecord">
        <id property="cancellationId" column="cancellation_id"/>
    </resultMap>

    <resultMap id="WaterfeeUserCancellationRecordResultVo" autoMapping="true"
               type="org.dromara.waterfee.user.domain.vo.WaterfeeUserCancellationRecordVo">
        <id property="cancellationId" column="cancellation_id"/>
    </resultMap>

    <sql id="selectWaterfeeUserCancellationRecordVo">
        select wucr.cancellation_id, wucr.user_id, wucr.cancellation_reason, wucr.cancellation_time, wucr.tenant_id,
        wucr.create_dept, wucr.create_time, wucr.create_by, wucr.update_time, wucr.update_by, wucr.del_flag from
        waterfee_user_cancellation_record wucr
    </sql>

    <select id="queryList" resultMap="WaterfeeUserCancellationRecordResultVo">
        select wucr.cancellation_id, wucr.user_id, wucr.cancellation_reason, wucr.cancellation_time, wucr.tenant_id,
        wucr.create_dept, wucr.create_time, wucr.create_by, wucr.update_time, wucr.update_by, wucr.del_flag, wu.user_no,
        wu.user_name, wu.customer_nature,
        wu.use_water_nature, su.nick_name as create_by_user_name, wc.community_name
        from waterfee_user_cancellation_record wucr
        left join waterfee_user wu on wucr.user_id = wu.user_id
        left join sys_user su on wucr.create_by = su.user_id
        left join waterfee_community wc on wu.community_id = wc.id
        <where>
            wucr.del_flag = '0'
            <if test="query.userNoOrUserName != null and query.userNoOrUserName != ''">
                and (wu.user_no like concat('%', #{query.userNoOrUserName}, '%')
                or wu.user_name like concat('%', #{query.userNoOrUserName}, '%'))
            </if>
            <if test="query.startTime != null">
                and DATE(wucr.create_time) &gt;= DATE(#{query.startTime})
            </if>
            <if test="query.endTime != null">
                and DATE(wucr.create_time) &lt;= DATE(#{query.endTime})
            </if>
        </where>
        order by wucr.create_time desc
    </select>
</mapper>
