<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.WaterfeeUserBasicInfoChangeRecordMapper">
    <resultMap id="WaterfeeUserBasicInfoChangeRecordResult" autoMapping="true" type="org.dromara.waterfee.user.domain.WaterfeeUserBasicInfoChangeRecord">
        <id property="basicInfoChangeId" column="basic_info_change_id"/>
    </resultMap>

    <resultMap id="WaterfeeUserBasicInfoChangeRecordResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.WaterfeeUserBasicInfoChangeRecordVo">
        <id property="basicInfoChangeId" column="basic_info_change_id"/>
    </resultMap>

    <sql id="selectWaterfeeUserBasicInfoChangeRecordVo">
        select wubicr.basic_info_change_id, wubicr.user_id,  wubicr.before_customer_nature, wubicr.before_use_water_nature, wubicr.before_use_water_number, wubicr.before_phone_number, wubicr.before_address, wubicr.before_email, wubicr.before_taxpayer_identification_number, wubicr.before_invoice_name, wubicr.before_invoice_type, wubicr.after_customer_nature, wubicr.after_use_water_nature, wubicr.after_use_water_number, wubicr.after_phone_number, wubicr.after_address, wubicr.after_email, wubicr.after_taxpayer_identification_number, wubicr.after_invoice_name, wubicr.after_invoice_type, wubicr.tenant_id, wubicr.create_dept, wubicr.create_time, wubicr.create_by, wubicr.update_time, wubicr.update_by, wubicr.del_flag, wubicr.change_content from waterfee_user_basic_info_change_record wubicr
    </sql>

    <select id="queryList" resultMap="WaterfeeUserBasicInfoChangeRecordResultVo">
        select wubicr.basic_info_change_id, wubicr.user_id,  wubicr.before_customer_nature, wubicr.before_use_water_nature, wubicr.before_use_water_number, wubicr.before_phone_number, wubicr.before_address, wubicr.before_email, wubicr.before_taxpayer_identification_number, wubicr.before_invoice_name, wubicr.before_invoice_type, wubicr.after_customer_nature, wubicr.after_use_water_nature, wubicr.after_use_water_number, wubicr.after_phone_number, wubicr.after_address, wubicr.after_email, wubicr.after_taxpayer_identification_number, wubicr.after_invoice_name, wubicr.after_invoice_type, wubicr.tenant_id, wubicr.create_dept, wubicr.create_time, wubicr.create_by, wubicr.update_time, wubicr.update_by, wubicr.del_flag, wubicr.change_content, wu.user_no, wu.user_name, su.nick_name as create_by_user_name
        from waterfee_user_basic_info_change_record wubicr
        left join waterfee_user wu on wubicr.user_id = wu.user_id
        left join sys_user su on wubicr.create_by = su.user_id
        <where>
            wubicr.del_flag = '0'
            <if test="query.userNoOrUserName != null and query.userNoOrUserName != ''">
                and (wu.user_no like concat('%', #{query.userNoOrUserName}, '%')
                or wu.user_name like concat('%', #{query.userNoOrUserName}, '%'))
            </if>
            <if test="query.startTime != null">
                and DATE(wubicr.create_time) &gt;= DATE(#{query.startTime})
            </if>
            <if test="query.endTime != null">
                and DATE(wubicr.create_time) &lt;= DATE(#{query.endTime})
            </if>
        </where>
        order by wubicr.create_time desc
    </select>

    <select id="getWaterTypeChangeDetails" resultType="org.dromara.waterfee.statisticalReport.domain.WaterTypeChangeDetailsVO">
        select
            bicr.create_time,
            u.user_no,
            u.user_name,
            u.address,
            u.phone_number,
            u.certificate_number,
            d1.dict_label as before_customer_nature,
            d2.dict_label as before_use_water_nature,
            d3.dict_label as after_customer_nature,
            d4.dict_label as after_use_water_nature
        from waterfee_user_basic_info_change_record bicr
            left join waterfee_user u on u.user_id = bicr.user_id
            left join sys_dict_data d1 on d1.dict_type = 'waterfee_user_customer_nature' and d1.dict_value = bicr.before_customer_nature
            left join sys_dict_data d2 on d2.dict_type = 'waterfee_user_use_water_nature' and d2.dict_value = bicr.before_use_water_nature
            left join sys_dict_data d3 on d3.dict_type = 'waterfee_user_customer_nature' and d3.dict_value = bicr.after_customer_nature
            left join sys_dict_data d4 on d4.dict_type = 'waterfee_user_use_water_nature' and d4.dict_value = bicr.after_use_water_nature
        where
            bicr.del_flag = 0
        order by bicr.create_time desc
    </select>
</mapper>
