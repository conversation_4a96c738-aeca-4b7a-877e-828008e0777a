<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.WaterfeeUserTransferOwnershipRecordMapper">
    <resultMap id="WaterfeeUserTransferOwnershipRecordResult" autoMapping="true" type="org.dromara.waterfee.user.domain.WaterfeeUserTransferOwnershipRecord">
        <id property="transferId" column="transfer_id"/>
    </resultMap>

    <resultMap id="WaterfeeUserTransferOwnershipRecordResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.WaterfeeUserTransferOwnershipRecordVo">
        <id property="transferId" column="transfer_id"/>
    </resultMap>

    <sql id="selectWaterfeeUserTransferOwnershipRecordVo">
        select wutor.transfer_id, wutor.user_id, wutor.before_use_water_number, wutor.before_user_name, wutor.before_phone_number, wutor.before_certificate_type, wutor.before_certificate_number, wutor.before_email, wutor.after_use_water_number, wutor.after_user_name, wutor.after_phone_number, wutor.after_certificate_type, wutor.after_certificate_number, wutor.after_email, wutor.tenant_id, wutor.create_dept, wutor.create_time, wutor.create_by, wutor.update_time, wutor.update_by, wutor.del_flag from waterfee_user_transfer_ownership_record wutor
    </sql>

    <select id="queryList" resultMap="WaterfeeUserTransferOwnershipRecordResultVo">
        select wutor.transfer_id, wutor.user_id, wutor.before_use_water_number, wutor.before_user_name, wutor.before_phone_number, wutor.before_certificate_type, wutor.before_certificate_number, wutor.before_email, wutor.after_use_water_number, wutor.after_user_name, wutor.after_phone_number, wutor.after_certificate_type, wutor.after_certificate_number, wutor.after_email, wutor.tenant_id, wutor.create_dept, wutor.create_time, wutor.create_by, wutor.update_time, wutor.update_by, wutor.del_flag, wu.user_no, su.nick_name as create_by_user_name
        from waterfee_user_transfer_ownership_record wutor
        left join waterfee_user wu on wutor.user_id = wu.user_id
        left join sys_user su on wutor.create_by = su.user_id
        <where>
            wutor.del_flag = '0'
            <if test="query.userNoOrUserName != null and query.userNoOrUserName != ''">
                and (wu.user_no like concat('%', #{query.userNoOrUserName}, '%')
                or wu.user_name like concat('%', #{query.userNoOrUserName}, '%')
                or wutor.before_user_name like concat('%', #{query.userNoOrUserName}, '%')
                or wutor.after_user_name like concat('%', #{query.userNoOrUserName}, '%'))
            </if>
            <if test="query.startTime != null">
                and DATE(wutor.create_time) &gt;= DATE(#{query.startTime})
            </if>
            <if test="query.endTime != null">
                and DATE(wutor.create_time) &lt;= DATE(#{query.endTime})
            </if>
        </where>
        order by wutor.create_time desc
    </select>

    <select id="getUserTransferDetails" resultType="org.dromara.waterfee.statisticalReport.domain.UserTransferDetailsVO">
        select
            tor.create_time,
            u.user_no,
            tor.before_user_name,
            u.address,
            u.phone_number,
            u.certificate_number,
            d1.dict_label as customer_nature,
            d2.dict_label as use_water_nature,
            tor.after_user_name
        from waterfee_user_transfer_ownership_record tor
            left join waterfee_user u on u.user_id = tor.user_id
            left join sys_dict_data d1 on d1.dict_type = 'waterfee_user_customer_nature' and d1.dict_value = u.customer_nature
            left join sys_dict_data d2 on d2.dict_type = 'waterfee_user_use_water_nature' and d2.dict_value = u.use_water_nature
        where
            tor.del_flag = 0
        order by tor.create_time desc
    </select>
</mapper>
