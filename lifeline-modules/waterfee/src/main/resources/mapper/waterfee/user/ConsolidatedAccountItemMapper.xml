<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.ConsolidatedAccountItemMapper">
    <resultMap id="ConsolidatedAccountItemResult" autoMapping="true" type="org.dromara.waterfee.user.domain.ConsolidatedAccountItem">
        <id property="consolidatedAccountItemId" column="consolidated_account_item_id"/>
    </resultMap>

    <resultMap id="ConsolidatedAccountItemResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.ConsolidatedAccountItemVo">
        <id property="consolidatedAccountItemId" column="consolidated_account_item_id"/>
    </resultMap>

    <sql id="selectConsolidatedAccountItemVo">
        select cai.consolidated_account_item_id, cai.consolidated_account_id, cai.user_id, cai.tenant_id, cai.create_dept, cai.create_time, cai.create_by, cai.update_time, cai.update_by, cai.del_flag from consolidated_account_item cai
    </sql>
    <select id="queryList" resultMap="ConsolidatedAccountItemResultVo">
        <include refid="selectConsolidatedAccountItemVo"/>
        <where>
            <if test="query.consolidatedAccountId != null"> and cai.consolidated_account_id = #{query.consolidatedAccountId}</if>
            <if test="query.userId != null"> and cai.user_id = #{query.userId}</if>
            and cai.del_flag = '0'
        </where>
    </select>
</mapper>
