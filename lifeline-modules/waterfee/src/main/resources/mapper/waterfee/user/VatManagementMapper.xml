<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.user.mapper.VatManagementMapper">
    <resultMap id="VatManagementResult" autoMapping="true" type="org.dromara.waterfee.user.domain.VatManagement">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="VatManagementResultVo" autoMapping="true" type="org.dromara.waterfee.user.domain.vo.VatManagementVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectVatManagementVo">
        select vm.id, vm.bill_number, vm.invoice_type, vm.invoice_code, vm.invoice_number, vm.invoice_status, vm.total_amount, vm.tax_exclusive_amount, vm.tax_rate, vm.tax_amount, vm.is_tax_included, vm.buyer_name, vm.buyer_tax_id, vm.buyer_address_tel, vm.buyer_bank_account, vm.seller_name, vm.seller_tax_id, vm.seller_address_tel, vm.seller_bank_account, vm.issue_time, vm.red_flush_time, vm.cancel_time, vm.operator_id, vm.red_flush_reason, vm.cancel_reason, vm.tenant_id, vm.create_dept, vm.create_time, vm.create_by, vm.update_time, vm.update_by, vm.del_flag from vat_management vm
    </sql>
    <select id="queryList" resultMap="VatManagementResultVo">
        <include refid="selectVatManagementVo"/>
        <where>
            <if test="query.billNumber != null and query.billNumber != ''"> and vm.bill_number = #{query.billNumber}</if>
            <if test="query.invoiceType != null and query.invoiceType != ''"> and vm.invoice_type = #{query.invoiceType}</if>
            <if test="query.invoiceCode != null and query.invoiceCode != ''"> and vm.invoice_code = #{query.invoiceCode}</if>
            <if test="query.invoiceNumber != null and query.invoiceNumber != ''"> and vm.invoice_number = #{query.invoiceNumber}</if>
            <if test="query.invoiceStatus != null and query.invoiceStatus != ''"> and vm.invoice_status = #{query.invoiceStatus}</if>
            <if test="query.totalAmount != null"> and vm.total_amount = #{query.totalAmount}</if>
            <if test="query.taxExclusiveAmount != null"> and vm.tax_exclusive_amount = #{query.taxExclusiveAmount}</if>
            <if test="query.taxRate != null"> and vm.tax_rate = #{query.taxRate}</if>
            <if test="query.taxAmount != null"> and vm.tax_amount = #{query.taxAmount}</if>
            <if test="query.isTaxIncluded != null and query.isTaxIncluded != ''"> and vm.is_tax_included = #{query.isTaxIncluded}</if>
            <if test="query.buyerName != null and query.buyerName != ''"> and vm.buyer_name like concat(concat('%', #{query.buyerName}), '%')</if>
            <if test="query.buyerTaxId != null and query.buyerTaxId != ''"> and vm.buyer_tax_id = #{query.buyerTaxId}</if>
            <if test="query.buyerAddressTel != null and query.buyerAddressTel != ''"> and vm.buyer_address_tel = #{query.buyerAddressTel}</if>
            <if test="query.buyerBankAccount != null and query.buyerBankAccount != ''"> and vm.buyer_bank_account = #{query.buyerBankAccount}</if>
            <if test="query.sellerName != null and query.sellerName != ''"> and vm.seller_name like concat(concat('%', #{query.sellerName}), '%')</if>
            <if test="query.sellerTaxId != null and query.sellerTaxId != ''"> and vm.seller_tax_id = #{query.sellerTaxId}</if>
            <if test="query.sellerAddressTel != null and query.sellerAddressTel != ''"> and vm.seller_address_tel = #{query.sellerAddressTel}</if>
            <if test="query.sellerBankAccount != null and query.sellerBankAccount != ''"> and vm.seller_bank_account = #{query.sellerBankAccount}</if>
            <if test="query.issueTime != null"> and vm.issue_time = #{query.issueTime}</if>
            <if test="query.redFlushTime != null"> and vm.red_flush_time = #{query.redFlushTime}</if>
            <if test="query.cancelTime != null"> and vm.cancel_time = #{query.cancelTime}</if>
            <if test="query.operatorId != null"> and vm.operator_id = #{query.operatorId}</if>
            <if test="query.redFlushReason != null and query.redFlushReason != ''"> and vm.red_flush_reason = #{query.redFlushReason}</if>
            <if test="query.cancelReason != null and query.cancelReason != ''"> and vm.cancel_reason = #{query.cancelReason}</if>
            and vm.del_flag = '0'
        </where>
    </select>
</mapper>
