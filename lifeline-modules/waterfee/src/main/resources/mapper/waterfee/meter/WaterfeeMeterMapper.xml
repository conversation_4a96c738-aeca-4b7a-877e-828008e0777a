<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meter.mapper.WaterfeeMeterMapper">

    <resultMap type="org.dromara.waterfee.meter.domain.WaterfeeMeter" id="WaterfeeMeterResult">
        <id property="meterId" column="meter_id"/>
        <result property="meterNo" column="meter_no"/>
        <result property="meterType" column="meter_type"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="meterCategory" column="meter_category"/>
        <result property="meterClassification" column="meter_classification"/>
        <result property="measurementPurpose" column="measurement_purpose"/>
        <result property="caliber" column="caliber"/>
        <result property="accuracy" column="accuracy"/>
        <result property="initialReading" column="initial_reading"/>
        <result property="installDate" column="install_date"/>
        <result property="businessAreaId" column="business_area_id"/>
        <result property="meterBookId" column="meter_book_id"/>
        <result property="sortNo" column="sort_no"/>
        <result property="installAddress" column="install_address"/>
        <result property="meterRatio" column="meter_ratio"/>
        <result property="communicationMode" column="communication_mode"/>
        <result property="valveControl" column="valve_control"/>
        <result property="imei" column="imei"/>
        <result property="imsi" column="imsi"/>
        <result property="iotPlatform" column="iot_platform"/>
        <result property="prepaid" column="prepaid"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

</mapper>
