<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageUserRelationMapper">
    <resultMap id="WaterfeeChargeManageUserRelationResult" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageUserRelation">
        <id property="id" column="id"/>
    </resultMap>

    <resultMap id="WaterfeeChargeManageUserRelationResultVo" autoMapping="true" type="org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageUserRelationVo">
        <id property="id" column="id"/>
    </resultMap>

    <sql id="selectWaterfeeChargeManageUserRelationVo">
        select wcmur.id, wcmur.master_user_id, wcmur.attached_user_id, wcmur.relation_type, wcmur.remark, wcmur.tenant_id, wcmur.create_by, wcmur.create_time, wcmur.update_by, wcmur.update_time, wcmur.del_flag from waterfee_charge_manage_user_relation wcmur
    </sql>
    <select id="queryList" resultMap="WaterfeeChargeManageUserRelationResultVo">
        <include refid="selectWaterfeeChargeManageUserRelationVo"/>
        <where>
            <if test="query.masterUserId != null"> and wcmur.master_user_id = #{query.masterUserId}</if>
            <if test="query.attachedUserId != null"> and wcmur.attached_user_id = #{query.attachedUserId}</if>
            <if test="query.relationType != null and query.relationType != ''"> and wcmur.relation_type = #{query.relationType}</if>
            and wcmur.del_flag = '0'
        </where>
    </select>
</mapper>
