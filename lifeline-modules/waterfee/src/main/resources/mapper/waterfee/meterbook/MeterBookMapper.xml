<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.meterBook.mapper.MeterBookMapper">

    <resultMap type="org.dromara.waterfee.meterBook.domain.MeterBook" id="MeterBookResult">
        <id property="id" column="id"/>
        <result property="areaId" column="area_id"/>
        <result property="bookNo" column="book_no"/>
        <result property="bookName" column="book_name"/>
        <result property="readType" column="read_type"/>
        <result property="readCycle" column="read_cycle"/>
        <result property="readDay" column="read_day"/>
        <result property="readBaseDay" column="read_base_day"/>
        <result property="reader" column="reader"/>
        <result property="readerName" column="reader_name"/>
        <result property="readerLeader" column="reader_leader"/>
        <result property="readerLeaderName" column="reader_leader_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap id="TableBookStatisticsNumberOfUsersResult" type="org.dromara.waterfee.statisticalReport.domain.TableBookStatisticsNumberOfUsersVO">
        <id property="bookName" column="book_name"/>
        <result property="totalCount" column="total_count"/>
        <result property="residentCount" column="resident_count"/>
        <result property="businessCount" column="business_count"/>
        <result property="specialType" column="special_type"/>
        <result property="publicGreeningCount" column="public_greening_count"/>
        <result property="communityGreeningCount" column="community_greening_count"/>
    </resultMap>

    <sql id="selectMeterBookVo">
        select mb.id, mb.area_id, a.area_name, mb.book_no, mb.book_name, mb.read_type, mb.read_cycle,
        mb.read_day, mb.read_base_day, mb.reader, mb.reader_name, mb.reader_leader, mb.reader_leader_name,
        mb.create_by, mb.create_time, mb.update_by, mb.update_time, mb.remark
        from waterfee_meter_book mb
        left join waterfee_area a on mb.area_id = a.area_id
    </sql>

    <select id="getTableBookStatisticsNumberOfUsers" resultMap="TableBookStatisticsNumberOfUsersResult">
        select
            b.book_name,
            count(u.user_id) as total_count,
            count(case when u.use_water_nature = 'resident' then 1 end) as resident_count,
            count(case when u.use_water_nature = 'business' then 1 end) as business_count,
            count(case when u.use_water_nature = 'publicGreening' then 1 end) as public_greening_count,
            count(case when u.use_water_nature = 'communityGreening' then 1 end) as community_greening_count,
            count(case when u.use_water_nature = 'specialType' then 1 end) as special_type
        from waterfee_meter_book b
            left join waterfee_meter m on m.meter_book_id = b.id
            left join waterfee_user u on u.user_id = m.user_id
        where
            u.user_status = 'normal' and u.del_flag = 0
        group by b.book_no
        order By total_count desc
    </select>

    <select id="getMechanicalWatchMeterNumberStatistics" resultType="org.dromara.waterfee.statisticalReport.domain.MechanicalWatchMeterNumberStatisticsVO">
        select
            u.user_no,
            u.user_name,
            u.address,
            u.phone_number,
            u.certificate_number,
            d1.dict_label as customer_nature,
            d2.dict_label as user_status,
            b.reader_name
        from waterfee_meter_book b
            left join waterfee_meter m on m.meter_book_id = b.id
            left join waterfee_user u on u.user_id = m.user_id
            left join sys_dict_data d1 on d1.dict_type = 'waterfee_user_customer_nature' and d1.dict_value = u.customer_nature
            left join sys_dict_data d2 on d2.dict_type = 'waterfee_user_user_status' and d2.dict_value = u.user_status
        where
            u.user_no is not null
            and m.meter_type = 1
            <if test="readerName != null and readerName != ''">
                and b.reader_name like concat('%', #{readerName}, '%')
            </if>
            <if test="userStatus != null and userStatus != ''">
                and u.user_status = #{userStatus}
            </if>
        order by b.create_time desc
    </select>

    <select id="getMechanicalMeterReadingRateStatistics" resultType="org.dromara.waterfee.statisticalReport.domain.MechanicalMeterReadingRateStatisticsVO">
        select
            table1.book_id,
            table1.reader_name,
            ifnull(max(total_count), 0) total_count,
            ifnull(max(cancellation_count), 0) cancellation_count,
            ifnull(max(deactivate_count), 0) deactivate_count,
            ifnull(max(plan_read_count), 0) plan_read_count,
            ifnull(max(read_count), 0) read_count,
            round(ifnull(max(read_count) / max(plan_read_count), 0), 2) AS reading_rate,
            ifnull(max(plan_receivable_count), 0) plan_receivable_count,
            ifnull(max(receivable_count), 0) receivable_count,
            round(ifnull(max(receivable_count) / max(plan_receivable_count), 0), 2) AS recovery_rate,
            ifnull(max(plan_payment), 0) plan_payment,
            ifnull(max(payment), 0) payment,
            round(ifnull(max(payment) / max(plan_payment), 0), 2) AS payment_rate
        from
            (
                select
                    book.id as book_id,
                    book.reader_name,
                    count(u.user_id) as total_count,
                    count(case when u.user_status = 'cancellation' then 1 end) as cancellation_count,
                    count(case when u.user_status = 'deactivate' then 1 end) as deactivate_count,
                    count(m.meter_id) as plan_read_count,
                    count(m.meter_id) as plan_receivable_count
                from waterfee_meter_book book
                    left join waterfee_meter m on m.meter_book_id = book.id
                    left join waterfee_user u on u.user_id = m.user_id
                where
                    book.del_flag = 0
                    and m.meter_type = 1
                group by book.id
            ) table1
            left join
            (
                select
                    book.id as book_id,
                    count(case when b.bill_status = 'PAID' then 1 end) as receivable_count,
                    count(b.total_amount) as plan_payment,
                    count(b.amount_paid) as payment
                from waterfee_meter_book book
                    left join waterfee_bills b on b.meter_book_id = book.id
                    left join waterfee_meter m on m.meter_id = b.meter_id
                where
                    book.del_flag = 0
                    and m.meter_type = 1
                    <if test="quarterStartTime != null">
                        and b.create_time &gt;= #{quarterStartTime}
                    </if>
                    <if test="quarterEndTime != null">
                        and b.create_time &lt;= #{quarterEndTime}
                    </if>
                group by book.id
            ) table2 on table2.book_id = table1.book_id
            left join
            (
                select
                    book.id as book_id,
                    count(rr.record_id) as read_count
                from waterfee_meter_book book
                    left join waterfee_meter_reading_record rr on rr.meter_book_id = book.id
                    left join waterfee_meter m on m.meter_no = rr.meter_no
                where
                    book.del_flag = 0
                    and m.meter_type = 1
                    <if test="quarterStartTime != null">
                        and rr.reading_time &gt;= #{quarterStartTime}
                    </if>
                    <if test="quarterEndTime != null">
                        and rr.reading_time &lt;= #{quarterEndTime}
                    </if>
                group by book.id
            ) table3 on table3.book_id = table1.book_id
        group by table1.book_id
    </select>
</mapper>
