<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.invoice.mapper.NuonuoTokenMapper">

    <select id="selectLatestValidToken" resultType="org.dromara.waterfee.invoice.domain.vo.NuonuoTokenVo">
        SELECT
            token_id AS tokenId,
            access_token AS accessToken,
            expires_in AS expiresIn,
            expire_time AS expireTime,
            is_valid AS isValid,
            create_time AS createTime
        FROM
            invoice_nuonuo_token
        WHERE
            is_valid = '1'
        ORDER BY
            create_time DESC
        LIMIT 1
    </select>

</mapper>
