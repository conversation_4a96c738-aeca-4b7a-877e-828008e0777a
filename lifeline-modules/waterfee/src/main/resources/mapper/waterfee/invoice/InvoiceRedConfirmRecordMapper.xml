<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.invoice.mapper.InvoiceRedConfirmRecordMapper">
    <resultMap id="InvoiceRedConfirmRecordResult" autoMapping="true" type="org.dromara.waterfee.invoice.domain.InvoiceRedConfirmRecord">
        <id property="redApplyRecordId" column="red_apply_record_id"/>
    </resultMap>

    <resultMap id="InvoiceRedConfirmRecordResultVo" autoMapping="true" type="org.dromara.waterfee.invoice.domain.vo.InvoiceRedConfirmRecordVo">
        <id property="redApplyRecordId" column="red_apply_record_id"/>
    </resultMap>

    <sql id="selectInvoiceRedConfirmRecordVo">
        select ircr.red_apply_record_id, ircr.invoice_id, ircr.bill_id, ircr.bill_uuid, ircr.bill_status, ircr.bill_message, ircr.invoice_serial_num, ircr.order_no, ircr.request_status, ircr.open_status, ircr.red_invoice_number, ircr.bill_time, ircr.confirm_time, ircr.pdf_url, ircr.remark, ircr.tenant_id, ircr.create_dept, ircr.create_by, ircr.create_time, ircr.update_by, ircr.update_time, ircr.del_flag from invoice_red_confirm_record ircr
    </sql>
    <select id="queryList" resultMap="InvoiceRedConfirmRecordResultVo">
        <include refid="selectInvoiceRedConfirmRecordVo"/>
        <where>
            <if test="query.invoiceId != null"> and ircr.invoice_id = #{query.invoiceId}</if>
            <if test="query.billStatus != null and query.billStatus != ''"> and ircr.bill_status = #{query.billStatus}</if>
            <if test="query.requestStatus != null and query.requestStatus != ''"> and ircr.request_status = #{query.requestStatus}</if>
            <if test="query.openStatus != null and query.openStatus != ''"> and ircr.open_status = #{query.openStatus}</if>
            <if test="query.billTime != null"> and ircr.bill_time = #{query.billTime}</if>
            <if test="query.confirmTime != null"> and ircr.confirm_time = #{query.confirmTime}</if>
            and ircr.del_flag = '0'
        </where>
    </select>
</mapper>
