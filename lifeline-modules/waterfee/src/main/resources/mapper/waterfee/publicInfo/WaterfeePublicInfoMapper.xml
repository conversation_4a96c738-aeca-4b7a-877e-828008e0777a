<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.waterfee.publicInfo.mapper.WaterfeePublicInfoMapper">

    <resultMap type="org.dromara.waterfee.publicInfo.domain.vo.WaterfeePublicInfoVo" id="WaterfeePublicInfoResult">
        <result property="infoId"               column="info_id"                />
        <result property="title"                column="title"                  />
        <result property="content"              column="content"                />
        <result property="infoType"             column="info_type"              />
        <result property="publishTime"          column="publish_time"           />
        <result property="endTime"              column="end_time"               />
        <result property="publisher"            column="publisher"              />
        <result property="viewCount"            column="view_count"             />
        <result property="status"               column="status"                 />
        <result property="wechatDraftId"        column="wechat_draft_id"        />
        <result property="createBy"             column="create_by"              />
        <result property="createTime"           column="create_time"            />
        <result property="updateBy"             column="update_by"              />
        <result property="updateTime"           column="update_time"            />
        <result property="remark"               column="remark"                 />
    </resultMap>

<!--    <sql id="selectWaterfeePublicInfoVo">-->
<!--        select info_id, title, content, info_type, publish_time, end_time, publisher, view_count,-->
<!--               status, wechat_draft_id, create_by, create_time, update_by, update_time, remark-->
<!--        from waterfee_public_info-->
<!--    </sql>-->

<!--    <select id="selectList" parameterType="org.dromara.waterfee.publicInfo.domain.bo.WaterfeePublicInfoBo" resultMap="WaterfeePublicInfoResult">-->
<!--        <include refid="selectWaterfeePublicInfoVo"/>-->
<!--        <where>-->
<!--            del_flag = '0'-->
<!--            <if test="title != null and title != ''">-->
<!--                AND title like concat('%', #{title}, '%')-->
<!--            </if>-->
<!--            <if test="infoType != null and infoType != ''">-->
<!--                AND info_type = #{infoType}-->
<!--            </if>-->
<!--            <if test="status != null and status != ''">-->
<!--                AND status = #{status}-->
<!--            </if>-->
<!--            <if test="params.beginTime != null and params.beginTime != ''">-->
<!--                AND create_time &gt;= #{params.beginTime}-->
<!--            </if>-->
<!--            <if test="params.endTime != null and params.endTime != ''">-->
<!--                AND create_time &lt;= #{params.endTime}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by create_time desc-->
<!--    </select>-->

</mapper>
