# =====================================================
# 极致性能优化配置
# 针对超大规模数据处理的极致优化配置
# =====================================================

waterfee:
  bill-processing:
    # 极致批处理配置
    ultra-batch:
      # 超大批处理大小
      ultra-batch-size: 1000
      # 巨型批处理大小
      mega-batch-size: 2000
      # 最大并行流数量
      max-parallel-streams: 16
      # 智能分片阈值
      intelligent-sharding-threshold: 5000
      # 内存池大小
      memory-pool-size: 1000

    # 极致并发配置
    ultra-concurrency:
      # 核心线程数（CPU核心数 * 4）
      core-pool-size: ${waterfee.bill-processing.ultra-concurrency.calculated-core-pool-size:32}
      # 最大线程数（CPU核心数 * 8）
      max-pool-size: ${waterfee.bill-processing.ultra-concurrency.calculated-max-pool-size:64}
      # 队列容量
      queue-capacity: 10000
      # 线程空闲时间（秒）
      keep-alive-seconds: 60
      # 线程优先级调整
      thread-priority-boost: true

    # 缓存配置
    ultra-cache:
      # 启用多级缓存
      enable-multi-level-cache: true
      # 本地缓存TTL（毫秒）
      local-cache-ttl: 300000
      # Redis缓存TTL（秒）
      redis-cache-ttl: 1800
      # 热点数据TTL（秒）
      hot-data-ttl: 7200
      # 本地缓存最大条目数
      local-cache-max-entries: 10000
      # 缓存预热
      enable-cache-warmup: true
      # 预热批次大小
      warmup-batch-size: 500

    # 异步处理配置
    async-processing:
      # 启用异步处理
      enable-async-processing: true
      # 消费者线程数
      consumer-thread-count: 8
      # 队列最大大小
      max-queue-size: 50000
      # 处理超时时间（毫秒）
      processing-timeout: 300000
      # 批处理大小
      async-batch-size: 500

    # 性能监控配置
    ultra-performance:
      # 极致吞吐量目标（记录数/秒）
      target-throughput: 10000
      # 最小成功率（%）
      min-success-rate: 99.0
      # 最大处理时间（毫秒）
      max-processing-time: 60000
      # 性能等级阈值
      performance-thresholds:
        ultra-extreme: 10000
        extreme: 5000
        excellent: 2000
        good: 1000

# =====================================================
# Spring Boot 极致性能优化配置
# =====================================================

spring:
  # 数据源极致优化配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 连接池配置 - 针对高并发优化
      minimum-idle: 20
      maximum-pool-size: 100
      connection-timeout: 20000
      idle-timeout: 300000
      max-lifetime: 1200000
      leak-detection-threshold: 60000
      
      # 连接测试
      connection-test-query: SELECT 1
      validation-timeout: 3000
      
      # 性能优化参数
      auto-commit: true
      read-only: false
      isolation-level: TRANSACTION_READ_COMMITTED
      
      # 连接池名称
      pool-name: WaterfeeUltraHikariCP
      
      # 高级配置
      register-mbeans: true
      allow-pool-suspension: false
      
      # 数据库特定优化
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
        netTimeoutForStreamingResults: 0

  # JPA极致优化配置
  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      jdbc:
        # 批处理优化
        batch_size: 500
        batch_versioned_data: true
        order_inserts: true
        order_updates: true
        # 获取大小优化
        fetch_size: 100
      # 二级缓存配置
      cache:
        use_second_level_cache: true
        use_query_cache: true
        region:
          factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
    
    # 显示SQL（生产环境关闭）
    show-sql: false
    
    properties:
      hibernate:
        # 批处理配置
        jdbc.batch_size: 500
        order_inserts: true
        order_updates: true
        jdbc.batch_versioned_data: true
        
        # 查询优化
        query.plan_cache_max_size: 2048
        query.plan_parameter_metadata_max_size: 128
        
        # 统计信息（生产环境关闭）
        generate_statistics: false
        
        # 连接释放模式
        connection.release_mode: after_transaction
        
        # 格式化SQL（生产环境关闭）
        format_sql: false
        use_sql_comments: false

  # Redis极致优化配置
  data:
    redis:
      # 连接池配置
      lettuce:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 10
          max-wait: 2000ms
        # 集群配置
        cluster:
          refresh:
            adaptive: true
            period: 30s
      # 超时配置
      timeout: 3000ms
      connect-timeout: 3000ms
      # 序列化配置
      serializer: json

  # 任务调度极致优化
  task:
    scheduling:
      pool:
        size: 16
      thread-name-prefix: "ultra-scheduling-"
    execution:
      pool:
        core-size: 16
        max-size: 64
        queue-capacity: 10000
        keep-alive: 60s
      thread-name-prefix: "ultra-execution-"

  # Jackson配置优化
  jackson:
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null

# =====================================================
# MyBatis-Plus 极致性能优化配置
# =====================================================

mybatis-plus:
  configuration:
    # 缓存配置
    cache-enabled: true
    local-cache-scope: statement
    
    # 懒加载配置
    lazy-loading-enabled: false
    aggressive-lazy-loading: false
    
    # 执行器类型 - 批处理优化
    default-executor-type: BATCH
    
    # 语句超时时间
    default-statement-timeout: 60
    
    # 获取大小
    default-fetch-size: 100
    
    # 结果集处理
    safe-result-handler-enabled: false
    safe-row-bounds-enabled: false
    
    # 映射下划线到驼峰
    map-underscore-to-camel-case: true
    
    # 自动映射行为
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    
    # 日志配置
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: delFlag
      logic-delete-value: 2
      logic-not-delete-value: 0
      
      # ID生成策略
      id-type: auto
      
      # 表名前缀
      table-prefix: ""
      
      # 字段策略
      insert-strategy: not_null
      update-strategy: not_null
      where-strategy: not_null

# =====================================================
# 日志配置优化
# =====================================================

logging:
  level:
    # 减少不必要的日志输出
    root: WARN
    org.springframework: WARN
    org.hibernate: WARN
    org.mybatis: WARN
    com.zaxxer.hikari: WARN
    
    # 保留重要的业务日志
    org.dromara.waterfee.meterReading: INFO
    org.dromara.waterfee.bill: INFO
    
  pattern:
    # 优化日志格式，减少I/O开销
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    
  # 异步日志配置
  config: classpath:logback-ultra-performance.xml

# =====================================================
# 监控配置
# =====================================================

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,caches,threaddump,heapdump
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
    distribution:
      percentiles-histogram:
        http.server.requests: true
        hikaricp.connections: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99,0.999
        hikaricp.connections: 0.5,0.9,0.95,0.99
      sla:
        http.server.requests: 10ms,50ms,100ms,200ms,500ms,1s,2s,5s
    tags:
      application: waterfee-ultra-performance

# =====================================================
# 服务器配置优化
# =====================================================

server:
  # Tomcat优化配置
  tomcat:
    # 线程配置
    threads:
      max: 400
      min-spare: 50
    # 连接配置
    max-connections: 2000
    accept-count: 200
    connection-timeout: 20000
    # 压缩配置
    compression: on
    compressible-mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml
    # 访问日志
    accesslog:
      enabled: false # 生产环境可开启
      
  # HTTP/2支持
  http2:
    enabled: true

# =====================================================
# 环境特定配置
# =====================================================

---
# 开发环境
spring:
  config:
    activate:
      on-profile: dev
waterfee:
  bill-processing:
    ultra-batch:
      ultra-batch-size: 100
      mega-batch-size: 200
    ultra-performance:
      target-throughput: 1000

---
# 测试环境
spring:
  config:
    activate:
      on-profile: test
waterfee:
  bill-processing:
    ultra-batch:
      ultra-batch-size: 500
      mega-batch-size: 1000
    ultra-performance:
      target-throughput: 5000

---
# 生产环境
spring:
  config:
    activate:
      on-profile: prod
waterfee:
  bill-processing:
    ultra-batch:
      ultra-batch-size: 2000
      mega-batch-size: 5000
    ultra-concurrency:
      core-pool-size: 64
      max-pool-size: 128
    ultra-performance:
      target-throughput: 20000
      min-success-rate: 99.5
