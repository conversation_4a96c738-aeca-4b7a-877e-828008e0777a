# Spring配置
spring:
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
      # 设置文件上传临时目录
      location: /tmp
      # 启用multipart上传功能
      enabled: true
      # 设置文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 解析multipart请求的编码
      resolve-lazily: false

# 日志配置
logging:
  level:
    org.springframework.web: debug
    org.apache.tomcat.util.http.fileupload: debug