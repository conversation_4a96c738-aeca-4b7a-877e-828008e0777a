# =====================================================
# 账单处理流程性能优化配置
# =====================================================

waterfee:
  bill-processing:
    # 批处理配置
    batch:
      # 账单批处理大小
      bill-batch-size: 300
      # 支付批处理大小  
      payment-batch-size: 150
      # 数据库查询批处理大小
      query-batch-size: 500
      # 最大并发批次数
      max-concurrent-batches: 8
      # 是否启用批量插入优化
      enable-batch-insert: true
      # 批量插入大小
      batch-insert-size: 200

    # 并发配置
    concurrency:
      # 最大并发任务数
      max-concurrent-tasks: 10
      # 账单处理线程池大小
      bill-processing-thread-pool-size: 8
      # 支付处理线程池大小
      payment-processing-thread-pool-size: 4
      # 线程池队列大小
      queue-capacity: 1000
      # 线程空闲时间（秒）
      keep-alive-seconds: 60

    # 性能监控配置
    performance:
      # 最小吞吐量（记录数/秒）
      min-throughput: 100.0
      # 最大吞吐量（记录数/秒）
      max-throughput: 2000.0
      # 最小成功率（%）
      min-success-rate: 95.0
      # 最大处理时间（毫秒）
      max-processing-time: 300000
      # 最大内存使用率（%）
      max-memory-usage-percent: 85.0
      # 性能数据保留时间（毫秒）
      metrics-retention-time: 86400000
      # 是否启用自动优化
      enable-auto-optimization: true
      # 自动优化检查间隔（分钟）
      auto-optimization-interval: 60

    # 缓存配置
    cache:
      # 是否启用缓存
      enable-caching: true
      # 缓存过期时间（分钟）
      cache-expiration-minutes: 10
      # 最大缓存条目数
      max-cache-entries: 10000
      # 缓存预热
      enable-cache-warmup: true

    # 数据库优化配置
    database:
      # 是否启用读写分离
      enable-read-write-splitting: false
      # 读库权重
      read-weight: 70
      # 写库权重
      write-weight: 30
      # 连接池配置
      connection-pool:
        # 最小连接数
        minimum-idle: 10
        # 最大连接数
        maximum-pool-size: 50
        # 连接超时时间（毫秒）
        connection-timeout: 30000
        # 空闲超时时间（毫秒）
        idle-timeout: 600000
        # 最大生命周期（毫秒）
        max-lifetime: 1800000

    # 智能表处理配置
    intelligent-meter:
      # 是否启用并行读取
      enable-parallel-reading: true
      # 读取超时时间（毫秒）
      reading-timeout: 5000
      # 重试次数
      max-retry-times: 3
      # 批量读取大小
      batch-reading-size: 100

    # 支付处理配置
    payment:
      # 支付超时时间（毫秒）
      payment-timeout: 10000
      # 支付重试次数
      max-retry-times: 2
      # 是否启用异步支付
      enable-async-payment: true
      # 支付队列大小
      payment-queue-size: 1000

# =====================================================
# Spring Boot 性能优化配置
# =====================================================

spring:
  # 数据源配置优化
  datasource:
    hikari:
      # 连接池配置
      minimum-idle: 10
      maximum-pool-size: 50
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1
      # 是否自动提交
      auto-commit: true
      # 连接池名称
      pool-name: WaterfeeHikariCP

  # JPA配置优化
  jpa:
    hibernate:
      # 批量处理大小
      jdbc:
        batch_size: 200
        batch_versioned_data: true
      # 二级缓存
      cache:
        use_second_level_cache: true
        use_query_cache: true
    # 显示SQL（生产环境建议关闭）
    show-sql: false
    properties:
      hibernate:
        # 批量插入优化
        jdbc.batch_size: 200
        order_inserts: true
        order_updates: true
        # 统计信息
        generate_statistics: false

  # 任务调度配置
  task:
    scheduling:
      pool:
        size: 8
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 1000
        keep-alive: 60s

# =====================================================
# MyBatis-Plus 性能优化配置
# =====================================================

mybatis-plus:
  configuration:
    # 缓存配置
    cache-enabled: true
    # 懒加载配置
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    # 执行器类型
    default-executor-type: REUSE
    # 语句超时时间
    default-statement-timeout: 30
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: delFlag
      logic-delete-value: 2
      logic-not-delete-value: 0

# =====================================================
# 日志配置优化
# =====================================================

logging:
  level:
    # 减少不必要的日志输出
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    # 保留重要的业务日志
    org.dromara.waterfee.meterReading: INFO
    org.dromara.waterfee.bill: INFO
  pattern:
    # 优化日志格式，减少I/O开销
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# =====================================================
# JVM 性能优化建议（启动参数）
# =====================================================
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+UnlockExperimentalVMOptions
# -XX:+UseStringDeduplication
# -XX:+OptimizeStringConcat
# -Djava.awt.headless=true
# -Dfile.encoding=UTF-8

# =====================================================
# 监控配置
# =====================================================

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
      sla:
        http.server.requests: 100ms,200ms,500ms,1s,2s

# =====================================================
# 环境特定配置
# =====================================================

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
waterfee:
  bill-processing:
    batch:
      bill-batch-size: 100
      payment-batch-size: 50
    performance:
      min-throughput: 50.0
      enable-auto-optimization: false

---
# 测试环境配置  
spring:
  config:
    activate:
      on-profile: test
waterfee:
  bill-processing:
    batch:
      bill-batch-size: 200
      payment-batch-size: 100
    performance:
      min-throughput: 80.0

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
waterfee:
  bill-processing:
    batch:
      bill-batch-size: 500
      payment-batch-size: 200
      max-concurrent-batches: 12
    concurrency:
      bill-processing-thread-pool-size: 16
      payment-processing-thread-pool-size: 8
    performance:
      min-throughput: 200.0
      max-throughput: 3000.0
      enable-auto-optimization: true
    database:
      enable-read-write-splitting: true
      connection-pool:
        maximum-pool-size: 100
