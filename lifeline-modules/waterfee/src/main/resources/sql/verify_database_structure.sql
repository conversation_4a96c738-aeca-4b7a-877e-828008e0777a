-- =====================================================
-- 数据库结构验证脚本
-- 用于验证实际数据库表结构与索引配置的匹配性
-- =====================================================

-- 1. 验证主要表是否存在
-- =====================================================

SELECT 'Checking table existence...' as status;

-- 检查抄表记录表
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_meter_reading_record';

-- 检查账单表
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_bills';

-- 检查水表表
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_meter';

-- 检查用户表
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_user';

-- 检查支付明细表
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_payment_detail';

-- 2. 验证关键字段是否存在
-- =====================================================

SELECT 'Checking column existence...' as status;

-- 检查抄表记录表字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_meter_reading_record'
AND COLUMN_NAME IN ('meter_no', 'reading_time', 'is_audited', 'meter_type', 'task_id', 'del_flag')
ORDER BY ORDINAL_POSITION;

-- 检查账单表字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_bills'
AND COLUMN_NAME IN ('bill_id', 'customer_id', 'bill_status', 'billing_period_start', 'meter_book_id', 'del_flag')
ORDER BY ORDINAL_POSITION;

-- 检查用户表字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_user'
AND COLUMN_NAME IN ('user_id', 'user_no', 'use_water_nature', 'user_status', 'audit_status', 'del_flag')
ORDER BY ORDINAL_POSITION;

-- 3. 检查现有索引
-- =====================================================

SELECT 'Checking existing indexes...' as status;

-- 检查抄表记录表索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_meter_reading_record'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 检查账单表索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_bills'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 检查用户表索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'waterfee_user'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 4. 生成表结构报告
-- =====================================================

SELECT 'Generating table structure report...' as status;

-- 生成完整的表结构报告
SELECT 
    t.TABLE_NAME,
    t.ENGINE,
    t.TABLE_ROWS,
    t.DATA_LENGTH,
    t.INDEX_LENGTH,
    ROUND(t.DATA_LENGTH/1024/1024, 2) as 'Data Size (MB)',
    ROUND(t.INDEX_LENGTH/1024/1024, 2) as 'Index Size (MB)',
    t.TABLE_COMMENT
FROM information_schema.TABLES t
WHERE t.TABLE_SCHEMA = DATABASE() 
AND t.TABLE_NAME LIKE 'waterfee_%'
ORDER BY t.DATA_LENGTH DESC;

-- 5. 检查外键关系
-- =====================================================

SELECT 'Checking foreign key relationships...' as status;

SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL
AND TABLE_NAME LIKE 'waterfee_%'
ORDER BY TABLE_NAME, CONSTRAINT_NAME;

-- 6. 性能相关统计信息
-- =====================================================

SELECT 'Checking performance statistics...' as status;

-- 检查表的统计信息
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    AVG_ROW_LENGTH,
    DATA_LENGTH,
    INDEX_LENGTH,
    AUTO_INCREMENT,
    UPDATE_TIME
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN (
    'waterfee_meter_reading_record',
    'waterfee_bills', 
    'waterfee_meter',
    'waterfee_user',
    'waterfee_payment_detail'
)
ORDER BY TABLE_ROWS DESC;

-- 7. 建议的索引创建验证
-- =====================================================

SELECT 'Verifying recommended indexes can be created...' as status;

-- 验证索引创建语法（不实际创建）
EXPLAIN 
SELECT * FROM waterfee_meter_reading_record 
WHERE meter_no = 'TEST' AND del_flag = '0' 
ORDER BY reading_time DESC LIMIT 1;

EXPLAIN 
SELECT * FROM waterfee_bills 
WHERE customer_id = 1 AND bill_status = 'PENDING' 
ORDER BY billing_period_start DESC;

EXPLAIN 
SELECT * FROM waterfee_user 
WHERE user_no = 'TEST' AND del_flag = '0';

-- 8. 数据库配置检查
-- =====================================================

SELECT 'Checking database configuration...' as status;

-- 检查重要的数据库配置参数
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'innodb_log_file_size';
SHOW VARIABLES LIKE 'innodb_flush_log_at_trx_commit';
SHOW VARIABLES LIKE 'max_connections';
SHOW VARIABLES LIKE 'query_cache_size';

-- 检查数据库版本
SELECT VERSION() as mysql_version;

-- 9. 存储引擎检查
-- =====================================================

SELECT 'Checking storage engines...' as status;

-- 检查所有水费相关表的存储引擎
SELECT 
    TABLE_NAME,
    ENGINE,
    ROW_FORMAT,
    TABLE_COLLATION
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME LIKE 'waterfee_%'
ORDER BY TABLE_NAME;

-- 10. 字符集检查
-- =====================================================

SELECT 'Checking character sets...' as status;

-- 检查数据库和表的字符集
SELECT 
    SCHEMA_NAME,
    DEFAULT_CHARACTER_SET_NAME,
    DEFAULT_COLLATION_NAME
FROM information_schema.SCHEMATA
WHERE SCHEMA_NAME = DATABASE();

SELECT 
    TABLE_NAME,
    TABLE_COLLATION
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME LIKE 'waterfee_%'
ORDER BY TABLE_NAME;

-- =====================================================
-- 验证完成提示
-- =====================================================

SELECT 'Database structure verification completed!' as status;
SELECT 'Please review the results above before applying performance optimizations.' as note;
SELECT 'If any tables or columns are missing, please check your database schema.' as warning;
