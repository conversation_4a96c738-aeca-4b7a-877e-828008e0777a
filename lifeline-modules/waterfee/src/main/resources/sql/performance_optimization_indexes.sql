-- =====================================================
-- 账单处理流程性能优化 - 数据库索引优化脚本
-- 创建时间: 2025-08-04
-- 说明: 针对账单处理流程的性能瓶颈，创建优化索引
-- =====================================================

-- 1. 抄表记录表优化索引
-- =====================================================

-- 优化按水表编号查询最新记录的性能
CREATE INDEX IF NOT EXISTS idx_meter_reading_meter_no_time 
ON waterfee_meter_reading_record (meter_no, reading_time DESC, record_id DESC);

-- 优化按任务ID和季度查询的性能
CREATE INDEX IF NOT EXISTS idx_meter_reading_task_quarter 
ON waterfee_meter_reading_record (task_id, reading_time, meter_no);

-- 优化审核状态查询的性能
CREATE INDEX IF NOT EXISTS idx_meter_reading_audit_status 
ON waterfee_meter_reading_record (is_audited, audit_time, meter_no);

-- 优化按表册ID查询的性能
CREATE INDEX IF NOT EXISTS idx_meter_reading_book_id 
ON waterfee_meter_reading_record (meter_book_id, reading_time DESC);

-- 复合索引优化批量查询
CREATE INDEX IF NOT EXISTS idx_meter_reading_composite 
ON waterfee_meter_reading_record (meter_no, del_flag, reading_time DESC, is_audited);

-- 2. 账单表优化索引
-- =====================================================

-- 优化按客户ID查询账单的性能
CREATE INDEX IF NOT EXISTS idx_bill_customer_id_status 
ON waterfee_bill (customer_id, bill_status, billing_period_start DESC);

-- 优化按账单状态查询的性能
CREATE INDEX IF NOT EXISTS idx_bill_status_time 
ON waterfee_bill (bill_status, billing_issue_date DESC, bill_id);

-- 优化按表册ID查询账单的性能
CREATE INDEX IF NOT EXISTS idx_bill_meter_book_id 
ON waterfee_bill (meter_book_id, bill_status, billing_period_start DESC);

-- 优化账单审核查询的性能
CREATE INDEX IF NOT EXISTS idx_bill_audit_status 
ON waterfee_bill (audit_status, audit_time DESC, customer_id);

-- 复合索引优化批量账单查询
CREATE INDEX IF NOT EXISTS idx_bill_composite 
ON waterfee_bill (bill_status, del_flag, customer_id, billing_period_start DESC);

-- 3. 水表表优化索引
-- =====================================================

-- 优化按水表编号批量查询的性能
CREATE INDEX IF NOT EXISTS idx_meter_no_type 
ON waterfee_meter (meter_no, meter_type, del_flag);

-- 优化按表册ID查询水表的性能
CREATE INDEX IF NOT EXISTS idx_meter_book_id_status 
ON waterfee_meter (meter_book_id, status, del_flag);

-- 优化按用户编号查询水表的性能
CREATE INDEX IF NOT EXISTS idx_meter_user_no 
ON waterfee_meter (user_no, meter_type, status);

-- 4. 用户表优化索引
-- =====================================================

-- 优化按用户编号查询的性能
CREATE INDEX IF NOT EXISTS idx_user_no_type 
ON waterfee_user (user_no, user_type, del_flag);

-- 优化按用户类型查询的性能
CREATE INDEX IF NOT EXISTS idx_user_type_status 
ON waterfee_user (user_type, status, del_flag);

-- 5. 支付相关表优化索引
-- =====================================================

-- 优化客户账户查询的性能
CREATE INDEX IF NOT EXISTS idx_customer_account_balance 
ON waterfee_customer_account (customer_id, del_flag, account_balance);

-- 优化支付记录查询的性能
CREATE INDEX IF NOT EXISTS idx_payment_record_customer 
ON waterfee_payment_record (customer_id, payment_status, payment_time DESC);

-- 优化按账单ID查询支付记录的性能
CREATE INDEX IF NOT EXISTS idx_payment_record_bill 
ON waterfee_payment_record (bill_id, payment_status, payment_time DESC);

-- 6. 价格配置表优化索引
-- =====================================================

-- 优化按用户类型查询价格配置的性能
CREATE INDEX IF NOT EXISTS idx_price_plan_user_type 
ON waterfee_price_plan (user_type, status, del_flag);

-- 7. 任务相关表优化索引
-- =====================================================

-- 优化抄表任务查询的性能
CREATE INDEX IF NOT EXISTS idx_reading_task_status_cycle 
ON waterfee_reading_task (status, is_cycle, next_execute_time);

-- 优化按表册ID查询任务的性能
CREATE INDEX IF NOT EXISTS idx_reading_task_book_id 
ON waterfee_reading_task (meter_book_id, status, del_flag);

-- 8. 智能表数据表优化索引（如果存在）
-- =====================================================

-- 优化智能表数据查询的性能
CREATE INDEX IF NOT EXISTS idx_intelligent_meter_data 
ON waterfee_intelligent_meter_data (meter_id, reading_time DESC, del_flag);

-- =====================================================
-- 性能优化建议
-- =====================================================

-- 1. 定期维护索引
-- ANALYZE TABLE waterfee_meter_reading_record;
-- ANALYZE TABLE waterfee_bill;
-- ANALYZE TABLE waterfee_meter;

-- 2. 监控慢查询
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 2;

-- 3. 优化MySQL配置参数建议
-- innodb_buffer_pool_size = 70% of RAM
-- innodb_log_file_size = 256M
-- innodb_flush_log_at_trx_commit = 2
-- innodb_flush_method = O_DIRECT
-- query_cache_size = 0 (禁用查询缓存，MySQL 8.0已移除)

-- 4. 分区表建议（对于大数据量）
-- 可以考虑按时间对抄表记录表进行分区
-- ALTER TABLE waterfee_meter_reading_record 
-- PARTITION BY RANGE (YEAR(reading_time)) (
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION pmax VALUES LESS THAN MAXVALUE
-- );

-- =====================================================
-- 索引使用情况监控查询
-- =====================================================

-- 查看索引使用情况
-- SELECT 
--     TABLE_SCHEMA,
--     TABLE_NAME,
--     INDEX_NAME,
--     CARDINALITY,
--     SUB_PART,
--     PACKED,
--     NULLABLE,
--     INDEX_TYPE
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = 'your_database_name'
-- AND TABLE_NAME IN ('waterfee_meter_reading_record', 'waterfee_bill', 'waterfee_meter')
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- 查看未使用的索引
-- SELECT 
--     s.TABLE_SCHEMA,
--     s.TABLE_NAME,
--     s.INDEX_NAME
-- FROM information_schema.STATISTICS s
-- LEFT JOIN performance_schema.table_io_waits_summary_by_index_usage t
--     ON s.TABLE_SCHEMA = t.OBJECT_SCHEMA
--     AND s.TABLE_NAME = t.OBJECT_NAME
--     AND s.INDEX_NAME = t.INDEX_NAME
-- WHERE s.TABLE_SCHEMA = 'your_database_name'
-- AND t.INDEX_NAME IS NULL
-- AND s.INDEX_NAME != 'PRIMARY'
-- ORDER BY s.TABLE_NAME, s.INDEX_NAME;

-- =====================================================
-- 执行计划分析示例
-- =====================================================

-- 分析批量查询最新抄表记录的执行计划
-- EXPLAIN SELECT r1.*
-- FROM waterfee_meter_reading_record r1
-- INNER JOIN (
--     SELECT meter_no, MAX(reading_time) as max_time, MAX(record_id) as max_id
--     FROM waterfee_meter_reading_record
--     WHERE meter_no IN ('M001', 'M002', 'M003')
--     AND del_flag = '0'
--     GROUP BY meter_no
-- ) r2 ON r1.meter_no = r2.meter_no
-- AND r1.reading_time = r2.max_time
-- AND r1.record_id = r2.max_id
-- WHERE r1.del_flag = '0';

-- 分析批量查询账单信息的执行计划
-- EXPLAIN SELECT * FROM waterfee_bill 
-- WHERE bill_id IN (1, 2, 3, 4, 5) 
-- AND del_flag = '0';

-- =====================================================
-- 注意事项
-- =====================================================
-- 1. 创建索引前请先备份数据库
-- 2. 在生产环境中创建索引时，建议在业务低峰期执行
-- 3. 创建索引后请监控查询性能变化
-- 4. 定期检查和清理不必要的索引
-- 5. 根据实际查询模式调整索引策略
