package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaymentDetailsVO {
    /**
     * 发票日期（收款时间）
     */
    @ExcelProperty(value = "收款时间")
    private Date paymentTime;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编码（用水编码）")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用水名称")
    private String userName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 水量
     */
    @ExcelProperty(value = "水量")
    private BigDecimal consumptionVolume;

    /**
     * 水费金额
     */
    @ExcelProperty(value = "水费")
    private Double waterBillOnly;

    /**
     * 水资源费金额
     */
    @ExcelProperty(value = "水资源费")
    private BigDecimal waterResourceTax;

    /**
     * 污水费金额
     */
    @ExcelProperty(value = "污水费")
    private BigDecimal sewageTreatmentFee;

    /**
     * 实收金额
     */
    @ExcelProperty(value = "实收金额")
    private BigDecimal paymentAmount;

    /**
     * 收费员
     */
    @ExcelProperty(value = "收费员")
    private String tollCollector;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式")
    private String paymentMethod;
}
