package org.dromara.waterfee;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 水务营收模块
 *
 * <AUTHOR> Li
 */
@EnableDubbo
@EnableScheduling
@SpringBootApplication
@MapperScan("org.dromara.waterfee.**.mapper")
public class WaterfeeApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(WaterfeeApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  水务营收模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
