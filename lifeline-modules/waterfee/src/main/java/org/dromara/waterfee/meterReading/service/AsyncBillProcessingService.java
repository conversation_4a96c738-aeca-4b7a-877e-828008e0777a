package org.dromara.waterfee.meterReading.service;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 基于消息队列的异步账单处理服务
 * 使用生产者-消费者模式，支持削峰填谷和高并发处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncBillProcessingService {

    private final UltraHighPerformanceBillProcessingService ultraHighPerformanceService;
    private final CachedDataAccessService cachedDataAccessService;

    // 消息队列配置
    private static final String QUEUE_PREFIX = "waterfee:async:queue:";
    private static final String PROCESSING_QUEUE = QUEUE_PREFIX + "processing";
    private static final String RESULT_QUEUE = QUEUE_PREFIX + "result";
    private static final String PRIORITY_QUEUE = QUEUE_PREFIX + "priority";
    private static final String BATCH_QUEUE = QUEUE_PREFIX + "batch";

    // 异步处理配置
    private static final int CONSUMER_THREAD_COUNT = 8;
    private static final int BATCH_SIZE = 500;
    private static final int MAX_QUEUE_SIZE = 50000;
    private static final long PROCESSING_TIMEOUT = 300000; // 5分钟超时

    // 消费者线程池
    private final ExecutorService consumerExecutor = Executors.newFixedThreadPool(
        CONSUMER_THREAD_COUNT,
        r -> {
            Thread t = new Thread(r, "AsyncBillConsumer-" + System.currentTimeMillis());
            t.setDaemon(false);
            return t;
        }
    );

    // 统计信息
    private final AtomicLong totalSubmitted = new AtomicLong(0);
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);

    // 处理状态跟踪
    private final Map<String, ProcessingStatus> processingStatus = new ConcurrentHashMap<>();

    /**
     * 异步提交账单处理任务
     */
    public String submitBillProcessingTask(List<WaterfeeMeterReadingRecord> records, ProcessingPriority priority) {
        if (records.isEmpty()) {
            throw new IllegalArgumentException("记录列表不能为空");
        }

        String taskId = generateTaskId();
        
        try {
            // 创建处理任务
            BillProcessingTask task = new BillProcessingTask();
            task.setTaskId(taskId);
            task.setRecords(records);
            task.setPriority(priority);
            task.setSubmitTime(System.currentTimeMillis());
            task.setStatus(TaskStatus.SUBMITTED);

            // 根据优先级选择队列
            String queueName = priority == ProcessingPriority.HIGH ? PRIORITY_QUEUE : PROCESSING_QUEUE;
            
            // 提交到队列
            RedisUtils.setCacheList(queueName, Collections.singletonList(task));
            
            // 记录处理状态
            ProcessingStatus status = new ProcessingStatus();
            status.setTaskId(taskId);
            status.setTotalRecords(records.size());
            status.setStatus(TaskStatus.SUBMITTED);
            status.setSubmitTime(System.currentTimeMillis());
            processingStatus.put(taskId, status);

            // 预热相关缓存
            List<String> meterNos = records.stream()
                .map(WaterfeeMeterReadingRecord::getMeterNo)
                .collect(Collectors.toList());
            cachedDataAccessService.warmupCache(meterNos);

            totalSubmitted.incrementAndGet();
            
            log.info("异步账单处理任务已提交，任务ID: {}, 记录数量: {}, 优先级: {}", 
                taskId, records.size(), priority);

            return taskId;

        } catch (Exception e) {
            log.error("提交异步账单处理任务失败，任务ID: {}", taskId, e);
            throw new RuntimeException("提交任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量提交账单处理任务
     */
    public List<String> submitBatchBillProcessingTasks(List<WaterfeeMeterReadingRecord> records, int batchSize) {
        if (records.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> taskIds = new ArrayList<>();
        
        // 分批提交
        for (int i = 0; i < records.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, records.size());
            List<WaterfeeMeterReadingRecord> batch = records.subList(i, endIndex);
            
            String taskId = submitBillProcessingTask(batch, ProcessingPriority.NORMAL);
            taskIds.add(taskId);
        }

        log.info("批量提交异步账单处理任务完成，任务数量: {}, 总记录数: {}", 
            taskIds.size(), records.size());

        return taskIds;
    }

    /**
     * 查询任务处理状态
     */
    public ProcessingStatus getTaskStatus(String taskId) {
        ProcessingStatus status = processingStatus.get(taskId);
        if (status == null) {
            // 尝试从Redis获取
            status = RedisUtils.getCacheObject("task_status:" + taskId);
        }
        return status;
    }

    /**
     * 查询多个任务状态
     */
    public Map<String, ProcessingStatus> getBatchTaskStatus(List<String> taskIds) {
        Map<String, ProcessingStatus> result = new HashMap<>();
        
        for (String taskId : taskIds) {
            ProcessingStatus status = getTaskStatus(taskId);
            if (status != null) {
                result.put(taskId, status);
            }
        }
        
        return result;
    }

    /**
     * 启动消费者
     */
    public void startConsumers() {
        log.info("启动异步账单处理消费者，消费者数量: {}", CONSUMER_THREAD_COUNT);

        for (int i = 0; i < CONSUMER_THREAD_COUNT; i++) {
            final int consumerId = i;
            consumerExecutor.submit(() -> {
                Thread.currentThread().setName("BillConsumer-" + consumerId);
                runConsumer(consumerId);
            });
        }
    }

    /**
     * 运行消费者
     */
    private void runConsumer(int consumerId) {
        log.info("消费者 {} 开始运行", consumerId);

        while (!Thread.currentThread().isInterrupted()) {
            try {
                // 优先处理高优先级队列
                BillProcessingTask task = pollTask();
                
                if (task != null) {
                    processTask(task, consumerId);
                } else {
                    // 没有任务时短暂休眠
                    Thread.sleep(1000);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("消费者 {} 处理异常", consumerId, e);
                try {
                    Thread.sleep(5000); // 异常后休眠5秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("消费者 {} 停止运行", consumerId);
    }

    /**
     * 从队列中获取任务
     */
    private BillProcessingTask pollTask() {
        // 优先从高优先级队列获取
        List<BillProcessingTask> priorityTasks = RedisUtils.getCacheList(PRIORITY_QUEUE);
        if (priorityTasks != null && !priorityTasks.isEmpty()) {
            BillProcessingTask task = priorityTasks.get(0);
            // 从队列中移除
            RedisUtils.deleteCacheListValue(PRIORITY_QUEUE, task);
            return task;
        }

        // 从普通队列获取
        List<BillProcessingTask> normalTasks = RedisUtils.getCacheList(PROCESSING_QUEUE);
        if (normalTasks != null && !normalTasks.isEmpty()) {
            BillProcessingTask task = normalTasks.get(0);
            // 从队列中移除
            RedisUtils.deleteCacheListValue(PROCESSING_QUEUE, task);
            return task;
        }

        return null;
    }

    /**
     * 处理任务
     */
    private void processTask(BillProcessingTask task, int consumerId) {
        String taskId = task.getTaskId();
        
        try {
            log.info("消费者 {} 开始处理任务: {}, 记录数量: {}", 
                consumerId, taskId, task.getRecords().size());

            // 更新状态为处理中
            updateTaskStatus(taskId, TaskStatus.PROCESSING, null);

            // 调用极致性能处理服务
            UltraHighPerformanceBillProcessingService.UltraPerformanceResult result = 
                ultraHighPerformanceService.processWithUltraHighPerformance(task.getRecords());

            // 更新处理结果
            ProcessingStatus status = processingStatus.get(taskId);
            if (status != null) {
                status.setStatus(TaskStatus.COMPLETED);
                status.setProcessedCount(result.getProcessedCount());
                status.setSuccessCount(result.getSuccessCount());
                status.setFailCount(result.getFailCount());
                status.setBillsGenerated(result.getBillsGenerated());
                status.setPaymentsSucceeded(result.getPaymentsSucceeded());
                status.setPaymentsFailed(result.getPaymentsFailed());
                status.setProcessingTime(result.getProcessingTimeMillis());
                status.setThroughput(result.getThroughputPerSecond());
                status.setCompleteTime(System.currentTimeMillis());
                status.setErrorMessages(result.getErrorMessages());

                // 持久化状态到Redis
                RedisUtils.setCacheObject("task_status:" + taskId, status, Duration.ofHours(24));
            }

            totalProcessed.incrementAndGet();

            log.info("消费者 {} 完成任务处理: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                consumerId, taskId, result.getSuccessCount(), result.getFailCount(), 
                result.getProcessingTimeMillis());

        } catch (Exception e) {
            log.error("消费者 {} 处理任务失败: {}", consumerId, taskId, e);
            
            // 更新状态为失败
            updateTaskStatus(taskId, TaskStatus.FAILED, e.getMessage());
            totalFailed.incrementAndGet();
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, TaskStatus status, String errorMessage) {
        ProcessingStatus processingStatus = this.processingStatus.get(taskId);
        if (processingStatus != null) {
            processingStatus.setStatus(status);
            if (errorMessage != null) {
                processingStatus.getErrorMessages().add(errorMessage);
            }
            
            // 异步持久化到Redis
            CompletableFuture.runAsync(() -> {
                RedisUtils.setCacheObject("task_status:" + taskId, processingStatus, Duration.ofHours(24));
            });
        }
    }

    /**
     * 获取队列统计信息
     */
    public QueueStats getQueueStats() {
        QueueStats stats = new QueueStats();
        
        List<BillProcessingTask> priorityTasks = RedisUtils.getCacheList(PRIORITY_QUEUE);
        List<BillProcessingTask> normalTasks = RedisUtils.getCacheList(PROCESSING_QUEUE);
        
        stats.setPriorityQueueSize(priorityTasks != null ? priorityTasks.size() : 0);
        stats.setNormalQueueSize(normalTasks != null ? normalTasks.size() : 0);
        stats.setTotalSubmitted(totalSubmitted.get());
        stats.setTotalProcessed(totalProcessed.get());
        stats.setTotalFailed(totalFailed.get());
        stats.setProcessingTasks(processingStatus.size());
        
        return stats;
    }

    /**
     * 清理过期任务状态
     */
    public void cleanupExpiredTasks() {
        long now = System.currentTimeMillis();
        List<String> expiredTasks = new ArrayList<>();
        
        for (Map.Entry<String, ProcessingStatus> entry : processingStatus.entrySet()) {
            ProcessingStatus status = entry.getValue();
            if (now - status.getSubmitTime() > PROCESSING_TIMEOUT) {
                expiredTasks.add(entry.getKey());
            }
        }
        
        for (String taskId : expiredTasks) {
            processingStatus.remove(taskId);
        }
        
        if (!expiredTasks.isEmpty()) {
            log.info("清理过期任务状态，数量: {}", expiredTasks.size());
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "BILL_TASK_" + System.currentTimeMillis() + "_" + 
               Thread.currentThread().getId() + "_" + 
               (int)(Math.random() * 1000);
    }

    /**
     * 账单处理任务
     */
    @Data
    public static class BillProcessingTask {
        private String taskId;
        private List<WaterfeeMeterReadingRecord> records;
        private ProcessingPriority priority;
        private long submitTime;
        private TaskStatus status;
    }

    /**
     * 处理状态
     */
    @Data
    public static class ProcessingStatus {
        private String taskId;
        private int totalRecords;
        private int processedCount;
        private int successCount;
        private int failCount;
        private int billsGenerated;
        private int paymentsSucceeded;
        private int paymentsFailed;
        private TaskStatus status;
        private long submitTime;
        private long startTime;
        private long completeTime;
        private long processingTime;
        private double throughput;
        private List<String> errorMessages = new ArrayList<>();
    }

    /**
     * 队列统计信息
     */
    @Data
    public static class QueueStats {
        private int priorityQueueSize;
        private int normalQueueSize;
        private long totalSubmitted;
        private long totalProcessed;
        private long totalFailed;
        private int processingTasks;
    }

    /**
     * 处理优先级
     */
    public enum ProcessingPriority {
        HIGH,    // 高优先级
        NORMAL,  // 普通优先级
        LOW      // 低优先级
    }

    /**
     * 任务状态
     */
    public enum TaskStatus {
        SUBMITTED,  // 已提交
        PROCESSING, // 处理中
        COMPLETED,  // 已完成
        FAILED      // 失败
    }
}
