package org.dromara.waterfee.meterReading.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeReadingTaskBo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo;
import org.dromara.waterfee.meterReading.service.IWaterfeeReadingTaskService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 抄表任务
 * 前端访问路由地址为:/waterfee/readingTask
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/readingTask")
public class WaterfeeReadingTaskController extends BaseController {

    private final IWaterfeeReadingTaskService waterfeeReadingTaskService;

    /**
     * 查询抄表任务列表
     */
    @SaCheckPermission("waterfee:readingTask:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeReadingTaskVo> list(WaterfeeReadingTaskBo bo, PageQuery pageQuery) {
        return waterfeeReadingTaskService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出抄表任务列表
     */
    @SaCheckPermission("waterfee:readingTask:export")
    @Log(title = "抄表任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeReadingTaskBo bo, HttpServletResponse response) {
        List<WaterfeeReadingTaskVo> list = waterfeeReadingTaskService.queryList(bo);
        ExcelUtil.exportExcel(list, "抄表任务", WaterfeeReadingTaskVo.class, response);
    }

    /**
     * 获取抄表任务详细信息
     *
     * @param taskId 主键
     */
    @SaCheckPermission("waterfee:readingTask:query")
    @GetMapping("/{taskId}")
    public R<WaterfeeReadingTaskVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long taskId) {
        return R.ok(waterfeeReadingTaskService.queryById(taskId));
    }

    /**
     * 新增抄表任务
     */
    @SaCheckPermission("waterfee:readingTask:add")
    @Log(title = "抄表任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated @RequestBody WaterfeeReadingTaskBo bo) {
        return toAjax(waterfeeReadingTaskService.insertByBo(bo));
    }

    /**
     * 修改抄表任务
     */
    @SaCheckPermission("waterfee:readingTask:edit")
    @Log(title = "抄表任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated @RequestBody WaterfeeReadingTaskBo bo) {
        return toAjax(waterfeeReadingTaskService.updateByBo(bo));
    }

    /**
     * 删除抄表任务
     *
     * @param taskIds 主键串
     */
    @SaCheckPermission("waterfee:readingTask:remove")
    @Log(title = "抄表任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] taskIds) {
        return toAjax(waterfeeReadingTaskService.deleteWithValidByIds(List.of(taskIds), true));
    }

    /**
     * 暂停抄表任务
     */
    @SaCheckPermission("waterfee:readingTask:pause")
    @Log(title = "暂停抄表任务", businessType = BusinessType.UPDATE)
    @PutMapping("/pause/{taskId}")
    public R<Void> pauseTask(@NotNull(message = "任务ID不能为空") @PathVariable Long taskId) {
        return toAjax(waterfeeReadingTaskService.pauseTask(taskId));
    }

    /**
     * 启用抄表任务
     */
    @SaCheckPermission("waterfee:readingTask:enable")
    @Log(title = "启用抄表任务", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{taskId}")
    public R<Void> enableTask(@NotNull(message = "任务ID不能为空") @PathVariable Long taskId) {
        return toAjax(waterfeeReadingTaskService.enableTask(taskId));
    }

    /**
     * 下发抄表任务
     */
    @SaCheckPermission("waterfee:readingTask:dispatch")
    @Log(title = "下发抄表任务", businessType = BusinessType.UPDATE)
    @PutMapping("/dispatch/{taskId}")
    public R<Void> dispatchTask(@NotNull(message = "任务ID不能为空") @PathVariable Long taskId) {
        return toAjax(waterfeeReadingTaskService.dispatchTask(taskId));
    }

//    /**
//     * 更新表册关联的用户数和水表数
//     */
//    @SaCheckPermission("waterfee:readingTask:edit")
//    @Log(title = "更新表册关联数据", businessType = BusinessType.UPDATE)
//    @PutMapping("/updateCount/{taskId}")
//    public R<Void> updateMeterAndUserCount(@NotNull(message = "任务ID不能为空") @PathVariable Long taskId) {
//        return toAjax(waterfeeReadingTaskService.updateMeterAndUserCount(taskId));
//    }

    /**
     * 批量更新表册关联的用户数和水表数
     */
    @SaCheckPermission("waterfee:readingTask:edit")
    @Log(title = "批量更新表册关联数据", businessType = BusinessType.UPDATE)
    @PutMapping("/updateCount/batch/{taskIds}")
    public R<Void> updateMeterAndUserCountBatch(@NotEmpty(message = "任务ID不能为空") @PathVariable Long[] taskIds) {
        return toAjax(waterfeeReadingTaskService.updateMeterAndUserCountBatch(List.of(taskIds)));
    }
}
