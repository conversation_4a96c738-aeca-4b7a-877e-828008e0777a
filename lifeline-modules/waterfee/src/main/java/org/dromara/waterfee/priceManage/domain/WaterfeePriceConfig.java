package org.dromara.waterfee.priceManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 阶梯价格配置对象 waterfee_price_config
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_price_config")
public class WaterfeePriceConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 价格名称 (例如：居民阶梯价)
     */
    private String name;

    /**
     * 用水性质 (例如：'1' 代表居民)
     */
    private String waterUseType;

    /**
     * 计算方式 (例如：按年、按月、按人口)
     */
    private String calculationMethod;

    /**
     * 是否按人口计算 (0:否, 1:是)
     */
    private Long isPopulation;

    /**
     * 人口数量 (当is_population为1时有效)
     */
    private Long populationCount;

    /**
     * 描述
     */
    private String description;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
