package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArAdjustment;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArAdjustmentBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 应收账追补记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManageArAdjustmentService {

    /**
     * 查询应收账追补记录
     *
     * @param id 主键
     * @return 应收账追补记录
     */
    WaterfeeChargeManageArAdjustment queryById(Long id);

    /**
     * 分页查询应收账追补记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收账追补记录分页列表
     */
    TableDataInfo<WaterfeeChargeManageArAdjustmentVo> queryPageList(WaterfeeChargeManageArAdjustmentBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应收账追补记录列表
     *
     * @param bo 查询条件
     * @return 应收账追补记录列表
     */
    List<WaterfeeChargeManageArAdjustmentVo> queryList(WaterfeeChargeManageArAdjustmentBo bo);

    /**
     * 新增应收账追补记录
     *
     * @param bo 应收账追补记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManageArAdjustmentBo bo);

    /**
     * 修改应收账追补记录
     *
     * @param bo 应收账追补记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManageArAdjustmentBo bo);

    /**
     * 校验并批量删除应收账追补记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
