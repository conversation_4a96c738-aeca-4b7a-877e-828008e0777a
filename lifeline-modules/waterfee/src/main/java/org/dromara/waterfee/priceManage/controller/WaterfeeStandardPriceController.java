package org.dromara.waterfee.priceManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.waterfee.priceManage.domain.WaterfeeStandardPrice;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeStandardPriceBo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeStandardPriceVo;
import org.dromara.waterfee.priceManage.service.IWaterfeeStandardPriceService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 标准价格
 * 前端访问路由地址为:/waterfee/standardPrice
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/standardPrice")
public class WaterfeeStandardPriceController extends BaseController {

    private final IWaterfeeStandardPriceService waterfeeStandardPriceService;

    /**
     * 查询标准价格列表
     */
    @SaCheckPermission("waterfee:standardPrice:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeStandardPriceVo> list(WaterfeeStandardPriceBo bo, PageQuery pageQuery) {
        return waterfeeStandardPriceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出标准价格列表
     */
    @SaCheckPermission("waterfee:standardPrice:export")
    @Log(title = "标准价格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeStandardPriceBo bo, HttpServletResponse response) {
        List<WaterfeeStandardPriceVo> list = waterfeeStandardPriceService.queryList(bo);
        ExcelUtil.exportExcel(list, "标准价格", WaterfeeStandardPriceVo.class, response);
    }

    /**
     * 获取标准价格详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:standardPrice:query")
    @GetMapping("/{id}")
    public R<WaterfeeStandardPrice> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeStandardPriceService.queryById(id));
    }

    /**
     * 新增标准价格
     */
    @SaCheckPermission("waterfee:standardPrice:add")
    @Log(title = "标准价格", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeStandardPriceBo bo) {
        return toAjax(waterfeeStandardPriceService.insertByBo(bo));
    }

    /**
     * 修改标准价格
     */
    @SaCheckPermission("waterfee:standardPrice:edit")
    @Log(title = "标准价格", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeStandardPriceBo bo) {
        return toAjax(waterfeeStandardPriceService.updateByBo(bo));
    }

    /**
     * 删除标准价格
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:standardPrice:remove")
    @Log(title = "标准价格", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeStandardPriceService.deleteWithValidByIds(List.of(ids), true));
    }
}
