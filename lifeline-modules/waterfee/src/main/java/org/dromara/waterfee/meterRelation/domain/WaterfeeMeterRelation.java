package org.dromara.waterfee.meterRelation.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:05
 **/

@Data
@TableName("waterfee_meter_relation")
public class WaterfeeMeterRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "总表ID")
    private Long parentMeterId;

    @Schema(description = "分表ID")
    private Long childMeterId;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "删除标志（0-正常 2-删除）")
    private String delFlag;
}
