package org.dromara.waterfee.community.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.community.domain.bo.CommunityBo;
import org.dromara.waterfee.community.domain.vo.CommunityVo;
import org.dromara.waterfee.community.service.ICommunityService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社区管理
 * 前端访问路由地址为:/waterfee/community
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/community")
public class CommunityController extends BaseController {

    private final ICommunityService communityService;

    /**
     * 查询社区列表
     */
    @SaCheckPermission("waterfee:community:list")
    @GetMapping("/list")
    public TableDataInfo<CommunityVo> list(CommunityBo bo, PageQuery pageQuery) {
        return communityService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询社区下拉列表
     */
    @SaCheckPermission("waterfee:community:list")
    @GetMapping("/getSelectList")
    public R list(CommunityBo bo) {
        return R.ok(communityService.queryList(bo));
    }

    /**
     * 导出社区列表
     */
    @SaCheckPermission("waterfee:community:export")
    @Log(title = "社区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CommunityBo bo, HttpServletResponse response) {
        List<CommunityVo> list = communityService.queryList(bo);
        ExcelUtil.exportExcel(list, "社区管理", CommunityVo.class, response);
    }

    /**
     * 获取社区详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:community:query")
    @GetMapping("/{id}")
    public R<CommunityVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable("id") Long id) {
        return R.ok(communityService.queryById(id));
    }

    /**
     * 新增社区
     */
    @SaCheckPermission("waterfee:community:add")
    @Log(title = "社区管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CommunityBo bo) {
        return toAjax(communityService.insertByBo(bo));
    }

    /**
     * 修改社区
     */
    @SaCheckPermission("waterfee:community:edit")
    @Log(title = "社区管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CommunityBo bo) {
        return toAjax(communityService.updateByBo(bo));
    }

    /**
     * 删除社区
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:community:remove")
    @Log(title = "社区管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(communityService.deleteWithValidByIds(List.of(ids), true));
    }
}
