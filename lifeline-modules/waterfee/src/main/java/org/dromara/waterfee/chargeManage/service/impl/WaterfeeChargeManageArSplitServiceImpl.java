package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArSplitBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArSplitVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArSplit;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageArSplitMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageArSplitService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 应收账分账记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManageArSplitServiceImpl implements IWaterfeeChargeManageArSplitService {

    private final WaterfeeChargeManageArSplitMapper baseMapper;

    /**
     * 查询应收账分账记录
     *
     * @param id 主键
     * @return 应收账分账记录
     */
    @Override
    public WaterfeeChargeManageArSplit queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询应收账分账记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收账分账记录分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManageArSplitVo> queryPageList(WaterfeeChargeManageArSplitBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManageArSplit> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManageArSplitVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应收账分账记录列表
     *
     * @param bo 查询条件
     * @return 应收账分账记录列表
     */
    @Override
    public List<WaterfeeChargeManageArSplitVo> queryList(WaterfeeChargeManageArSplitBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManageArSplit> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManageArSplit> buildQueryWrapper(WaterfeeChargeManageArSplitBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManageArSplit> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManageArSplit::getCreateTime);
        lqw.eq(bo.getBillId() != null, WaterfeeChargeManageArSplit::getBillId, bo.getBillId());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectCode()), WaterfeeChargeManageArSplit::getProjectCode, bo.getProjectCode());
        lqw.eq(bo.getDepartmentId() != null, WaterfeeChargeManageArSplit::getDepartmentId, bo.getDepartmentId());
        lqw.eq(bo.getSplitAmount() != null, WaterfeeChargeManageArSplit::getSplitAmount, bo.getSplitAmount());
        return lqw;
    }

    /**
     * 新增应收账分账记录
     *
     * @param bo 应收账分账记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManageArSplitBo bo) {
        WaterfeeChargeManageArSplit add = MapstructUtils.convert(bo, WaterfeeChargeManageArSplit.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改应收账分账记录
     *
     * @param bo 应收账分账记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManageArSplitBo bo) {
        WaterfeeChargeManageArSplit update = MapstructUtils.convert(bo, WaterfeeChargeManageArSplit.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManageArSplit entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除应收账分账记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
