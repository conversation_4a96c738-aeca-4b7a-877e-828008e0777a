package org.dromara.waterfee.meterReading.service;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.mapper.OptimizedBatchMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 高性能账单处理服务
 * 使用最新的优化技术提供极致的账单处理性能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HighPerformanceBillProcessingService {

    private final IMeterReadingRecordService meterReadingRecordService;
    private final IWaterfeeBillService waterfeeBillService;
    private final IWaterfeeCounterPaymentService waterfeeCounterPaymentService;
    private final OptimizedBatchMapper optimizedBatchMapper;
    private final BillProcessingPerformanceManager performanceManager;

    // 性能优化配置
    private static final int BILL_BATCH_SIZE = 300; // 账单批处理大小
    private static final int PAYMENT_BATCH_SIZE = 150; // 支付批处理大小
    private static final int MAX_CONCURRENT_BATCHES = 8; // 最大并发批次数
    private static final int QUERY_BATCH_SIZE = 500; // 查询批处理大小

    /**
     * 超高性能批量账单处理
     * 使用流式处理、批量操作、异步并发等技术
     */
    public HighPerformanceResult ultraHighPerformanceBatchProcessBills(List<WaterfeeMeterReadingRecord> records) {
        long startTime = System.currentTimeMillis();
        HighPerformanceResult result = new HighPerformanceResult();
        result.setTotalRecords(records.size());

        if (records.isEmpty()) {
            return result;
        }

        try {
            log.info("开始超高性能批量账单处理，记录数量: {}", records.size());

            // 开始性能监控
            String processId = "BILL_PROCESS_" + System.currentTimeMillis();
            performanceManager.recordProcessingStart(processId, records.size());

            // 1. 预处理：分组和过滤
            Map<Boolean, List<WaterfeeMeterReadingRecord>> groupedRecords = records.stream()
                .collect(Collectors.groupingBy(record -> "2".equals(record.getMeterType())));

            List<WaterfeeMeterReadingRecord> intelligentRecords = groupedRecords.getOrDefault(true, new ArrayList<>());
            List<WaterfeeMeterReadingRecord> mechanicalRecords = groupedRecords.getOrDefault(false, new ArrayList<>());

            // 2. 并行处理智能表和机械表
            CompletableFuture<HighPerformanceResult> intelligentFuture = CompletableFuture.supplyAsync(() ->
                processIntelligentMeterBillsUltraFast(intelligentRecords));

            CompletableFuture<HighPerformanceResult> mechanicalFuture = CompletableFuture.supplyAsync(() ->
                processMechanicalMeterBillsUltraFast(mechanicalRecords));

            // 3. 等待并合并结果
            HighPerformanceResult intelligentResult = intelligentFuture.join();
            HighPerformanceResult mechanicalResult = mechanicalFuture.join();

            mergeResults(result, intelligentResult, mechanicalResult);

            result.setProcessingTime(System.currentTimeMillis() - startTime);

            // 4. 性能分析
            analyzePerformance(result);

            // 记录性能监控结果
            performanceManager.recordProcessingComplete(processId,
                result.getTotalRecords() - result.getErrorMessages().size(),
                result.getErrorMessages().size(),
                result.getBillsGenerated(),
                result.getPaymentsSucceeded());

            log.info("超高性能批量账单处理完成 - 总数: {}, 账单生成: {}, 审核: {}, 支付成功: {}, 耗时: {}ms, 吞吐量: {}/s",
                result.getTotalRecords(), result.getBillsGenerated(), result.getBillsAudited(),
                result.getPaymentsSucceeded(), result.getProcessingTime(),
                result.getProcessingTime() > 0 ? (result.getTotalRecords() * 1000 / result.getProcessingTime()) : 0);

        } catch (Exception e) {
            log.error("超高性能批量账单处理异常", e);
            result.getErrorMessages().add("批量账单处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 超快速智能表账单处理
     */
    private HighPerformanceResult processIntelligentMeterBillsUltraFast(List<WaterfeeMeterReadingRecord> records) {
        HighPerformanceResult result = new HighPerformanceResult();
        if (records.isEmpty()) {
            return result;
        }

        log.info("开始超快速处理智能表账单，数量: {}", records.size());

        try {
            // 1. 流式批量审核记录
            Map<Long, List<Long>> recordBillMap = streamBatchAuditRecords(records);

            // 2. 批量获取账单信息（使用优化的批量查询）
            Set<Long> allBillIds = recordBillMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

            Map<Long, WaterfeeBillVo> billMap = ultraFastBatchGetBillInfo(new ArrayList<>(allBillIds));

            // 3. 超快速批量处理自动支付
            result = ultraFastBatchProcessAutoPayments(records, recordBillMap, billMap);

            // 4. 统计账单生成和审核
            result.setBillsGenerated(recordBillMap.size());
            result.setBillsAudited(recordBillMap.size());

            log.info("智能表账单处理完成，生成: {}, 审核: {}, 支付成功: {}",
                result.getBillsGenerated(), result.getBillsAudited(), result.getPaymentsSucceeded());

        } catch (Exception e) {
            log.error("智能表账单处理异常", e);
            result.getErrorMessages().add("智能表账单处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 超快速机械表账单处理
     */
    private HighPerformanceResult processMechanicalMeterBillsUltraFast(List<WaterfeeMeterReadingRecord> records) {
        HighPerformanceResult result = new HighPerformanceResult();
        if (records.isEmpty()) {
            return result;
        }

        log.info("开始超快速处理机械表账单，数量: {}", records.size());

        try {
            // 流式批量审核记录（机械表不需要自动支付）
            Map<Long, List<Long>> recordBillMap = streamBatchAuditRecords(records);

            result.setBillsGenerated(recordBillMap.size());
            result.setBillsAudited(recordBillMap.size());

            log.info("机械表账单处理完成，生成: {}, 审核: {}",
                result.getBillsGenerated(), result.getBillsAudited());

        } catch (Exception e) {
            log.error("机械表账单处理异常", e);
            result.getErrorMessages().add("机械表账单处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 流式批量审核记录
     * 使用流式处理和批量操作优化性能
     */
    private Map<Long, List<Long>> streamBatchAuditRecords(List<WaterfeeMeterReadingRecord> records) {
        Map<Long, List<Long>> recordBillMap = new ConcurrentHashMap<>();

        // 分批处理以避免内存压力和数据库压力
        List<List<WaterfeeMeterReadingRecord>> batches = partitionList(records, BILL_BATCH_SIZE);

        // 限制并发数量，避免过度并发
        List<CompletableFuture<Void>> futures = batches.stream()
            .limit(MAX_CONCURRENT_BATCHES)
            .map(batch -> CompletableFuture.runAsync(() -> {
                processAuditBatchOptimized(batch, recordBillMap);
            }))
            .collect(Collectors.toList());

        // 处理剩余批次
        if (batches.size() > MAX_CONCURRENT_BATCHES) {
            for (int i = MAX_CONCURRENT_BATCHES; i < batches.size(); i++) {
                processAuditBatchOptimized(batches.get(i), recordBillMap);
            }
        }

        // 等待并发批次完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return recordBillMap;
    }

    /**
     * 优化的审核批次处理
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void processAuditBatchOptimized(List<WaterfeeMeterReadingRecord> batch, Map<Long, List<Long>> recordBillMap) {
        for (WaterfeeMeterReadingRecord record : batch) {
            try {
                List<Long> billIds = meterReadingRecordService.auditRecordRtnBillIds(record);
                if (billIds != null && !billIds.isEmpty()) {
                    recordBillMap.put(record.getRecordId(), billIds);
                }
            } catch (Exception e) {
                log.error("审核记录失败，记录ID: {}", record.getRecordId(), e);
            }
        }
    }

    /**
     * 超快速批量获取账单信息
     */
    private Map<Long, WaterfeeBillVo> ultraFastBatchGetBillInfo(List<Long> billIds) {
        if (billIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 使用优化的批量查询
            List<WaterfeeBillVo> bills = optimizedBatchMapper.batchSelectBillsByIds(billIds);
            return bills.stream()
                .collect(Collectors.toMap(
                    WaterfeeBillVo::getBillId,
                    bill -> bill,
                    (existing, replacement) -> existing
                ));
        } catch (Exception e) {
            log.error("批量获取账单信息失败，回退到服务层查询", e);
            // 回退到服务层批量查询
            try {
                List<WaterfeeBillVo> bills = waterfeeBillService.queryByIds(billIds);
                return bills.stream()
                    .collect(Collectors.toMap(
                        WaterfeeBillVo::getBillId,
                        bill -> bill,
                        (existing, replacement) -> existing
                    ));
            } catch (Exception ex) {
                log.error("服务层批量查询也失败", ex);
                return new HashMap<>();
            }
        }
    }

    /**
     * 超快速批量处理自动支付
     */
    private HighPerformanceResult ultraFastBatchProcessAutoPayments(List<WaterfeeMeterReadingRecord> records,
                                                                   Map<Long, List<Long>> recordBillMap,
                                                                   Map<Long, WaterfeeBillVo> billMap) {
        HighPerformanceResult result = new HighPerformanceResult();

        // 过滤出需要自动支付的记录
        List<WaterfeeMeterReadingRecord> paymentRecords = records.stream()
            .filter(record -> "1".equals(record.getIsAudited()))
            .filter(record -> recordBillMap.containsKey(record.getRecordId()))
            .collect(Collectors.toList());

        if (paymentRecords.isEmpty()) {
            return result;
        }

        log.info("开始超快速批量处理自动支付，数量: {}", paymentRecords.size());

        // 分批处理支付
        List<List<WaterfeeMeterReadingRecord>> paymentBatches = partitionList(paymentRecords, PAYMENT_BATCH_SIZE);

        AtomicInteger totalProcessed = new AtomicInteger(0);
        AtomicInteger totalSucceeded = new AtomicInteger(0);
        AtomicInteger totalFailed = new AtomicInteger(0);

        // 限制并发支付批次
        List<CompletableFuture<Void>> futures = paymentBatches.stream()
            .limit(MAX_CONCURRENT_BATCHES / 2) // 支付操作更谨慎，减少并发数
            .map(batch -> CompletableFuture.runAsync(() -> {
                processPaymentBatchOptimized(batch, recordBillMap, billMap, totalProcessed, totalSucceeded, totalFailed);
            }))
            .collect(Collectors.toList());

        // 处理剩余支付批次（串行）
        if (paymentBatches.size() > MAX_CONCURRENT_BATCHES / 2) {
            for (int i = MAX_CONCURRENT_BATCHES / 2; i < paymentBatches.size(); i++) {
                processPaymentBatchOptimized(paymentBatches.get(i), recordBillMap, billMap,
                    totalProcessed, totalSucceeded, totalFailed);
            }
        }

        // 等待所有支付批次完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        result.setPaymentsProcessed(totalProcessed.get());
        result.setPaymentsSucceeded(totalSucceeded.get());
        result.setPaymentsFailed(totalFailed.get());

        log.info("超快速批量自动支付完成，处理: {}, 成功: {}, 失败: {}",
            totalProcessed.get(), totalSucceeded.get(), totalFailed.get());

        return result;
    }

    /**
     * 优化的支付批次处理
     */
    private void processPaymentBatchOptimized(List<WaterfeeMeterReadingRecord> batch,
                                            Map<Long, List<Long>> recordBillMap,
                                            Map<Long, WaterfeeBillVo> billMap,
                                            AtomicInteger totalProcessed,
                                            AtomicInteger totalSucceeded,
                                            AtomicInteger totalFailed) {

        for (WaterfeeMeterReadingRecord record : batch) {
            try {
                List<Long> billIds = recordBillMap.get(record.getRecordId());
                if (billIds == null || billIds.isEmpty()) {
                    continue;
                }

                WaterfeeBillVo bill = billMap.get(billIds.get(0));
                if (bill == null) {
                    totalFailed.incrementAndGet();
                    continue;
                }

                totalProcessed.incrementAndGet();

                boolean success = waterfeeCounterPaymentService.autoPayBillByDeposit(
                    bill.getCustomerId(),
                    billIds,
                    bill.getTotalAmount(),
                    "智能表自动扣款：" + bill.getBillNumber()
                );

                if (success) {
                    totalSucceeded.incrementAndGet();
                } else {
                    totalFailed.incrementAndGet();
                }

            } catch (Exception e) {
                totalFailed.incrementAndGet();
                log.error("自动支付异常，水表编号: {}", record.getMeterNo(), e);
            }
        }
    }

    /**
     * 性能分析
     */
    private void analyzePerformance(HighPerformanceResult result) {
        if (result.getProcessingTime() > 0) {
            double throughput = (double) result.getTotalRecords() * 1000 / result.getProcessingTime();
            result.setThroughput(throughput);

            if (throughput > 1000) {
                result.setPerformanceLevel("EXCELLENT");
            } else if (throughput > 500) {
                result.setPerformanceLevel("GOOD");
            } else if (throughput > 200) {
                result.setPerformanceLevel("NORMAL");
            } else {
                result.setPerformanceLevel("POOR");
            }
        }
    }

    /**
     * 合并处理结果
     */
    private void mergeResults(HighPerformanceResult target, HighPerformanceResult... sources) {
        for (HighPerformanceResult source : sources) {
            target.setBillsGenerated(target.getBillsGenerated() + source.getBillsGenerated());
            target.setBillsAudited(target.getBillsAudited() + source.getBillsAudited());
            target.setPaymentsProcessed(target.getPaymentsProcessed() + source.getPaymentsProcessed());
            target.setPaymentsSucceeded(target.getPaymentsSucceeded() + source.getPaymentsSucceeded());
            target.setPaymentsFailed(target.getPaymentsFailed() + source.getPaymentsFailed());
            target.getErrorMessages().addAll(source.getErrorMessages());
        }
    }

    /**
     * 分割列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    /**
     * 高性能处理结果
     */
    @Data
    public static class HighPerformanceResult {
        private int totalRecords = 0;
        private int billsGenerated = 0;
        private int billsAudited = 0;
        private int paymentsProcessed = 0;
        private int paymentsSucceeded = 0;
        private int paymentsFailed = 0;
        private long processingTime = 0;
        private double throughput = 0.0; // 吞吐量（记录数/秒）
        private String performanceLevel = "UNKNOWN"; // 性能等级
        private List<String> errorMessages = new ArrayList<>();
    }
}
