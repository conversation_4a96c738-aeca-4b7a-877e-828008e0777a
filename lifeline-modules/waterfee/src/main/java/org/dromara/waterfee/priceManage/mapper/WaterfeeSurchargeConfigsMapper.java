package org.dromara.waterfee.priceManage.mapper;

import org.dromara.waterfee.priceManage.domain.WaterfeeSurchargeConfigs;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeSurchargeConfigsVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;

/**
 * 附加费配置 (Surcharge Configurations)Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface WaterfeeSurchargeConfigsMapper extends BaseMapperPlus<WaterfeeSurchargeConfigs, WaterfeeSurchargeConfigsVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeSurchargeConfigsVo>> P selectVoPage(IPage<WaterfeeSurchargeConfigs> page, Wrapper<WaterfeeSurchargeConfigs> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

}
