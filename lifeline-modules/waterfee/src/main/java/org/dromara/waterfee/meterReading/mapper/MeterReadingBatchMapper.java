package org.dromara.waterfee.meterReading.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;

import java.util.List;
import java.util.Map;

/**
 * 抄表记录批量操作Mapper接口
 * 专门用于优化批量数据库操作
 */
@Mapper
public interface MeterReadingBatchMapper {

    /**
     * 批量获取水表信息
     *
     * @param meterNos 水表编号列表
     * @return 水表信息列表
     */
    List<WaterfeeMeterVo> batchSelectMetersByNos(@Param("meterNos") List<String> meterNos);

    /**
     * 批量获取最新抄表记录
     *
     * @param meterNos 水表编号列表
     * @return 最新抄表记录列表
     */
    List<WaterfeeMeterReadingRecord> batchSelectLatestRecords(@Param("meterNos") List<String> meterNos);

    /**
     * 批量插入抄表记录
     *
     * @param records 抄表记录列表
     * @return 插入成功的记录数
     */
    int batchInsertReadingRecords(@Param("records") List<WaterfeeMeterReadingRecord> records);

    /**
     * 批量检查季度重复记录
     *
     * @param meterNos 水表编号列表
     * @param taskId   任务ID
     * @param year     年份
     * @param quarter  季度
     * @return 已存在记录的水表编号列表
     */
    List<String> batchCheckQuarterDuplicates(@Param("meterNos") List<String> meterNos,
                                             @Param("taskId") Long taskId,
                                             @Param("year") int year,
                                             @Param("quarter") int quarter);

    /**
     * 批量获取水表统计信息
     *
     * @param meterBookIds 表册ID列表
     * @return 统计信息
     */
    @MapKey("meter_book_id")
    List<Map<String, Object>> batchGetMeterStatistics(@Param("meterBookIds") List<Long> meterBookIds);

    /**
     * 批量更新抄表记录状态
     *
     * @param recordIds 记录ID列表
     * @param status    状态
     * @return 更新成功的记录数
     */
    int batchUpdateRecordStatus(@Param("recordIds") List<Long> recordIds, @Param("status") String status);

    /**
     * 批量删除抄表记录（逻辑删除）
     *
     * @param recordIds 记录ID列表
     * @return 删除成功的记录数
     */
    int batchDeleteRecords(@Param("recordIds") List<Long> recordIds);

    /**
     * 获取指定时间范围内的抄表记录统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    Map<String, Object> getReadingStatistics(@Param("startTime") String startTime,
                                             @Param("endTime") String endTime);

    /**
     * 获取任务执行统计
     *
     * @param taskIds 任务ID列表
     * @return 任务统计结果
     */
    @MapKey("task_id")
    List<Map<String, Object>> batchGetTaskStatistics(@Param("taskIds") List<Long> taskIds);

    /**
     * 批量获取水表的最近N次抄表记录
     *
     * @param meterNos 水表编号列表
     * @param limit    记录数限制
     * @return 抄表记录列表
     */
    List<WaterfeeMeterReadingRecord> batchGetRecentRecords(@Param("meterNos") List<String> meterNos,
                                                           @Param("limit") int limit);

    /**
     * 批量检查水表是否存在未完成的抄表记录
     *
     * @param meterNos 水表编号列表
     * @return 存在未完成记录的水表编号列表
     */
    List<String> batchCheckIncompleteRecords(@Param("meterNos") List<String> meterNos);
}
