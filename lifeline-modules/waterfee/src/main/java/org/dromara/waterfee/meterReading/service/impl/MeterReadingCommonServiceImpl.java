package org.dromara.waterfee.meterReading.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.mapper.WaterfeeMeterMapper;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeReadingTaskBo;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo;
import org.dromara.waterfee.meterReading.mapper.MeterReadingRecordMapper;
import org.dromara.waterfee.meterReading.mapper.WaterfeeReadingTaskMapper;
import org.dromara.waterfee.meterReading.service.IMeterReadingCommonService;
import org.dromara.waterfee.user.domain.WaterfeeUser;
import org.dromara.waterfee.user.mapper.WaterfeeUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抄表共享服务实现类 - 用于解决循环依赖问题
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@RequiredArgsConstructor
@Service
public class MeterReadingCommonServiceImpl implements IMeterReadingCommonService {

    private final MeterReadingRecordMapper meterReadingRecordMapper;
    private final WaterfeeReadingTaskMapper waterfeeReadingTaskMapper;
    private final WaterfeeMeterMapper waterfeeMeterMapper;
    private final WaterfeeUserMapper waterfeeUserMapper;

    /**
     * 创建抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createMeterReadingRecord(WaterfeeMeterReadingRecord record) {
        return meterReadingRecordMapper.insert(record) > 0;
    }

    /**
     * 更新抄表任务状态
     *
     * @param taskId    任务ID
     * @param isAudited 是否已审核
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTaskAuditStatus(Long taskId, String isAudited) {
        WaterfeeReadingTask task = waterfeeReadingTaskMapper.selectTaskById(taskId);

        if (task == null) {
            return false;
        }

        // 设置审核状态
        task.setIsAudited(isAudited);

        // 如果是审核通过，设置审核时间和审核人
        if ("1".equals(isAudited)) {
            // 设置审核时间
            task.setAuditTime(new Date());

            // 设置审核人（使用当前登录用户）
            try {
                Long userId = LoginHelper.getUserId();
                String userName = LoginHelper.getUsername();
                task.setAuditorName(userName);
            } catch (Exception e) {
                // 如果无法获取当前用户，使用默认值
                task.setAuditorName("system");
            }

            // 获取当月的开始和结束时间
            LocalDate now = LocalDate.now();
            LocalDateTime startOfMonth = now.withDayOfMonth(1).atStartOfDay();
            LocalDateTime endOfMonth = now.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);

            // 查询实际抄表数（当前任务对应的当月抄表记录数量）
            int actualReadingNum = meterReadingRecordMapper.selectCount(
                new LambdaQueryWrapper<WaterfeeMeterReadingRecord>()
                    .eq(WaterfeeMeterReadingRecord::getTaskId, taskId)
                    .eq(WaterfeeMeterReadingRecord::getIsAudited, "1")
                    .ge(WaterfeeMeterReadingRecord::getReadingTime, startOfMonth)
                    .le(WaterfeeMeterReadingRecord::getReadingTime, endOfMonth)
            ).intValue();
            task.setActualReadingNum(actualReadingNum);

//            // 设置抄表月份（使用当前月份）
//            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM");
//            task.setReadingMonth(sdf.format(new Date()));
        }

        return waterfeeReadingTaskMapper.updateById(task) > 0;
    }

    /**
     * 计算表册关联的水表数量和用户数量
     *
     * @param meterBookId 表册ID
     * @return 水表数量和用户数量
     */
//    @Override
//    public Map<String, Integer> calculateMeterAndUserCount(Long meterBookId) {
//        Map<String, Integer> result = new HashMap<>();
//
//        // 查询表册关联的水表数量
//        int meterCount = waterfeeMeterMapper.selectCount(
//            new LambdaQueryWrapper<WaterfeeMeter>()
//                .eq(WaterfeeMeter::getMeterBookId, meterBookId)
//                .eq(WaterfeeMeter::getDelFlag, "0")
//        ).intValue();
//
//        // 查询表册关联的用户数量
//        int userCount = waterfeeUserMapper.selectCount(
//            new LambdaQueryWrapper<WaterfeeUser>()
//                .inSql(WaterfeeUser::getMeterNo,
//                    "SELECT meter_no FROM waterfee_meter WHERE meter_book_id = " + meterBookId + " AND del_flag = '0'")
//                .eq(WaterfeeUser::getDelFlag, "0")
//        ).intValue();
//
//        result.put("meterCount", meterCount);
//        result.put("userCount", userCount);
//
//        return result;
//    }

    /**
     * 根据表册ID查询机械表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 机械表抄表记录列表
     */
    @Override
    public List<MeterReadingRecordVo> queryMechanicalMetersByBookId(Long meterBookId) {
        return meterReadingRecordMapper.selectMechanicalMetersByBookId(meterBookId);
    }

    /**
     * 根据表册ID查询智能表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 智能表抄表记录列表
     */
    @Override
    public List<MeterReadingRecordVo> queryIntelligentMetersByBookId(Long meterBookId) {
        return meterReadingRecordMapper.selectIntelligentMetersByBookId(meterBookId);
    }

    /**
     * 根据表册ID和任务ID查询抄表记录
     *
     * @param meterBookId 表册ID
     * @param taskId      任务ID
     * @return 抄表记录列表
     */
    @Override
    public List<WaterfeeMeterReadingRecord> selectByBookIdAndTaskId(Long meterBookId, Long taskId) {
        return meterReadingRecordMapper.selectByBookIdAndTaskId(meterBookId, taskId);
    }

    /**
     * 查询抄表任务
     *
     * @param taskId 任务ID
     * @return 抄表任务
     */
    @Override
    public WaterfeeReadingTaskVo queryTaskById(Long taskId) {
        return waterfeeReadingTaskMapper.selectTaskVoById(taskId);
    }

    /**
     * 更新抄表任务
     *
     * @param bo 抄表任务
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTask(WaterfeeReadingTaskBo bo) {
        WaterfeeReadingTask update = BeanUtil.toBean(bo, WaterfeeReadingTask.class);
        return waterfeeReadingTaskMapper.updateById(update) > 0;
    }

    /**
     * 根据水表编号查询最新一次的抄表记录（实体）
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    @Override
    public WaterfeeMeterReadingRecord queryLatestEntityByMeterNo(String meterNo) {
        return meterReadingRecordMapper.selectLatestEntityByMeterNo(meterNo);
    }

    /**
     * 统计指定任务的抄表记录数量
     *
     * @param taskId 任务ID
     * @return 记录数量
     */
    @Override
    public int countRecordsByTaskId(Long taskId) {
        return meterReadingRecordMapper.selectCount(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<WaterfeeMeterReadingRecord>()
                .eq(WaterfeeMeterReadingRecord::getTaskId, taskId)
        ).intValue();
    }

    /**
     * 根据任务ID查询抄表任务
     *
     * @param taskId 任务ID
     * @return 抄表任务
     */
    @Override
    public WaterfeeReadingTask getTaskById(Long taskId) {
        return waterfeeReadingTaskMapper.selectTaskById(taskId);
    }
}
