package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MechanicalReadingIndicatesFineVO {
    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编码（用水编码）")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 上期抄表读数
     */
    @ExcelProperty(value = "上期表数")
    private Double lastReading;

    /**
     * 本期抄表读数
     */
    @ExcelProperty(value = "本期表数")
    private Double currentReading;

    /**
     * 本期水量
     */
    @ExcelProperty(value = "用水量")
    private Double waterUsage;

    /**
     * 账单总金额 (含调整)
     */
    @ExcelProperty(value = "合计金额")
    private BigDecimal totalAmount;

    /**
     * 抄表员名称
     */
    @ExcelProperty(value = "抄表员")
    private String readerName;
}
