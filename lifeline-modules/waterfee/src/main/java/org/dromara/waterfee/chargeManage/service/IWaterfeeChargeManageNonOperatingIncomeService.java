package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageNonOperatingIncome;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageNonOperatingIncomeVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageNonOperatingIncomeBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 营业外收入记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManageNonOperatingIncomeService {

    /**
     * 查询营业外收入记录
     *
     * @param id 主键
     * @return 营业外收入记录
     */
    WaterfeeChargeManageNonOperatingIncome queryById(Long id);

    /**
     * 分页查询营业外收入记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 营业外收入记录分页列表
     */
    TableDataInfo<WaterfeeChargeManageNonOperatingIncomeVo> queryPageList(WaterfeeChargeManageNonOperatingIncomeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的营业外收入记录列表
     *
     * @param bo 查询条件
     * @return 营业外收入记录列表
     */
    List<WaterfeeChargeManageNonOperatingIncomeVo> queryList(WaterfeeChargeManageNonOperatingIncomeBo bo);

    /**
     * 新增营业外收入记录
     *
     * @param bo 营业外收入记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManageNonOperatingIncomeBo bo);

    /**
     * 修改营业外收入记录
     *
     * @param bo 营业外收入记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManageNonOperatingIncomeBo bo);

    /**
     * 校验并批量删除营业外收入记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
