package org.dromara.waterfee.priceManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeSurchargeConfigsBo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeSurchargeConfigsVo;
import org.dromara.waterfee.priceManage.domain.WaterfeeSurchargeConfigs;
import org.dromara.waterfee.priceManage.mapper.WaterfeeSurchargeConfigsMapper;
import org.dromara.waterfee.priceManage.service.IWaterfeeSurchargeConfigsService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 附加费配置 (Surcharge Configurations)Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RequiredArgsConstructor
@Service
public class WaterfeeSurchargeConfigsServiceImpl implements IWaterfeeSurchargeConfigsService {

    private final WaterfeeSurchargeConfigsMapper baseMapper;

    /**
     * 查询附加费配置 (Surcharge Configurations)
     *
     * @param id 主键
     * @return 附加费配置 (Surcharge Configurations)
     */
    @Override
    public WaterfeeSurchargeConfigs queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询附加费配置 (Surcharge Configurations)列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 附加费配置 (Surcharge Configurations)分页列表
     */
    @Override
    public TableDataInfo<WaterfeeSurchargeConfigsVo> queryPageList(WaterfeeSurchargeConfigsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeSurchargeConfigs> lqw = buildQueryWrapper(bo);
        Page<WaterfeeSurchargeConfigsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的附加费配置 (Surcharge Configurations)列表
     *
     * @param bo 查询条件
     * @return 附加费配置 (Surcharge Configurations)列表
     */
    @Override
    public List<WaterfeeSurchargeConfigsVo> queryList(WaterfeeSurchargeConfigsBo bo) {
        LambdaQueryWrapper<WaterfeeSurchargeConfigs> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeSurchargeConfigs> buildQueryWrapper(WaterfeeSurchargeConfigsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeSurchargeConfigs> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeSurchargeConfigs::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getName()), WaterfeeSurchargeConfigs::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCalculationMethod()), WaterfeeSurchargeConfigs::getCalculationMethod, bo.getCalculationMethod());
        lqw.eq(bo.getFixedAmount() != null, WaterfeeSurchargeConfigs::getFixedAmount, bo.getFixedAmount());
        lqw.eq(bo.getRatePercent() != null, WaterfeeSurchargeConfigs::getRatePercent, bo.getRatePercent());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), WaterfeeSurchargeConfigs::getCategory, bo.getCategory());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), WaterfeeSurchargeConfigs::getRemarks, bo.getRemarks());
        return lqw;
    }

    /**
     * 新增附加费配置 (Surcharge Configurations)
     *
     * @param bo 附加费配置 (Surcharge Configurations)
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeSurchargeConfigsBo bo) {
        WaterfeeSurchargeConfigs add = MapstructUtils.convert(bo, WaterfeeSurchargeConfigs.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改附加费配置 (Surcharge Configurations)
     *
     * @param bo 附加费配置 (Surcharge Configurations)
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeSurchargeConfigsBo bo) {
        WaterfeeSurchargeConfigs update = MapstructUtils.convert(bo, WaterfeeSurchargeConfigs.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeSurchargeConfigs entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除附加费配置 (Surcharge Configurations)信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
