package org.dromara.waterfee.community.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.area.domain.WaterfeeArea;
import org.dromara.waterfee.area.mapper.WaterfeeAreaMapper;
import org.dromara.waterfee.community.domain.Community;
import org.dromara.waterfee.community.domain.bo.CommunityBo;
import org.dromara.waterfee.community.domain.vo.CommunityVo;
import org.dromara.waterfee.community.mapper.CommunityMapper;
import org.dromara.waterfee.community.service.ICommunityService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 社区服务实现类
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@RequiredArgsConstructor
@Service
public class CommunityServiceImpl implements ICommunityService {

    private final CommunityMapper baseMapper;

    private final WaterfeeAreaMapper areaMapper;
    /**
     * 查询社区
     *
     * @param id ID
     * @return 社区
     */
    @Override
    public CommunityVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询社区列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 社区列表
     */
    @Override
    public TableDataInfo<CommunityVo> queryPageList(CommunityBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Community> lqw = buildQueryWrapper(bo);
        IPage<CommunityVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<CommunityVo> list = result.getRecords();
        if (!list.isEmpty()) {
            List<Long> areaIds = list.stream().map(CommunityVo::getAreaId).distinct().toList();
            List<WaterfeeArea> areas = areaMapper.selectBatchIds(areaIds);
            Map<Long, String> areaMap = areas.stream().collect(
                java.util.stream.Collectors.toMap(WaterfeeArea::getAreaId, WaterfeeArea::getAreaName));

            list.forEach(vo -> vo.setAreaName(areaMap.get(vo.getAreaId())));
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的社区列表
     *
     * @param bo 查询条件
     * @return 社区列表
     */
    @Override
    public List<CommunityVo> queryList(CommunityBo bo) {
        LambdaQueryWrapper<Community> lqw = buildQueryWrapper(bo);
        List<CommunityVo> list = baseMapper.selectVoList(lqw);
        if (!list.isEmpty()) {
            List<Long> areaIds = list.stream().map(CommunityVo::getAreaId).distinct().toList();
            List<WaterfeeArea> areas = areaMapper.selectBatchIds(areaIds);
            Map<Long, String> areaMap = areas.stream().collect(
                java.util.stream.Collectors.toMap(WaterfeeArea::getAreaId, WaterfeeArea::getAreaName));

            list.forEach(vo -> vo.setAreaName(areaMap.get(vo.getAreaId())));
        }
        return list;
    }

    /**
     * 新增社区
     *
     * @param bo 社区
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CommunityBo bo) {
        Community add = MapstructUtils.convert(bo, Community.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改社区
     *
     * @param bo 社区
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CommunityBo bo) {
        Community update = MapstructUtils.convert(bo, Community.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 验证实体类数据
     *
     * @param entity 实体类
     */
    private void validEntityBeforeSave(Community entity) {
        // TODO 验证数据
    }

    /**
     * 验证后批量删除社区
     *
     * @param ids     需要删除的ID集合
     * @param isValid 是否进行有效性验证
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 验证是否存在关联数据
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 构建查询条件
     *
     * @param bo 查询条件
     * @return 查询条件对象
     */
    private LambdaQueryWrapper<Community> buildQueryWrapper(CommunityBo bo) {
        LambdaQueryWrapper<Community> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, Community::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getCommunityName()), Community::getCommunityName, bo.getCommunityName());
        lqw.eq(StringUtils.isNotBlank(bo.getCommunityNo()), Community::getCommunityNo, bo.getCommunityNo());
        lqw.eq(bo.getAreaId() != null, Community::getAreaId, bo.getAreaId());
        return lqw;
    }

    /**
     * 获取社区下拉选择框数据
     *
     * @return 社区ID和名称的Map集合，key为社区ID，value为社区名称
     */
    @Override
    public Map<String, String> getSelectCommunityMap() {
        // 查询所有可用的社区列表
        LambdaQueryWrapper<Community> lqw = Wrappers.lambdaQuery();
        // 如果社区有状态字段，可以添加状态过滤条件
        // lqw.eq(Community::getStatus, "0"); // 只查询状态正常的社区
        lqw.orderByAsc(Community::getId); // 按照ID排序

        List<Community> communityList = baseMapper.selectList(lqw);

        // 转换为Map<String, String>格式，key为社区ID，value为社区名称
        Map<String, String> communityMap = new HashMap<>(communityList.size());
        for (Community community : communityList) {
            communityMap.put(community.getId().toString(), community.getCommunityName());
        }

        return communityMap;
    }
}
