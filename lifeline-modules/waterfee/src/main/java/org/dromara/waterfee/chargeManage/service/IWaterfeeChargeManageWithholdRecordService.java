package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdRecord;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdRecordVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdRecordBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 代扣记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManageWithholdRecordService {

    /**
     * 查询代扣记录
     *
     * @param id 主键
     * @return 代扣记录
     */
    WaterfeeChargeManageWithholdRecord queryById(Long id);

    /**
     * 分页查询代扣记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代扣记录分页列表
     */
    TableDataInfo<WaterfeeChargeManageWithholdRecordVo> queryPageList(WaterfeeChargeManageWithholdRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的代扣记录列表
     *
     * @param bo 查询条件
     * @return 代扣记录列表
     */
    List<WaterfeeChargeManageWithholdRecordVo> queryList(WaterfeeChargeManageWithholdRecordBo bo);

    /**
     * 新增代扣记录
     *
     * @param bo 代扣记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManageWithholdRecordBo bo);

    /**
     * 修改代扣记录
     *
     * @param bo 代扣记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManageWithholdRecordBo bo);

    /**
     * 校验并批量删除代扣记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
