package org.dromara.waterfee.publicInfo.mapper;

import org.dromara.waterfee.publicInfo.domain.WaterfeeLeakReport;
import org.dromara.waterfee.publicInfo.domain.vo.WaterfeeLeakReportVo;
import org.dromara.waterfee.publicInfo.domain.bo.WaterfeeLeakReportBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 偷漏水举报Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface WaterfeeLeakReportMapper extends BaseMapperPlus<WaterfeeLeakReport, WaterfeeLeakReportVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeLeakReportVo>> P selectVoPage(IPage<WaterfeeLeakReport> page, Wrapper<WaterfeeLeakReport> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询偷漏水举报列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeLeakReportVo> queryList(@Param("page") Page<WaterfeeLeakReport> page, @Param("query") WaterfeeLeakReportBo query);

}
