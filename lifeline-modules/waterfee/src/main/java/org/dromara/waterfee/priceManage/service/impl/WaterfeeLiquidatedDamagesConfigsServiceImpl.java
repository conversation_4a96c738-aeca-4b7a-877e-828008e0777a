package org.dromara.waterfee.priceManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeLiquidatedDamagesConfigsBo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeLiquidatedDamagesConfigsVo;
import org.dromara.waterfee.priceManage.domain.WaterfeeLiquidatedDamagesConfigs;
import org.dromara.waterfee.priceManage.mapper.WaterfeeLiquidatedDamagesConfigsMapper;
import org.dromara.waterfee.priceManage.service.IWaterfeeLiquidatedDamagesConfigsService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 违约金配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RequiredArgsConstructor
@Service
public class WaterfeeLiquidatedDamagesConfigsServiceImpl implements IWaterfeeLiquidatedDamagesConfigsService {

    private final WaterfeeLiquidatedDamagesConfigsMapper baseMapper;

    /**
     * 查询违约金配置
     *
     * @param id 主键
     * @return 违约金配置
     */
    @Override
    public WaterfeeLiquidatedDamagesConfigs queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询违约金配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 违约金配置分页列表
     */
    @Override
    public TableDataInfo<WaterfeeLiquidatedDamagesConfigsVo> queryPageList(WaterfeeLiquidatedDamagesConfigsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeLiquidatedDamagesConfigs> lqw = buildQueryWrapper(bo);
        Page<WaterfeeLiquidatedDamagesConfigsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的违约金配置列表
     *
     * @param bo 查询条件
     * @return 违约金配置列表
     */
    @Override
    public List<WaterfeeLiquidatedDamagesConfigsVo> queryList(WaterfeeLiquidatedDamagesConfigsBo bo) {
        LambdaQueryWrapper<WaterfeeLiquidatedDamagesConfigs> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeLiquidatedDamagesConfigs> buildQueryWrapper(WaterfeeLiquidatedDamagesConfigsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeLiquidatedDamagesConfigs> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeLiquidatedDamagesConfigs::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getName()), WaterfeeLiquidatedDamagesConfigs::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCalculationMethod()), WaterfeeLiquidatedDamagesConfigs::getCalculationMethod, bo.getCalculationMethod());
        lqw.eq(bo.getFixedAmount() != null, WaterfeeLiquidatedDamagesConfigs::getFixedAmount, bo.getFixedAmount());
        lqw.eq(bo.getInterestRatePercent() != null, WaterfeeLiquidatedDamagesConfigs::getInterestRatePercent, bo.getInterestRatePercent());
        lqw.eq(StringUtils.isNotBlank(bo.getCollectionMethod()), WaterfeeLiquidatedDamagesConfigs::getCollectionMethod, bo.getCollectionMethod());
        lqw.eq(bo.getStartDate() != null, WaterfeeLiquidatedDamagesConfigs::getStartDate, bo.getStartDate());
        lqw.eq(bo.getCanExceedPrincipal() != null, WaterfeeLiquidatedDamagesConfigs::getCanExceedPrincipal, bo.getCanExceedPrincipal());
        lqw.eq(bo.getWaiverEnabled() != null, WaterfeeLiquidatedDamagesConfigs::getWaiverEnabled, bo.getWaiverEnabled());
        lqw.eq(bo.getWaiverTime() != null, WaterfeeLiquidatedDamagesConfigs::getWaiverTime, bo.getWaiverTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), WaterfeeLiquidatedDamagesConfigs::getRemarks, bo.getRemarks());
        return lqw;
    }

    /**
     * 新增违约金配置
     *
     * @param bo 违约金配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeLiquidatedDamagesConfigsBo bo) {
        WaterfeeLiquidatedDamagesConfigs add = MapstructUtils.convert(bo, WaterfeeLiquidatedDamagesConfigs.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改违约金配置
     *
     * @param bo 违约金配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeLiquidatedDamagesConfigsBo bo) {
        WaterfeeLiquidatedDamagesConfigs update = MapstructUtils.convert(bo, WaterfeeLiquidatedDamagesConfigs.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeLiquidatedDamagesConfigs entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除违约金配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
