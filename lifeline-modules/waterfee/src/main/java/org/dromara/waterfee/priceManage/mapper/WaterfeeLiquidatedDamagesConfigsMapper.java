package org.dromara.waterfee.priceManage.mapper;

import org.dromara.waterfee.priceManage.domain.WaterfeeLiquidatedDamagesConfigs;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeLiquidatedDamagesConfigsVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;

/**
 * 违约金配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface WaterfeeLiquidatedDamagesConfigsMapper extends BaseMapperPlus<WaterfeeLiquidatedDamagesConfigs, WaterfeeLiquidatedDamagesConfigsVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeLiquidatedDamagesConfigsVo>> P selectVoPage(IPage<WaterfeeLiquidatedDamagesConfigs> page, Wrapper<WaterfeeLiquidatedDamagesConfigs> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

}
