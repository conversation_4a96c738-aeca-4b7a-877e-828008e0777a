package org.dromara.waterfee.meterRelation.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.mapper.WaterfeeMeterMapper;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterBalanceRecord;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterThresholdConfig;
import org.dromara.waterfee.meterRelation.domain.vo.BalanceAnalyzeResultVO;
import org.dromara.waterfee.meterRelation.domain.vo.MeterBalanceAnalysisVO;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterBalanceRecordMapper;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterReadingRecordMapper;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterRelationMapper;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterThresholdConfigMapper;
import org.dromara.waterfee.meterRelation.service.MeterBalanceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:09
 **/


@Slf4j
@Service
@RequiredArgsConstructor
public class MeterBalanceServiceImpl implements MeterBalanceService {

    private final WaterfeeMeterRelationMapper relationMapper;
    private final WaterfeeMeterReadingRecordMapper readingMapper;
    private final WaterfeeMeterMapper meterMapper;
    private final WaterfeeMeterThresholdConfigMapper thresholdConfigMapper;
    private final WaterfeeMeterBalanceRecordMapper balanceRecordMapper;

    private static final double DEFAULT_LEAK_THRESHOLD_PERCENT = 5;

    @Override
    public BalanceAnalyzeResultVO analyzeBalance(Long parentMeterId, LocalDateTime readingTime) {
        WaterfeeMeter parentMeter = meterMapper.selectById(parentMeterId);
        if (parentMeter == null) throw new RuntimeException("总表不存在");

        Double parentUsage = readingMapper.selectWaterUsageByMeterAndTime(parentMeterId, readingTime);
        if (parentUsage == null) parentUsage = 0.0;

        List<Long> childIds = relationMapper.selectChildMeterIds(parentMeterId);
        List<MeterBalanceAnalysisVO.ChildMeterReadingVO> children = new ArrayList<>();
        double totalChildUsage = 0;

        for (Long childId : childIds) {
            Double usage = readingMapper.selectWaterUsageByMeterAndTime(childId, readingTime);
            if (usage == null) usage = 0.0;
            totalChildUsage += usage;

            WaterfeeMeter child = meterMapper.selectById(childId);
            MeterBalanceAnalysisVO.ChildMeterReadingVO vo = new MeterBalanceAnalysisVO.ChildMeterReadingVO();
            vo.setMeterId(childId);
            vo.setMeterNo(child.getMeterNo());
            vo.setWaterUsage(usage);
            children.add(vo);
        }

        // 查询总表的阈值配置（不再是每对总分表）
        WaterfeeMeterThresholdConfig config = thresholdConfigMapper.selectByParentId(parentMeterId);
        double threshold = config != null ? config.getThreshold().doubleValue() : DEFAULT_LEAK_THRESHOLD_PERCENT;

        double diff = parentUsage - totalChildUsage;

        WaterfeeMeterBalanceRecord record = new WaterfeeMeterBalanceRecord();
        record.setParentMeterId(parentMeterId);
        record.setParentMeterNo(parentMeter.getMeterNo());
        record.setTenantId(parentMeter.getTenantId());
        record.setReadingTime(readingTime);
        record.setParentUsage(parentUsage);
        record.setChildUsage(totalChildUsage);
        record.setDiffUsage(diff);

        double leakRate = parentUsage == 0 ? 0 : (diff / parentUsage) * 100;
        boolean abnormal = Math.abs(diff) > threshold || Math.abs(leakRate) > 10;
 
        record.setLeakRate(leakRate);
        record.setIsAbnormal(abnormal ? "1" : "0");
        record.setAbnormalReason(abnormal ? "总-分表读数差值超过阈值配置" : null);

        balanceRecordMapper.insert(record);

        BalanceAnalyzeResultVO vo = new BalanceAnalyzeResultVO();
        BeanUtils.copyProperties(record, vo);
        vo.setChildMeterNos(Collections.singletonList(childIds.toString()));
        return vo;

    }

}
