package org.dromara.waterfee.meterRelation.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterRelation.domain.vo.BalanceAnalyzeResultVO;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterReadingRecordMapper;
import org.dromara.waterfee.meterRelation.service.MeterBalanceService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:11
 **/

@RestController
@RequestMapping("/api/meter/balance")
@RequiredArgsConstructor
@Tag(name = "总分水表平衡分析")
public class MeterBalanceController {

    private final MeterBalanceService meterBalanceService;
    private final WaterfeeMeterReadingRecordMapper readingMapper;

    @GetMapping("/analyze")
    @Operation(summary = "分析总-分水表水量差异")
    public R<BalanceAnalyzeResultVO> analyze(@RequestParam Long parentMeterId,
                                             @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime readingTime) {
        return R.ok(meterBalanceService.analyzeBalance(parentMeterId, readingTime));
    }

    @GetMapping("/getRecordByMonth")
    @Operation(summary = "分析总-分水表水量差异")
    public R<WaterfeeMeterReadingRecord> getRecordByMonth(@RequestParam String MeterNo,
                                                          @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime readingTime) {
        return R.ok(readingMapper.selectByMeterNoAndMonth(MeterNo, readingTime));
    }


}
