package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdConfig;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdConfigVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdConfigBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 代扣配置信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManageWithholdConfigMapper extends BaseMapperPlus<WaterfeeChargeManageWithholdConfig, WaterfeeChargeManageWithholdConfigVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManageWithholdConfigVo>> P selectVoPage(IPage<WaterfeeChargeManageWithholdConfig> page, Wrapper<WaterfeeChargeManageWithholdConfig> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询代扣配置信息列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManageWithholdConfigVo> queryList(@Param("page") Page<WaterfeeChargeManageWithholdConfig> page, @Param("query") WaterfeeChargeManageWithholdConfigBo query);

}
