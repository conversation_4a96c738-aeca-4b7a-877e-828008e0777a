package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArSplit;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArSplitVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArSplitBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 应收账分账记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManageArSplitMapper extends BaseMapperPlus<WaterfeeChargeManageArSplit, WaterfeeChargeManageArSplitVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManageArSplitVo>> P selectVoPage(IPage<WaterfeeChargeManageArSplit> page, Wrapper<WaterfeeChargeManageArSplit> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询应收账分账记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManageArSplitVo> queryList(@Param("page") Page<WaterfeeChargeManageArSplit> page, @Param("query") WaterfeeChargeManageArSplitBo query);

}
