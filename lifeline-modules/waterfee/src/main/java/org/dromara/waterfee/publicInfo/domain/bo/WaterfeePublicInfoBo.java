package org.dromara.waterfee.publicInfo.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.publicInfo.domain.entity.WaterfeePublicInfo;

import java.util.Date;

/**
 * 公共信息业务对象 waterfee_public_info
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeePublicInfo.class)
public class WaterfeePublicInfoBo extends BaseEntity {

    /**
     * 信息ID
     */
    @NotNull(message = "信息ID不能为空", groups = {EditGroup.class})
    private Long infoId;

    /**
     * 信息标题
     */
    @NotBlank(message = "信息标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 信息内容
     */
    @NotBlank(message = "信息内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 信息类型（1-停水通知 2-供水公告 3-业务常识 4-水费标准 5-营商环境 6-公司简介）
     */
    @NotBlank(message = "信息类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String infoType;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 截止时间（针对停水通知有效）
     */
    private Date endTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 点击量
     */
    private Long viewCount;

    /**
     * 状态（0-未发布 1-已发布）
     */
    private String status;

    /**
     * 关联的微信草稿ID
     */
    private Long wechatDraftId;

    /**
     * 备注
     */
    private String remark;
}
