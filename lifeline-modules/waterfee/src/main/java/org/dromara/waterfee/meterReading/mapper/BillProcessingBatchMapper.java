package org.dromara.waterfee.meterReading.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;

import java.util.List;
import java.util.Map;

/**
 * 账单处理批量操作Mapper接口
 * 专门用于优化账单相关的批量数据库操作
 */
@Mapper
public interface BillProcessingBatchMapper {

    /**
     * 批量获取账单信息
     *
     * @param billIds 账单ID列表
     * @return 账单信息列表
     */
    List<WaterfeeBillVo> batchSelectBillsByIds(@Param("billIds") List<Long> billIds);

    /**
     * 批量获取客户账单信息
     *
     * @param customerIds 客户ID列表
     * @return 账单信息列表
     */
    List<WaterfeeBillVo> batchSelectUnpaidBillsByCustomers(@Param("customerIds") List<Long> customerIds);

    /**
     * 批量获取客户余额信息
     *
     * @param customerIds 客户ID列表
     * @return 客户余额信息
     */
    @MapKey("customer_id")
    List<Map<String, Object>> batchSelectCustomerBalances(@Param("customerIds") List<Long> customerIds);

    /**
     * 批量更新账单状态
     *
     * @param billIds 账单ID列表
     * @param status  新状态
     * @return 更新成功的记录数
     */
    int batchUpdateBillStatus(@Param("billIds") List<Long> billIds, @Param("status") String status);

    /**
     * 批量插入支付记录
     *
     * @param paymentRecords 支付记录列表
     * @return 插入成功的记录数
     */
    int batchInsertPaymentRecords(@Param("paymentRecords") List<Map<String, Object>> paymentRecords);

    /**
     * 批量获取账单统计信息
     *
     * @param meterNos 水表编号列表
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计信息
     */
    @MapKey("meter_id")
    List<Map<String, Object>> batchGetBillStatistics(@Param("meterNos") List<String> meterNos,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);

    /**
     * 批量检查账单是否存在
     *
     * @param meterNos 水表编号列表
     * @param billingPeriod 账单周期
     * @return 已存在账单的水表编号列表
     */
    List<String> batchCheckBillExists(@Param("meterNos") List<String> meterNos,
                                    @Param("billingPeriod") String billingPeriod);

    /**
     * 批量获取客户支付能力信息
     *
     * @param customerIds 客户ID列表
     * @return 支付能力信息
     */
    @MapKey("customer_id")
    List<Map<String, Object>> batchGetCustomerPaymentCapacity(@Param("customerIds") List<Long> customerIds);

    /**
     * 批量获取最近的支付记录
     *
     * @param customerIds 客户ID列表
     * @param limit       记录数限制
     * @return 支付记录列表
     */
    @MapKey("customer_id")
    List<Map<String, Object>> batchGetRecentPaymentRecords(@Param("customerIds") List<Long> customerIds,
                                                          @Param("limit") int limit);

    /**
     * 批量获取账单详细信息（包含客户信息）
     *
     * @param billIds 账单ID列表
     * @return 账单详细信息
     */
    @MapKey("bill_id")
    List<Map<String, Object>> batchGetBillDetailsWithCustomer(@Param("billIds") List<Long> billIds);

    /**
     * 批量更新客户账户余额
     *
     * @param balanceUpdates 余额更新信息列表
     * @return 更新成功的记录数
     */
    int batchUpdateCustomerBalances(@Param("balanceUpdates") List<Map<String, Object>> balanceUpdates);

    /**
     * 批量获取账单支付历史
     *
     * @param billIds 账单ID列表
     * @return 支付历史记录
     */
    @MapKey("bill_id")
    List<Map<String, Object>> batchGetBillPaymentHistory(@Param("billIds") List<Long> billIds);

    /**
     * 批量检查客户支付能力
     *
     * @param paymentRequests 支付请求列表（包含客户ID和金额）
     * @return 支付能力检查结果
     */
    @MapKey("customer_id")
    List<Map<String, Object>> batchCheckPaymentCapacity(@Param("paymentRequests") List<Map<String, Object>> paymentRequests);
}
