package org.dromara.waterfee.publicInfo.domain.bo;

import org.dromara.waterfee.publicInfo.domain.BranchOffice;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 营业网点业务对象 branch_office
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BranchOffice.class, reverseConvertGenerate = false)
public class BranchOfficeBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long branchId;

    /**
     * 网点名称
     */
    private String branchName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 经度
     */
    private String lon;

    /**
     * 维度
     */
    private String lat;

    /**
     * 备注
     */
    private String remark;


}
