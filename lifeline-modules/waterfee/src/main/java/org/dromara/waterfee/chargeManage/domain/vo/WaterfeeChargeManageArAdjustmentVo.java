package org.dromara.waterfee.chargeManage.domain.vo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArAdjustment;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 应收账追补记录视图对象 waterfee_charge_manage_ar_adjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManageArAdjustment.class)
public class WaterfeeChargeManageArAdjustmentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
    private String meterNo;

    /**
     * 账期
     */
    @ExcelProperty(value = "账期")
    private String billPeriod;

    /**
     * 原应收金额
     */
    @ExcelProperty(value = "原应收金额")
    private Long originalAmount;

    /**
     * 调整后金额
     */
    @ExcelProperty(value = "调整后金额")
    private Long adjustedAmount;

    /**
     * 调整原因
     */
    @ExcelProperty(value = "调整原因")
    private String reason;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
