package org.dromara.waterfee.chargeManage.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageThirdParty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 第三方对账记录视图对象 waterfee_charge_manage_third_party
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManageThirdParty.class)
public class WaterfeeChargeManageThirdPartyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 渠道编码（UNIONPAY、WECHAT等）
     */
    @ExcelProperty(value = "渠道编码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "U=NIONPAY、WECHAT等")
    private String channelCode;

    /**
     * 本地订单号
     */
    @ExcelProperty(value = "本地订单号")
    private String localOrderNo;

    /**
     * 第三方订单号
     */
    @ExcelProperty(value = "第三方订单号")
    private String thirdOrderNo;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long amount;

    /**
     * 状态（MATCHED、MISMATCHED、PENDING）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "M=ATCHED、MISMATCHED、PENDING")
    private String status;

    /**
     * 对账日期
     */
    @ExcelProperty(value = "对账日期")
    private Date reconciliationDate;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
