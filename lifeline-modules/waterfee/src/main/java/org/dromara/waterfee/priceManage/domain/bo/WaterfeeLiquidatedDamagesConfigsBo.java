package org.dromara.waterfee.priceManage.domain.bo;

import org.dromara.waterfee.priceManage.domain.WaterfeeLiquidatedDamagesConfigs;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 违约金配置业务对象 waterfee_liquidated_damages_configs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeLiquidatedDamagesConfigs.class, reverseConvertGenerate = false)
public class WaterfeeLiquidatedDamagesConfigsBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 违约金名称
     */
    @NotBlank(message = "违约金名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 计算方式 (e.g., daily_rate, fixed_amount)
     */
    @NotBlank(message = "计算方式 (e.g., daily_rate, fixed_amount)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String calculationMethod;

    /**
     * 固定金额 (if calculation_method is fixed_amount)
     */
    private Long fixedAmount;

    /**
     * 利率(%), null if not applicable
     */
    private Long interestRatePercent;

    /**
     * 收取方式 (e.g., term, next_month)
     */
    @NotBlank(message = "收取方式 (e.g., term, next_month)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String collectionMethod;

    /**
     * 开始日期 (if applicable)
     */
    private Date startDate;

    /**
     * 是否大于本金 (True/False)
     */
    @NotNull(message = "是否大于本金 (True/False)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long canExceedPrincipal;

    /**
     * 免除违约金启用状态
     */
    @NotNull(message = "免除违约金启用状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long waiverEnabled;

    /**
     * 免除截止时间 (if waiver_enabled is true)
     */
    private Date waiverTime;

    /**
     * 备注
     */
    private String remarks;


}
