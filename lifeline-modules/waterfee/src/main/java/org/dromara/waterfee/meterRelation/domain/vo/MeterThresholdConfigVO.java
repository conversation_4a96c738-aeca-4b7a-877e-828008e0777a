package org.dromara.waterfee.meterRelation.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:25
 **/

@Data
@Schema(description = "总分表阈值配置VO")
public class MeterThresholdConfigVO {

    private Long parentMeterId;

    private String parentMeterNo;

    private Long childMeterId;

    private String childMeterNo;

    private BigDecimal threshold;
}
