package org.dromara.waterfee.meterRelation.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterRelation;
import org.dromara.waterfee.meterRelation.domain.dto.MeterRelationAddDTO;
import org.dromara.waterfee.meterRelation.domain.vo.MeterRelationTreeVO;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterRelationMapper;
import org.dromara.waterfee.meterRelation.service.WaterfeeMeterRelationService;
import org.dromara.waterfee.meterRelation.service.impl.MeterRelationValidator;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:03
 **/


@RestController
@RequestMapping("/meter/relation")
@RequiredArgsConstructor
@Tag(name = "水表总分关系管理")
public class WaterfeeMeterRelationController {

    private final WaterfeeMeterRelationService relationService;
    private final MeterRelationValidator relationValidator;
    private final WaterfeeMeterRelationMapper relationMapper;

    @GetMapping("/tree")
    @Operation(summary = "获取水表总分树结构")
    public R<List<MeterRelationTreeVO>> tree() {
        return R.ok(relationService.buildMeterTree());
    }

    @PostMapping("/add")
    @Operation(summary = "添加总-分水表关系")
    public R<Void> add(@RequestBody MeterRelationAddDTO dto) {
        relationValidator.validateNoLoop(dto.getParentMeterId(), dto.getChildMeterId());

        WaterfeeMeterRelation relation = new WaterfeeMeterRelation();
        relation.setParentMeterId(dto.getParentMeterId());
        relation.setChildMeterId(dto.getChildMeterId());
        relation.setTenantId("000000");
        relation.setCreateBy(LoginHelper.getUsername());
        relation.setCreateTime(LocalDateTime.now());
        relationMapper.insert(relation);

        return R.ok();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除总-分水表关系")
    @SaCheckPermission("waterfee:meterRelation:remove")
    @Log(title = "水表总分关系管理", businessType = BusinessType.DELETE)
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long id) {
        WaterfeeMeterRelation relation = relationMapper.selectById(id);
        if (relation == null) {
            return R.fail("关系不存在");
        }
        relation.setDelFlag("2");
        relationMapper.updateById(relation);
        return R.ok();
    }
}
