package org.dromara.waterfee.meterRelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterThresholdConfig;
import org.dromara.waterfee.meterRelation.domain.dto.MeterThresholdConfigDTO;
import org.dromara.waterfee.meterRelation.domain.vo.MeterThresholdConfigVO;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterThresholdConfigMapper;
import org.dromara.waterfee.meterRelation.service.MeterThresholdConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:28
 **/

@Service
@RequiredArgsConstructor
public class MeterThresholdConfigServiceImpl implements MeterThresholdConfigService {

    private final WaterfeeMeterThresholdConfigMapper configMapper;

    @Override
    public void saveOrUpdate(MeterThresholdConfigDTO dto) {
        LambdaQueryWrapper<WaterfeeMeterThresholdConfig> query = Wrappers.lambdaQuery(WaterfeeMeterThresholdConfig.class)
            .eq(WaterfeeMeterThresholdConfig::getParentMeterId, dto.getParentMeterId())
            .eq(WaterfeeMeterThresholdConfig::getChildMeterId, dto.getChildMeterId())
            .eq(WaterfeeMeterThresholdConfig::getDelFlag, "0");

        WaterfeeMeterThresholdConfig entity = configMapper.selectOne(query);
        if (entity == null) {
            entity = new WaterfeeMeterThresholdConfig();
            BeanUtils.copyProperties(dto, entity);
            entity.setTenantId("000000");
            entity.setCreateTime(LocalDateTime.now());
            entity.setCreateBy(LoginHelper.getUsername());
            configMapper.insert(entity);
        } else {
            entity.setThreshold(dto.getThreshold());
            entity.setUpdateTime(LocalDateTime.now());
            entity.setUpdateBy(LoginHelper.getUsername());
            configMapper.updateById(entity);
        }
    }

    @Override
    public List<MeterThresholdConfigVO> listAll() {
        return configMapper.selectConfigList();
    }
}
