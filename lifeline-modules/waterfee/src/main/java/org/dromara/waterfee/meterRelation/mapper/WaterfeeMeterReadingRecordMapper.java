package org.dromara.waterfee.meterRelation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.statisticalReport.domain.MechanicalMeterNonResidentUsersPayMeterReadingVO;
import org.dromara.waterfee.statisticalReport.domain.MechanicalReadingIndicatesFineVO;
import org.dromara.waterfee.statisticalReport.domain.MechanicalWatchesByYearVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:10
 **/

public interface WaterfeeMeterReadingRecordMapper extends BaseMapper<WaterfeeMeterReadingRecord> {

    @Select("SELECT water_usage FROM waterfee_meter_reading_record " +
            "WHERE meter_no = (SELECT meter_no FROM waterfee_meter WHERE meter_id = #{meterId}) " +
            "AND DATE_FORMAT(reading_time, '%Y-%m') = DATE_FORMAT(#{readingTime}, '%Y-%m') AND del_flag = '0' LIMIT 1")
    Double selectWaterUsageByMeterAndTime(@Param("meterId") Long meterId, @Param("readingTime") LocalDateTime readingTime);

    @Select("SELECT * FROM waterfee_meter_reading_record " +
            "WHERE meter_no = #{meterNo} " +
            "AND DATE_FORMAT(reading_time, '%Y-%m') = DATE_FORMAT(#{readingTime}, '%Y-%m') " +
            "AND del_flag = '0' LIMIT 1")
    WaterfeeMeterReadingRecord selectByMeterNoAndMonth(@Param("meterNo") String meterNo, @Param("readingTime") LocalDateTime readingTime);

    /**
     * 机械表抄表明细表
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("<script>" +
        "select " +
        "            u.user_no," +
        "            u.user_name," +
        "            u.address," +
        "            r.last_reading," +
        "            r.current_reading," +
        "            r.water_usage," +
        "            b.total_amount," +
        "            book.reader_name" +
        "   from waterfee_meter_reading_record r " +
        "   left join waterfee_meter m on r.meter_no = m.meter_no" +
        "   left join waterfee_meter_book book on m.meter_book_id = book.id" +
        "   left join waterfee_user u on u.user_id = m.user_id" +
        "   left join waterfee_bills b on b.current_reading_id = r.record_id" +
        "   where " +
        "   m.meter_type = 1" +
        "   and u.user_no is not null" +
        "   <if test=\"startTime != null and startTime != ''\"> " +
        "           and r.create_time &gt;= #{startTime} " +
        "   </if> " +
        "   <if test=\"endTime != null and endTime != ''\"> " +
        "           and r.create_time &lt;= #{endTime} " +
        "   </if> order by r.create_time desc"
        + "</script>")
    List<MechanicalReadingIndicatesFineVO> getMechanicalReadingIndicatesFine(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 机械表按年份统计抄表数据
     * @return
     */
    @Select("(select   \n" +
        " date_format(r.reading_time, '%Y%m') as yearMonth,  \n" +
        " count(case when u.use_water_nature = 'resident' then r.water_usage end) as residentialWaterUsage,  \n" +
        " count(case when u.use_water_nature = 'business' then r.water_usage end) as businessWaterUsage,  \n" +
        " count(case when (u.use_water_nature = 'publicGreening' or u.use_water_nature = 'communityGreening') then r.water_usage end) as greenWaterUsage,  \n" +
        " count(case when u.use_water_nature = 'specialType' then r.water_usage end) as specialWaterUsage,  \n" +
        " count(water_usage) as totalWaterUsage  \n" +
        "\n" +
        "from waterfee_meter_reading_record r   \n" +
        " left join waterfee_meter m on r.meter_no = m.meter_no  \n" +
        " left join waterfee_user u on u.user_id = m.user_id  \n" +
        "where   \n" +
        " m.meter_type = 1  \n" +
        " and u.user_no is not null  \n" +
        "group by date_format(r.reading_time, '%Y%m')  \n" +
        "order by yearMonth desc )\n" +
        "union\n" +
        "(select   \n" +
        " '合计' as yearMonth,  \n" +
        " count(case when u.use_water_nature = 'resident' then r.water_usage end) as residentialWaterUsage,  \n" +
        " count(case when u.use_water_nature = 'business' then r.water_usage end) as businessWaterUsage,  \n" +
        " count(case when (u.use_water_nature = 'publicGreening' or u.use_water_nature = 'communityGreening') then r.water_usage end) as greenWaterUsage,  \n" +
        " count(case when u.use_water_nature = 'specialType' then r.water_usage end) as specialWaterUsage,  \n" +
        " count(water_usage) as totalWaterUsage  \n" +
        "\n" +
        "from waterfee_meter_reading_record r   \n" +
        " left join waterfee_meter m on r.meter_no = m.meter_no  \n" +
        " left join waterfee_user u on u.user_id = m.user_id  \n" +
        "where   \n" +
        " m.meter_type = 1  \n" +
        " and u.user_no is not null )")
    List<MechanicalWatchesByYearVO> getMechanicalWatchesByYear();

    /**
     * 机械表非居民用户抄表缴费明细表（搜索条件按月份）
     * @return
     */
    @Select("<script>" +
        "select  " +
            "  u.user_no, " +
            "  u.user_name, " +
            "  u.address, " +
            "  u.phone_number, " +
            "  u.certificate_number, " +
            "  r.reading_time, " +
            "  r.water_usage, " +
            "  b.total_amount, " +
            "  b.amount_paid, " +
            "  b.balance_due, " +
            "  p.payment_time " +
            "from waterfee_meter_reading_record r  " +
            "left join waterfee_meter m on r.meter_no = m.meter_no " +
            "left join waterfee_user u on u.user_id = m.user_id " +
            "left join waterfee_bills b on b.current_reading_id = r.record_id " +
            "left join waterfee_payment_detail p on p.bill_id = b.bill_id " +
            "where  " +
            "  m.meter_type = 1 " +
            "  and u.user_no is not null " +
            "  and r.del_flag = 0 " +
            "  and p.payment_detail_id is not null " +
            "  <if test=\"startTime != null and startTime != ''\"> " +
            "    and r.reading_time like CONCAT(#{startTime} , '%') " +
            "  </if>  order by r.reading_time desc"
        + "</script>"
    )
    List<MechanicalMeterNonResidentUsersPayMeterReadingVO> getMechanicalMeterNonResidentUsersPayMeterReading(@Param("startTime") String startTime);
}
