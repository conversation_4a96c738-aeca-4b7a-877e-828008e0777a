package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArSplit;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageArSplitVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageArSplitBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 应收账分账记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManageArSplitService {

    /**
     * 查询应收账分账记录
     *
     * @param id 主键
     * @return 应收账分账记录
     */
    WaterfeeChargeManageArSplit queryById(Long id);

    /**
     * 分页查询应收账分账记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收账分账记录分页列表
     */
    TableDataInfo<WaterfeeChargeManageArSplitVo> queryPageList(WaterfeeChargeManageArSplitBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应收账分账记录列表
     *
     * @param bo 查询条件
     * @return 应收账分账记录列表
     */
    List<WaterfeeChargeManageArSplitVo> queryList(WaterfeeChargeManageArSplitBo bo);

    /**
     * 新增应收账分账记录
     *
     * @param bo 应收账分账记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManageArSplitBo bo);

    /**
     * 修改应收账分账记录
     *
     * @param bo 应收账分账记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManageArSplitBo bo);

    /**
     * 校验并批量删除应收账分账记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
