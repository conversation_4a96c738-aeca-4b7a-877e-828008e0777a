package org.dromara.waterfee.meterRelation.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:24
 **/

@Data
@TableName("waterfee_meter_threshold_config")
@Schema(description = "总分表预警阈值配置")
public class WaterfeeMeterThresholdConfig {

    @TableId(type = IdType.AUTO)
    private Long configId;

    private Long parentMeterId;

    // 修改为可为空，当为总表整体阈值时，此字段为null
    private Long childMeterId;

    private BigDecimal threshold;

    private String tenantId;

    private String createBy;

    private LocalDateTime createTime;

    private String updateBy;

    private LocalDateTime updateTime;

    private String delFlag;
}
