package org.dromara.waterfee.priceManage.service;

import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeStandardPriceVo;

import java.util.List;
import java.util.Map;

/**
 * 价格方案缓存服务接口
 *
 * <AUTHOR> Assistant
 * @date 2023-11-15
 */
public interface WaterfeePriceCacheService {

    /**
     * 初始化价格方案缓存
     */
    void initPriceCache();

    /**
     * 刷新价格方案缓存
     */
    void refreshPriceCache();

    /**
     * 获取标准价格方案
     *
     * @param priceId 价格方案ID
     * @return 标准价格方案
     */
    WaterfeeStandardPriceVo getStandardPrice(Long priceId);

    /**
     * 获取阶梯价格方案
     *
     * @param priceId 价格方案ID
     * @return 阶梯价格方案
     */
    WaterfeePriceConfigVo getLadderPrice(Long priceId);

    /**
     * 获取价格方案类型
     *
     * @param priceId 价格方案ID
     * @return 价格方案类型 ("STANDARD" 或 "LADDER")
     */
    String getPriceType(Long priceId);

    /**
     * 获取所有标准价格方案
     *
     * @return 标准价格方案列表
     */
    List<WaterfeeStandardPriceVo> getAllStandardPrices();

    /**
     * 获取所有阶梯价格方案
     *
     * @return 阶梯价格方案列表
     */
    List<WaterfeePriceConfigVo> getAllLadderPrices();

    /**
     * 获取所有价格方案类型映射
     *
     * @return 价格方案ID到类型的映射
     */
    Map<Long, String> getAllPriceTypes();

    /**
     * 更新标准价格方案缓存
     *
     * @param priceVo 标准价格方案
     */
    void updateStandardPriceCache(WaterfeeStandardPriceVo priceVo);

    /**
     * 更新阶梯价格方案缓存
     *
     * @param priceVo 阶梯价格方案
     */
    void updateLadderPriceCache(WaterfeePriceConfigVo priceVo);

    /**
     * 删除标准价格方案缓存
     *
     * @param priceId 价格方案ID
     */
    void removeStandardPriceCache(Long priceId);

    /**
     * 删除阶梯价格方案缓存
     *
     * @param priceId 价格方案ID
     */
    void removeLadderPriceCache(Long priceId);
}
