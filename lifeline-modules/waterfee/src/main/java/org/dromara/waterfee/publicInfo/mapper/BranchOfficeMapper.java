package org.dromara.waterfee.publicInfo.mapper;

import org.dromara.waterfee.publicInfo.domain.BranchOffice;
import org.dromara.waterfee.publicInfo.domain.vo.BranchOfficeVo;
import org.dromara.waterfee.publicInfo.domain.bo.BranchOfficeBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 营业网点Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface BranchOfficeMapper extends BaseMapperPlus<BranchOffice, BranchOfficeVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<BranchOfficeVo>> P selectVoPage(IPage<BranchOffice> page, Wrapper<BranchOffice> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询营业网点列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<BranchOfficeVo> queryList(@Param("page") Page<BranchOffice> page, @Param("query") BranchOfficeBo query);

}
