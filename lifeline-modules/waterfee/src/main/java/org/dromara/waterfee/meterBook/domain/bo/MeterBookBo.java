package org.dromara.waterfee.meterBook.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.meterBook.domain.MeterBook;

/**
 * u6284u8868u624bu518cu4e1au52a1u5bf9u8c61
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MeterBook.class, reverseConvertGenerate = false)
public class MeterBookBo extends BaseEntity {

    /**
     * u4e3bu952eID
     */
    @NotNull(message = "IDu4e0du80fdu4e3au7a7a", groups = {EditGroup.class})
    private Long id;

    /**
     * u7ba1u8f96u533au57dfID
     */
    @NotNull(message = "u7ba1u8f96u533au57dfIDu4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    private Long areaId;

    /**
     * u624bu518cu7f16u53f7
     */
    @NotBlank(message = "u624bu518cu7f16u53f7u4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "u624bu518cu7f16u53f7u957fu5ea6u4e0du80fdu8d85u8fc750u4e2au5b57u7b26")
    private String bookNo;

    /**
     * u624bu518cu540du79f0
     */
    @NotBlank(message = "u624bu518cu540du79f0u4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "u624bu518cu540du79f0u957fu5ea6u4e0du80fdu8d85u8fc7100u4e2au5b57u7b26")
    private String bookName;

    /**
     * u6284u8868u65b9u5f0fuff08u4ebau5de5u6284u8868u3001u8fdcu4f20u6284u8868uff09
     */
    @NotBlank(message = "u6284u8868u65b9u5f0fu4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 20, message = "u6284u8868u65b9u5f0fu957fu5ea6u4e0du80fdu8d85u8fc720u4e2au5b57u7b26")
    private String readType;

    /**
     * u6284u8868u5468u671fuff081u4e00u6708u4e00u6284u30012u4e24u6708u4e00u62843u4e09u6708u4e00u6284uff09
     */
    @NotBlank(message = "u6284u8868u5468u671fu4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 20, message = "u6284u8868u5468u671fu957fu5ea6u4e0du80fdu8d85u8fc720u4e2au5b57u7b26")
    private String readCycle;

    /**
     * u6284u8868u4f8bu65e5
     */
    @NotNull(message = "u6284u8868u4f8bu65e5u4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 1, message = "u6284u8868u4f8bu65e5u4e0du80fdu5c0fu4e8e1")
    @Max(value = 31, message = "u6284u8868u4f8bu65e5u4e0du80fdu5927u4e8e31")
    private Integer readDay;

    /**
     * u6284u8868u57fau51c6u65e5
     */
    @NotNull(message = "u6284u8868u57fau51c6u65e5u4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 1, message = "u6284u8868u57fau51c6u65e5u4e0du80fdu5c0fu4e8e1")
    @Max(value = 31, message = "u6284u8868u57fau51c6u65e5u4e0du80fdu5927u4e8e31")
    private Integer readBaseDay;

    /**
     * u6284u8868u5458
     */
    @NotNull(message = "u6284u8868u5458u4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    private Long reader;

    /**
     * u6284u8868u5458u540du79f0
     */
    private String readerName;

    /**
     * u6284u8868u7ec4u957f
     */
    @NotNull(message = "u6284u8868u7ec4u957fu4e0du80fdu4e3au7a7a", groups = {AddGroup.class, EditGroup.class})
    private Long readerLeader;

    /**
     * u6284u8868u7ec4u957fu540du79f0
     */
    private String readerLeaderName;

    /**
     * u5907u6ce8
     */
    private String remark;

}
