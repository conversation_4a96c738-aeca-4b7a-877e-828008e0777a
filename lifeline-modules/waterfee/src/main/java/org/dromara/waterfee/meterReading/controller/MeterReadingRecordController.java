package org.dromara.waterfee.meterReading.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.bo.MeterReadingRecordBo;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;
import org.dromara.waterfee.meterReading.service.IMeterReadingRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 抄表记录
 * 前端访问路由地址为:/waterfee/meterReadingRecord
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/meterReadingRecord")
public class MeterReadingRecordController extends BaseController {

    private final IMeterReadingRecordService meterReadingRecordService;

    /**
     * 查询抄表记录列表
     */
    @SaCheckPermission("waterfee:meterReadingRecord:list")
    @GetMapping("/list")
    public TableDataInfo<MeterReadingRecordVo> list(WaterfeeMeterReadingRecord record, PageQuery pageQuery) {
        return meterReadingRecordService.queryPageList(record, pageQuery);
    }

    /**
     * 查询指定月份的抄表记录列表
     * 如果指定了任务ID，则使用任务的抄表月份；否则使用当前月份
     */
    @SaCheckPermission("waterfee:meterReadingRecord:list")
    @GetMapping("/listByMonth")
    public TableDataInfo<MeterReadingRecordVo> listByMonth(WaterfeeMeterReadingRecord record, PageQuery pageQuery) {
        return meterReadingRecordService.queryCurrentMonthList(record, pageQuery);
    }

    /**
     * 获取抄表记录详细信息
     *
     * @param recordId 主键
     */
    @SaCheckPermission("waterfee:meterReadingRecord:query")
    @GetMapping("/{recordId}")
    public R<MeterReadingRecordVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long recordId) {
        return R.ok(meterReadingRecordService.queryById(recordId));
    }

    /**
     * 根据水表编号查询最新一次的抄表记录
     *
     * @param meterNo 水表编号
     */
    @SaCheckPermission("waterfee:meterReadingRecord:query")
    @GetMapping("/latest/{meterNo}")
    public R<MeterReadingRecordVo> getLatestByMeterNo(@NotBlank(message = "水表编号不能为空") @PathVariable String meterNo) {
        return R.ok(meterReadingRecordService.queryLatestByMeterNo(meterNo));
    }

    /**
     * 查询水表关联信息
     *
     * @param meterNo 水表编号
     */
    @SaCheckPermission("waterfee:meterReadingRecord:query")
    @GetMapping("/meterInfo/{meterNo}")
    public R<Map<String, Object>> getMeterInfo(@NotBlank(message = "水表编号不能为空") @PathVariable String meterNo) {
        return R.ok(meterReadingRecordService.queryMeterInfo(meterNo));
    }

    /**
     * 根据水表编号查询所有已审核的抄表记录
     *
     * @param meterNo 水表编号
     */
    @SaCheckPermission("waterfee:meterReadingRecord:query")
    @GetMapping("/history/{meterNo}")
    public R<List<MeterReadingRecordVo>> getAllByMeterNo(@NotBlank(message = "水表编号不能为空") @PathVariable String meterNo) {
        return R.ok(meterReadingRecordService.queryAllByMeterNo(meterNo));
    }

    /**
     * 审核单条抄表记录
     *
     * @param recordId 抄表记录ID
     */
    @SaCheckPermission("waterfee:meterReadingRecord:audit")
    @Log(title = "审核抄表记录", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/{recordId}")
    public R<Void> auditRecord(@NotNull(message = "抄表记录ID不能为空") @PathVariable Long recordId) {
        return toAjax(meterReadingRecordService.auditRecord(recordId));
    }

    /**
     * 按表册审核抄表记录
     *
     * @param meterBookId 表册ID
     * @param taskId      任务ID
     */
    @SaCheckPermission("waterfee:meterReadingRecord:audit")
    @Log(title = "按表册审核抄表记录", businessType = BusinessType.UPDATE)
    @PutMapping("/audit/book")
    public R<Void> auditRecordsByBook(
            @NotNull(message = "表册ID不能为空") @RequestParam Long meterBookId,
            @NotNull(message = "任务ID不能为空") @RequestParam Long taskId) {
        return toAjax(meterReadingRecordService.auditRecordsByBook(meterBookId, taskId));
    }

    /**
     * 更新抄表记录
     *
     * @param record 抄表记录
     */
    @SaCheckPermission("waterfee:meterReadingRecord:edit")
    @Log(title = "更新抄表记录", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public R<Void> updateRecord(@Validated @RequestBody WaterfeeMeterReadingRecord record) {
        return toAjax(meterReadingRecordService.updateRecord(record));
    }

    /**
     * 根据表册ID查询机械表抄表记录
     *
     * @param meterBookId 表册ID
     */
    @SaCheckPermission("waterfee:meterReadingRecord:query")
    @GetMapping("/mechanical/{meterBookId}")
    public R<List<MeterReadingRecordVo>> getMechanicalMetersByBookId(
            @NotNull(message = "表册ID不能为空") @PathVariable Long meterBookId) {
        return R.ok(meterReadingRecordService.queryMechanicalMetersByBookId(meterBookId));
    }

    /**
     * 根据表册ID查询智能表抄表记录
     *
     * @param meterBookId 表册ID
     */
    @SaCheckPermission("waterfee:meterReadingRecord:query")
    @GetMapping("/intelligent/{meterBookId}")
    public R<List<MeterReadingRecordVo>> getIntelligentMetersByBookId(
            @NotNull(message = "表册ID不能为空") @PathVariable Long meterBookId) {
        return R.ok(meterReadingRecordService.queryIntelligentMetersByBookId(meterBookId));
    }

    /**
     * 新增抄表记录
     */
    @SaCheckPermission("waterfee:meterReadingRecord:add")
    @Log(title = "抄表记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MeterReadingRecordBo bo) {
        return toAjax(meterReadingRecordService.insertByBo(bo));
    }

    /**
     * 删除抄表记录
     *
     * @param recordIds 主键串
     */
    @SaCheckPermission("waterfee:meterReadingRecord:remove")
    @Log(title = "抄表记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] recordIds) {
        return toAjax(meterReadingRecordService.deleteWithValidByIds(List.of(recordIds), true));
    }

    /**
     * 挂起抄表记录
     */
    @SaCheckPermission("waterfee:meterReadingRecord:pending")
    @Log(title = "挂起抄表记录", businessType = BusinessType.UPDATE)
    @PutMapping("/pending")
    public R<Void> pendingRecord(@RequestBody Map<String, Object> params) {
        Long recordId = getLongParam(params, "recordId");
        String reason = (String) params.get("reason");
        
        if (recordId == null) {
            return R.fail("抄表记录ID不能为空");
        }
        
        if (reason == null || reason.trim().isEmpty()) {
            return R.fail("挂起原因不能为空");
        }
        
        return toAjax(meterReadingRecordService.pendingRecord(recordId, reason));
    }

    /**
     * 取消挂起抄表记录
     */
    @SaCheckPermission("waterfee:meterReadingRecord:pending")
    @Log(title = "取消挂起抄表记录", businessType = BusinessType.UPDATE)
    @PutMapping("/cancelPending")
    public R<Void> cancelPendingRecord(@RequestBody Map<String, Object> params) {
        Long recordId = getLongParam(params, "recordId");
        
        if (recordId == null) {
            return R.fail("抄表记录ID不能为空");
        }
        
        return toAjax(meterReadingRecordService.cancelPendingRecord(recordId));
    }
    
    /**
     * 从参数中获取Long类型值
     */
    private Long getLongParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 查询挂起状态的抄表记录列表
     */
    @SaCheckPermission("waterfee:meterReadingRecord:list")
    @GetMapping("/pendingList")
    public TableDataInfo<MeterReadingRecordVo> pendingList(WaterfeeMeterReadingRecord record, PageQuery pageQuery) {
        // 强制设置查询条件为挂起状态
        record.setIsPending("1");
        return meterReadingRecordService.queryPageList(record, pageQuery);
    }

    /**
     * 批量挂起抄表记录
     */
    @SaCheckPermission("waterfee:meterReadingRecord:pending")
    @Log(title = "批量挂起抄表记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchPending")
    public R<Void> batchPendingRecord(@RequestBody Map<String, Object> params) {
        Long[] recordIds = getRecordIdsFromParams(params);
        String reason = (String) params.get("reason");
        
        if (recordIds == null || recordIds.length == 0) {
            return R.fail("抄表记录ID不能为空");
        }
        
        if (reason == null || reason.trim().isEmpty()) {
            return R.fail("挂起原因不能为空");
        }
        
        boolean success = true;
        for (Long recordId : recordIds) {
            success = success && meterReadingRecordService.pendingRecord(recordId, reason);
        }
        return toAjax(success);
    }

    /**
     * 批量取消挂起抄表记录
     */
    @SaCheckPermission("waterfee:meterReadingRecord:pending")
    @Log(title = "批量取消挂起抄表记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batchCancelPending")
    public R<Void> batchCancelPendingRecord(@RequestBody Map<String, Object> params) {
        Long[] recordIds = getRecordIdsFromParams(params);
        
        if (recordIds == null || recordIds.length == 0) {
            return R.fail("抄表记录ID不能为空");
        }
        
        boolean success = true;
        for (Long recordId : recordIds) {
            success = success && meterReadingRecordService.cancelPendingRecord(recordId);
        }
        return toAjax(success);
    }
    
    /**
     * 从请求参数中获取记录ID数组
     */
    @SuppressWarnings("unchecked")
    private Long[] getRecordIdsFromParams(Map<String, Object> params) {
        Object recordIdsObj = params.get("recordIds");
        if (recordIdsObj instanceof Long[]) {
            return (Long[]) recordIdsObj;
        } else if (recordIdsObj instanceof List) {
            List<Object> recordIdsList = (List<Object>) recordIdsObj;
            return recordIdsList.stream()
                .map(id -> {
                    if (id instanceof Integer) {
                        return ((Integer) id).longValue();
                    } else if (id instanceof Long) {
                        return (Long) id;
                    } else if (id instanceof String) {
                        return Long.parseLong((String) id);
                    } else {
                        return null;
                    }
                })
                .filter(id -> id != null)
                .toArray(Long[]::new);
        }
        return new Long[0];
    }
}
