package org.dromara.waterfee.meterReading.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 账单处理优化配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "waterfee.bill-processing")
public class BillProcessingOptimizationConfig {

    /**
     * 批处理配置
     */
    private BatchConfig batch = new BatchConfig();

    /**
     * 并发配置
     */
    private ConcurrencyConfig concurrency = new ConcurrencyConfig();

    /**
     * 性能配置
     */
    private PerformanceConfig performance = new PerformanceConfig();

    /**
     * 支付配置
     */
    private PaymentConfig payment = new PaymentConfig();

    @Data
    public static class BatchConfig {
        /**
         * 账单批处理大小
         */
        private int billBatchSize = 200;

        /**
         * 支付批处理大小
         */
        private int paymentBatchSize = 100;

        /**
         * 最大重试次数
         */
        private int maxRetries = 3;

        /**
         * 批处理超时时间（毫秒）
         */
        private long timeoutMs = 60000;

        /**
         * 是否启用批量优化
         */
        private boolean enableBatchOptimization = true;
    }

    @Data
    public static class ConcurrencyConfig {
        /**
         * 最大并发批次数
         */
        private int maxConcurrentBatches = 5;

        /**
         * 账单处理线程池大小
         */
        private int billProcessingThreadPoolSize = 8;

        /**
         * 支付处理线程池大小
         */
        private int paymentProcessingThreadPoolSize = 4;

        /**
         * 线程池队列大小
         */
        private int queueCapacity = 1000;

        /**
         * 线程空闲时间（秒）
         */
        private int keepAliveSeconds = 60;
    }

    @Data
    public static class PerformanceConfig {
        /**
         * 是否启用性能监控
         */
        private boolean enablePerformanceMonitoring = true;

        /**
         * 慢处理阈值（毫秒）
         */
        private long slowProcessingThresholdMs = 10000;

        /**
         * 性能统计窗口大小
         */
        private int performanceWindowSize = 100;

        /**
         * 是否启用缓存
         */
        private boolean enableCaching = true;

        /**
         * 缓存过期时间（分钟）
         */
        private int cacheExpirationMinutes = 10;
    }

    @Data
    public static class PaymentConfig {
        /**
         * 是否启用自动支付
         */
        private boolean enableAutoPayment = true;

        /**
         * 支付超时时间（毫秒）
         */
        private long paymentTimeoutMs = 30000;

        /**
         * 最大支付重试次数
         */
        private int maxPaymentRetries = 2;

        /**
         * 支付失败后的等待时间（毫秒）
         */
        private long paymentRetryDelayMs = 1000;

        /**
         * 单次最大支付金额
         */
        private double maxPaymentAmount = 10000.0;

        /**
         * 是否检查客户余额
         */
        private boolean checkCustomerBalance = true;
    }

    /**
     * 获取账单批处理大小
     */
    public int getBillBatchSize() {
        return batch.getBillBatchSize();
    }

    /**
     * 获取支付批处理大小
     */
    public int getPaymentBatchSize() {
        return batch.getPaymentBatchSize();
    }

    /**
     * 获取最大并发批次数
     */
    public int getMaxConcurrentBatches() {
        return concurrency.getMaxConcurrentBatches();
    }

    /**
     * 是否启用批量优化
     */
    public boolean isBatchOptimizationEnabled() {
        return batch.isEnableBatchOptimization();
    }

    /**
     * 是否启用自动支付
     */
    public boolean isAutoPaymentEnabled() {
        return payment.isEnableAutoPayment();
    }

    /**
     * 是否启用性能监控
     */
    public boolean isPerformanceMonitoringEnabled() {
        return performance.isEnablePerformanceMonitoring();
    }

    /**
     * 获取慢处理阈值
     */
    public long getSlowProcessingThreshold() {
        return performance.getSlowProcessingThresholdMs();
    }

    /**
     * 验证配置的有效性
     */
    public void validateConfig() {
        if (batch.getBillBatchSize() <= 0) {
            throw new IllegalArgumentException("账单批处理大小必须大于0");
        }
        
        if (batch.getPaymentBatchSize() <= 0) {
            throw new IllegalArgumentException("支付批处理大小必须大于0");
        }
        
        if (concurrency.getMaxConcurrentBatches() <= 0) {
            throw new IllegalArgumentException("最大并发批次数必须大于0");
        }
        
        if (payment.getMaxPaymentAmount() <= 0) {
            throw new IllegalArgumentException("最大支付金额必须大于0");
        }
        
        if (batch.getTimeoutMs() <= 0) {
            throw new IllegalArgumentException("批处理超时时间必须大于0");
        }
    }

    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format(
            "账单处理优化配置 - 账单批次:%d, 支付批次:%d, 最大并发:%d, 自动支付:%s, 性能监控:%s",
            getBillBatchSize(),
            getPaymentBatchSize(),
            getMaxConcurrentBatches(),
            isAutoPaymentEnabled() ? "启用" : "禁用",
            isPerformanceMonitoringEnabled() ? "启用" : "禁用"
        );
    }
}
