package org.dromara.waterfee.chargeManage.mapper;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdRecord;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdRecordVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdRecordBo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * 代扣记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface WaterfeeChargeManageWithholdRecordMapper extends BaseMapperPlus<WaterfeeChargeManageWithholdRecord, WaterfeeChargeManageWithholdRecordVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeChargeManageWithholdRecordVo>> P selectVoPage(IPage<WaterfeeChargeManageWithholdRecord> page, Wrapper<WaterfeeChargeManageWithholdRecord> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

    /**
    * 查询代扣记录列表
    */
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    Page<WaterfeeChargeManageWithholdRecordVo> queryList(@Param("page") Page<WaterfeeChargeManageWithholdRecord> page, @Param("query") WaterfeeChargeManageWithholdRecordBo query);

}
