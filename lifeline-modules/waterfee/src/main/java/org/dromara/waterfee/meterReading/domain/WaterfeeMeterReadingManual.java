package org.dromara.waterfee.meterReading.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 抄表补录对象 waterfee_meter_reading_manual
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_meter_reading_manual")
public class WaterfeeMeterReadingManual extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 创建者
     */
    @TableField(exist = false)
    private Long createBy;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private Long updateBy;

    /**
     * 补录ID
     */
    @TableId(value = "manual_id")
    private Long manualId;

    /**
     * 水表编号
     */
    private String meterNo;

    /**
     * 本期抄表读数
     */
    private Double currentReading;

    /**
     * 补录时间
     */
    private Date readingTime;

    /**
     * 补录原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(value = "meter_book_id")
    private Long meterBookId;
}
