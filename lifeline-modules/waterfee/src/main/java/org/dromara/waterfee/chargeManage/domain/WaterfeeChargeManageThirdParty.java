package org.dromara.waterfee.chargeManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 第三方对账记录对象 waterfee_charge_manage_third_party
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_charge_manage_third_party")
public class WaterfeeChargeManageThirdParty extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 渠道编码（UNIONPAY、WECHAT等）
     */
    private String channelCode;

    /**
     * 本地订单号
     */
    private String localOrderNo;

    /**
     * 第三方订单号
     */
    private String thirdOrderNo;

    /**
     * 
     */
    private Long amount;

    /**
     * 状态（MATCHED、MISMATCHED、PENDING）
     */
    private String status;

    /**
     * 对账日期
     */
    private Date reconciliationDate;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    @TableLogic
    private String delFlag;


}
