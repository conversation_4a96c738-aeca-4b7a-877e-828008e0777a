package org.dromara.waterfee.statisticalReport.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.waterfee.bill.mapper.PaymentDetailMapper;
import org.dromara.waterfee.bill.mapper.WaterfeeBillMapper;
import org.dromara.waterfee.meter.mapper.MeterChangeRecordMapper;
import org.dromara.waterfee.meterBook.mapper.MeterBookMapper;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterReadingRecordMapper;
import org.dromara.waterfee.statisticalReport.domain.*;
import org.dromara.waterfee.statisticalReport.service.StatisticalReportService;
import org.dromara.waterfee.user.mapper.WaterfeeUserBasicInfoChangeRecordMapper;
import org.dromara.waterfee.user.mapper.WaterfeeUserMapper;
import org.dromara.waterfee.user.mapper.WaterfeeUserTransferOwnershipRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class StatisticalReportServiceImpl implements StatisticalReportService {
    @Autowired
    private MeterBookMapper meterBookMapper;

    @Autowired
    private WaterfeeUserMapper userMapper;

    @Autowired
    private WaterfeeMeterReadingRecordMapper readingRecordMapper;

    @Autowired
    private WaterfeeBillMapper billMapper;

    @Autowired
    private PaymentDetailMapper paymentDetailMapper;

    @Autowired
    private WaterfeeUserTransferOwnershipRecordMapper ownershipRecordMapper;

    @Autowired
    private MeterChangeRecordMapper changeRecordMapper;

    @Autowired
    private WaterfeeUserBasicInfoChangeRecordMapper basicInfoChangeRecordMapper;

    /**
     * 1、按表册统计用户数（包括机械表和物联网表/不包括销户和停户的户数）
     * @return
     */
    @Override
    public List<TableBookStatisticsNumberOfUsersVO> getTableBookStatisticsNumberOfUsers() {
        return meterBookMapper.getTableBookStatisticsNumberOfUsers();
    }

    /**
     * 2、按性质统计用户明细表（搜索条件是用户性质/不包括销户和停户的户数）
     * @param customerNature
     * @return
     */
    @Override
    public List<NatureStatisticsUserVO> getNatureStatisticsUser(String customerNature) {
        return userMapper.getNatureStatisticsUser(customerNature);
    }

    /**
     * 4.机械表抄表员户数统计明细表（搜索条件是抄表员和状态）
     * @return
     */
    @Override
    public List<MechanicalWatchMeterNumberStatisticsVO> getMechanicalWatchMeterNumberStatistics(String readerName, String userStatus) {
        return meterBookMapper.getMechanicalWatchMeterNumberStatistics(readerName, userStatus);
    }

    /**
     * 机械表抄表率统计
     * @return
     */
    @Override
    public List<MechanicalMeterReadingRateStatisticsVO> getMechanicalMeterReadingRateStatistics() {
        // 查询本季度的开始和结束时间
        Date quarterStartTime = DateUtils.getQuarterStartTime();
        Date quarterEndTime = DateUtils.getNextQuarterStartTime();
        return meterBookMapper.getMechanicalMeterReadingRateStatistics(quarterStartTime, quarterEndTime);
    }

    /**
     * 6.机械表抄表明细表
     * @return
     */
    @Override
    public List<MechanicalReadingIndicatesFineVO> getMechanicalReadingIndicatesFine(String startTime, String endTime) {
        return readingRecordMapper.getMechanicalReadingIndicatesFine(startTime, endTime);
    }

    /**
     * 7.机械表按年份统计抄表数据
     * @return
     */
    @Override
    public List<MechanicalWatchesByYearVO> getMechanicalWatchesByYear() {
        return readingRecordMapper.getMechanicalWatchesByYear();
    }

    /**
     * 8.机械表非居民用户抄表缴费明细表（搜索条件按月份）
     * @return
     */
    @Override
    public List<MechanicalMeterNonResidentUsersPayMeterReadingVO> getMechanicalMeterNonResidentUsersPayMeterReading(String startTime) {
        return readingRecordMapper.getMechanicalMeterNonResidentUsersPayMeterReading(startTime);
    }

    /**
     * 9.机械表欠费明细表（搜索条件是区册）
     * @return
     */
    @Override
    public List<MechanicalWatchArrearsDetailsVO> getMechanicalWatchArrearsDetails(Long meterBookId) {
        return billMapper.getMechanicalWatchArrearsDetails(meterBookId);
    }

    /**
     * 10.收款明细表（搜索条件是收费员、支付方式、起始日期和截止日期）
     * @param tollCollector
     * @param paymentMethod
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<PaymentDetailsVO> getPaymentDetails(String tollCollector, String paymentMethod, String startTime, String endTime) {
        return paymentDetailMapper.getPaymentDetails(tollCollector, paymentMethod, startTime, endTime);
    }

    /**
     * 11、网上缴费明细汇总表（搜索条件是支付方式）
     * @param paymentMethod
     * @return
     */
    @Override
    public List<OnlinePaymentDetailSummaryVO> getOnlinePaymentDetailSummary(String paymentMethod) {
        return paymentDetailMapper.getOnlinePaymentDetailSummary(paymentMethod);
    }

    /**
     * 12、新开户用户明细表
     * @param year
     * @return
     */
    @Override
    public List<NewAccountUserDetailsVO> getNewAccountUserDetails(String year) {
        return userMapper.getNewAccountUserDetails(year);
    }

    /**
     * 13、用户过户明细表
     * @return
     */
    @Override
    public List<UserTransferDetailsVO> getUserTransferDetails() {
        return ownershipRecordMapper.getUserTransferDetails();
    }

    /**
     * 14、用户换表修改表数明细表
     * @return
     */
    @Override
    public List<UserChangeTableCountVO> getUserChangeTableCount() {
        return changeRecordMapper.getUserChangeTableCount();
    }

    /**
     * 15、用水类型更改明细表
     * @return
     */
    @Override
    public List<WaterTypeChangeDetailsVO> getWaterTypeChangeDetails() {
        return basicInfoChangeRecordMapper.getWaterTypeChangeDetails();
    }
}
