package org.dromara.waterfee.priceManage.service;

import org.dromara.waterfee.priceManage.domain.WaterfeeSurchargeConfigs;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeSurchargeConfigsVo;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeSurchargeConfigsBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 附加费配置 (Surcharge Configurations)Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface IWaterfeeSurchargeConfigsService {

    /**
     * 查询附加费配置 (Surcharge Configurations)
     *
     * @param id 主键
     * @return 附加费配置 (Surcharge Configurations)
     */
    WaterfeeSurchargeConfigs queryById(Long id);

    /**
     * 分页查询附加费配置 (Surcharge Configurations)列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 附加费配置 (Surcharge Configurations)分页列表
     */
    TableDataInfo<WaterfeeSurchargeConfigsVo> queryPageList(WaterfeeSurchargeConfigsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的附加费配置 (Surcharge Configurations)列表
     *
     * @param bo 查询条件
     * @return 附加费配置 (Surcharge Configurations)列表
     */
    List<WaterfeeSurchargeConfigsVo> queryList(WaterfeeSurchargeConfigsBo bo);

    /**
     * 新增附加费配置 (Surcharge Configurations)
     *
     * @param bo 附加费配置 (Surcharge Configurations)
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeSurchargeConfigsBo bo);

    /**
     * 修改附加费配置 (Surcharge Configurations)
     *
     * @param bo 附加费配置 (Surcharge Configurations)
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeSurchargeConfigsBo bo);

    /**
     * 校验并批量删除附加费配置 (Surcharge Configurations)信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
