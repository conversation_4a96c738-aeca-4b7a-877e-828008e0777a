package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManagePenaltyAdjustment;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManagePenaltyAdjustmentVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManagePenaltyAdjustmentBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManagePenaltyAdjustmentService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 违约金调整/减免
 * 前端访问路由地址为:/waterfee/chargeManagePenaltyAdjustment
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManagePenaltyAdjustment")
public class WaterfeeChargeManagePenaltyAdjustmentController extends BaseController {

    private final IWaterfeeChargeManagePenaltyAdjustmentService waterfeeChargeManagePenaltyAdjustmentService;

    /**
     * 查询违约金调整/减免列表
     */
    @SaCheckPermission("waterfee:chargeManagePenaltyAdjustment:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManagePenaltyAdjustmentVo> list(WaterfeeChargeManagePenaltyAdjustmentBo bo, PageQuery pageQuery) {
        return waterfeeChargeManagePenaltyAdjustmentService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出违约金调整/减免列表
     */
    @SaCheckPermission("waterfee:chargeManagePenaltyAdjustment:export")
    @Log(title = "违约金调整/减免", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManagePenaltyAdjustmentBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManagePenaltyAdjustmentVo> list = waterfeeChargeManagePenaltyAdjustmentService.queryList(bo);
        ExcelUtil.exportExcel(list, "违约金调整/减免", WaterfeeChargeManagePenaltyAdjustmentVo.class, response);
    }

    /**
     * 获取违约金调整/减免详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManagePenaltyAdjustment:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManagePenaltyAdjustment> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManagePenaltyAdjustmentService.queryById(id));
    }

    /**
     * 新增违约金调整/减免
     */
    @SaCheckPermission("waterfee:chargeManagePenaltyAdjustment:add")
    @Log(title = "违约金调整/减免", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManagePenaltyAdjustmentBo bo) {
        return toAjax(waterfeeChargeManagePenaltyAdjustmentService.insertByBo(bo));
    }

    /**
     * 修改违约金调整/减免
     */
    @SaCheckPermission("waterfee:chargeManagePenaltyAdjustment:edit")
    @Log(title = "违约金调整/减免", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManagePenaltyAdjustmentBo bo) {
        return toAjax(waterfeeChargeManagePenaltyAdjustmentService.updateByBo(bo));
    }

    /**
     * 删除违约金调整/减免
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManagePenaltyAdjustment:remove")
    @Log(title = "违约金调整/减免", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManagePenaltyAdjustmentService.deleteWithValidByIds(List.of(ids), true));
    }
}
