package org.dromara.waterfee.meterReading.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.bo.MeterReadingRecordBo;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 抄表记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface IMeterReadingRecordService {

    /**
     * 查询抄表记录
     *
     * @param recordId 主键
     * @return 抄表记录
     */
    MeterReadingRecordVo queryById(Long recordId);

    /**
     * 查询抄表记录列表
     *
     * @param record 抄表记录
     * @return 抄表记录集合
     */
    TableDataInfo<MeterReadingRecordVo> queryPageList(WaterfeeMeterReadingRecord record, PageQuery pageQuery);

    /**
     * 查询抄表记录列表
     *
     * @param record 抄表记录
     * @return 抄表记录集合
     */
    List<MeterReadingRecordVo> queryList(WaterfeeMeterReadingRecord record);

    /**
     * 新增抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    Boolean insertByEntity(WaterfeeMeterReadingRecord record);

    /**
     * 修改抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    Boolean updateByEntity(WaterfeeMeterReadingRecord record);

    /**
     * 校验并批量删除抄表记录
     *
     * @param ids     需要删除的抄表记录主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据水表编号查询最新一次的抄表记录
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    MeterReadingRecordVo queryLatestByMeterNo(String meterNo);

    /**
     * 根据水表编号查询最新一次的抄表记录（实体）
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    WaterfeeMeterReadingRecord queryLatestEntityByMeterNo(String meterNo);

    /**
     * 批量查询多个水表的最新抄表记录（优化版本）
     *
     * @param meterNos 水表编号列表
     * @return 水表编号到最新记录的映射
     */
    Map<String, WaterfeeMeterReadingRecord> batchQueryLatestEntityByMeterNos(List<String> meterNos);

    /**
     * 查询水表关联信息
     *
     * @param meterNo 水表编号
     * @return 水表关联信息
     */
    Map<String, Object> queryMeterInfo(String meterNo);

    /**
     * 根据水表编号查询所有已审核的抄表记录
     *
     * @param meterNo 水表编号
     * @return 抄表记录列表
     */
    List<MeterReadingRecordVo> queryAllByMeterNo(String meterNo);

    /**
     * 审核单条抄表记录
     *
     * @param recordId 抄表记录ID
     * @return 结果
     */
    Boolean auditRecord(Long recordId);

    List<Long> auditRecordRtnBillIds(WaterfeeMeterReadingRecord record);

    /**
     * 按表册审核抄表记录
     *
     * @param meterBookId 表册ID
     * @param taskId      任务ID
     * @return 结果
     */
    Boolean auditRecordsByBook(Long meterBookId, Long taskId);

    /**
     * 更新抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    Boolean updateRecord(WaterfeeMeterReadingRecord record);

    /**
     * 根据表册ID查询机械表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 机械表抄表记录列表
     */
    List<MeterReadingRecordVo> queryMechanicalMetersByBookId(Long meterBookId);

    /**
     * 根据表册ID查询智能表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 智能表抄表记录列表
     */
    List<MeterReadingRecordVo> queryIntelligentMetersByBookId(Long meterBookId);

    /**
     * 查询本月抄表记录列表
     *
     * @param record    抄表记录
     * @param pageQuery 分页参数
     * @return 抄表记录列表
     */
    TableDataInfo<MeterReadingRecordVo> queryCurrentMonthList(WaterfeeMeterReadingRecord record, PageQuery pageQuery);

    /**
     * 新增抄表记录
     *
     * @param bo 抄表记录业务对象
     * @return 结果
     */
    Boolean insertByBo(MeterReadingRecordBo bo);

    /**
     * 批量新增抄表记录（优化版本）
     *
     * @param bos 抄表记录业务对象列表
     * @return 成功插入的记录数
     */
    int batchInsertByBos(List<MeterReadingRecordBo> bos);

    boolean existsByTaskIdAndMeterNo(Long taskId, String meterNo);

    /**
     * 判断某水表在某个季度是否已有记录
     *
     * @param date
     * @param taskId
     * @param meterNo
     * @return
     */
    boolean existsByQuarterAndMeterNo(Date date, Long taskId, String meterNo);

    /**
     * 挂起抄表记录
     *
     * @param recordId 抄表记录ID
     * @param reason   挂起原因
     * @return 结果
     */
    Boolean pendingRecord(Long recordId, String reason);

    /**
     * 取消挂起抄表记录
     *
     * @param recordId 抄表记录ID
     * @return 结果
     */
    Boolean cancelPendingRecord(Long recordId);
}
