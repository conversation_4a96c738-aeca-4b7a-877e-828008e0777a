package org.dromara.waterfee.meterReading.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeReadingTaskBo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo;

import java.util.Collection;
import java.util.List;

/**
 * 抄表任务Service接口
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface IWaterfeeReadingTaskService {

    /**
     * 查询抄表任务
     *
     * @param taskId 主键
     * @return 抄表任务
     */
    WaterfeeReadingTaskVo queryById(Long taskId);

    /**
     * 查询抄表任务列表
     *
     * @param bo 抄表任务
     * @return 抄表任务集合
     */
    TableDataInfo<WaterfeeReadingTaskVo> queryPageList(WaterfeeReadingTaskBo bo, PageQuery pageQuery);

    /**
     * 查询抄表任务列表
     *
     * @param bo 抄表任务
     * @return 抄表任务集合
     */
    List<WaterfeeReadingTaskVo> queryList(WaterfeeReadingTaskBo bo);

    /**
     * 新增抄表任务
     *
     * @param bo 抄表任务
     * @return 结果
     */
    Boolean insertByBo(WaterfeeReadingTaskBo bo);

    /**
     * 修改抄表任务
     *
     * @param bo 抄表任务
     * @return 结果
     */
    Boolean updateByBo(WaterfeeReadingTaskBo bo);

    /**
     * 校验并批量删除抄表任务
     *
     * @param ids     需要删除的抄表任务主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 暂停抄表任务
     * 对已设置自动循环的任务可终止操作，对已生成的任务不影响，但是影响下一次
     *
     * @param taskId 任务ID
     * @return 结果
     */
    Boolean pauseTask(Long taskId);

    /**
     * 启用抄表任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    Boolean enableTask(Long taskId);

    /**
     * 下发抄表任务
     * 只能针对非自动循环设置的任务有效
     *
     * @param taskId 任务ID
     * @return 结果
     */
    Boolean dispatchTask(Long taskId);

    /**
     * 更新表册关联的用户数和水表数
     *
     * @param taskId 任务ID
     * @return 结果
     */
    Boolean updateMeterAndUserCount(Long taskId);

    /**
     * 批量更新表册关联的用户数和水表数
     *
     * @param taskIds 任务ID集合
     * @return 结果
     */
    Boolean updateMeterAndUserCountBatch(Collection<Long> taskIds);
}
