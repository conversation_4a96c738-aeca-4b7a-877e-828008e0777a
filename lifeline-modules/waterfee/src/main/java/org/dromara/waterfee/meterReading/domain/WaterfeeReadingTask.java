package org.dromara.waterfee.meterReading.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 抄表任务对象 waterfee_meter_reading_task
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_meter_reading_task")
public class WaterfeeReadingTask extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
    @TableField(exist = false)
    private Long createBy;

    /**
     * 更新者
     */
    @TableField(exist = false)
    private Long updateBy;

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 任务ID
     */
    @TableId(value = "task_id")
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 营业区域ID
     */
    private Long businessAreaId;

    /**
     * 抄表手册ID
     */
    private Long meterBookId;

    /**
     * 抄表员ID
     */
    private Long readerId;

    /**
     * 抄表员姓名
     */
    private String readerName;

    /**
     * 抄表方式（字典waterfee_reading_method）
     */
    private String readingMethod;

    /**
     * 抄表周期（字典waterfee_reading_cycle）
     */
    private String readingCycle;

    /**
     * 抄表例日（具体安排哪一天进行抄表，通常是设置成每个月固定的一天，比如每月5号）
     */
    private Integer readingDay;

    /**
     * 抄表基准日（系统计算账单的基准日期，一般也设置成每月的某一天，比如20号）
     */
    private Integer baseDay;

    /**
     * 是否循环（0-否 1-是）
     */
    private String isCycle;

    /**
     * 任务状态（0-暂停 1-正常）
     */
    private String taskStatus;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 上次执行时间
     */
    private Date lastExecuteTime;

    /**
     * 下次执行时间
     */
    private Date nextExecuteTime;

    /**
     * 表册关联用户数量
     */
    private Integer bookUserNum;

    /**
     * 表册关联水表数量
     */
    private Integer planReadingNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 是否审核（0-未审核 1-已审核）
     */
    private String isAudited;

    /**
     * 实际抄表数
     */
    private Integer actualReadingNum;

    /**
     * 抄表月份，格式：yyyy-MM
     */
    private String readingMonth;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核人姓名
     */
    private String auditorName;
}
