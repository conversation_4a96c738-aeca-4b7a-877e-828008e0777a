package org.dromara.waterfee.priceManage.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.priceManage.domain.WaterfeeStandardPrice;

/**
 * 标准价格业务对象 waterfee_standard_price
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeStandardPrice.class, reverseConvertGenerate = false)
public class WaterfeeStandardPriceBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 价格名称
     */
    @NotBlank(message = "价格名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long price;

    /**
     * 用水性质
     */
    @NotNull(message = "用水性质不能为空", groups = { AddGroup.class, EditGroup.class })
    private String waterUseType;
    /**
     * 是否关联违约金
     */
    private Boolean penaltyEnabled;

    /**
     * 违约金ID
     */
    private Long penaltyId;
    /**
     * 违约金名称
     */
    private String penaltyName;

    /**
     * 是否关联附加费
     */
    private Boolean additionalEnabled;

    /**
     * 附加费ID列表（JSON数组）
     */
    private String additionalFeeIds;

    /**
     * 附加费名称列表（JSON数组）
     */
    private String additionalFeeNames;

    /**
     * 描述
     */
    private String description;


}
