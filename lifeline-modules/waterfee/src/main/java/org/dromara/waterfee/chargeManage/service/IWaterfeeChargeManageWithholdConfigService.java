package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdConfig;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdConfigVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdConfigBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 代扣配置信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManageWithholdConfigService {

    /**
     * 查询代扣配置信息
     *
     * @param id 主键
     * @return 代扣配置信息
     */
    WaterfeeChargeManageWithholdConfig queryById(Long id);

    /**
     * 分页查询代扣配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代扣配置信息分页列表
     */
    TableDataInfo<WaterfeeChargeManageWithholdConfigVo> queryPageList(WaterfeeChargeManageWithholdConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的代扣配置信息列表
     *
     * @param bo 查询条件
     * @return 代扣配置信息列表
     */
    List<WaterfeeChargeManageWithholdConfigVo> queryList(WaterfeeChargeManageWithholdConfigBo bo);

    /**
     * 新增代扣配置信息
     *
     * @param bo 代扣配置信息
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManageWithholdConfigBo bo);

    /**
     * 修改代扣配置信息
     *
     * @param bo 代扣配置信息
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManageWithholdConfigBo bo);

    /**
     * 校验并批量删除代扣配置信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
