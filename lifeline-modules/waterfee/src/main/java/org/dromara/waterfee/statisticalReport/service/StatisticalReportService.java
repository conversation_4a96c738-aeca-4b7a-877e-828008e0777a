package org.dromara.waterfee.statisticalReport.service;

import org.dromara.waterfee.statisticalReport.domain.*;

import java.util.List;

public interface StatisticalReportService {
    /**
     * 1、按表册统计用户数（包括机械表和物联网表/不包括销户和停户的户数）
     * @return
     */
    List<TableBookStatisticsNumberOfUsersVO> getTableBookStatisticsNumberOfUsers();

    /**
     * 2、按性质统计用户明细表（搜索条件是用户性质/不包括销户和停户的户数）
     * @param customerNature
     * @return
     */
    List<NatureStatisticsUserVO> getNatureStatisticsUser(String customerNature);

    /**
     * 4.机械表抄表员户数统计明细表（搜索条件是抄表员和状态）
     * @return
     */
    List<MechanicalWatchMeterNumberStatisticsVO> getMechanicalWatchMeterNumberStatistics(String readerName, String userStatus);

    /**
     * 5.机械表抄表率统计
     * @return
     */
    List<MechanicalMeterReadingRateStatisticsVO> getMechanicalMeterReadingRateStatistics();

    /**
     * 6.机械表抄表明细表
     * @return
     */
    List<MechanicalReadingIndicatesFineVO> getMechanicalReadingIndicatesFine(String startTime, String endTime);

    /**
     * 7.机械表按年份统计抄表数据
     * @return
     */
    List<MechanicalWatchesByYearVO> getMechanicalWatchesByYear();

    /**
     * 8.机械表非居民用户抄表缴费明细表（搜索条件按月份）
     * @return
     */
    List<MechanicalMeterNonResidentUsersPayMeterReadingVO> getMechanicalMeterNonResidentUsersPayMeterReading(String startTime);

    /**
     * 9.机械表欠费明细表（搜索条件是区册）
     * @return
     */
    List<MechanicalWatchArrearsDetailsVO> getMechanicalWatchArrearsDetails(Long meterBookId);

    /**
     * 10.收款明细表（搜索条件是收费员、支付方式、起始日期和截止日期）
     * @return
     */
    List<PaymentDetailsVO> getPaymentDetails(String tollCollector, String paymentMethod, String startTime, String endTime);

    /**
     * 11、网上缴费明细汇总表（搜索条件是支付方式）
     * @param paymentMethod
     * @return
     */
    List<OnlinePaymentDetailSummaryVO>getOnlinePaymentDetailSummary(String paymentMethod);

    /**
     * 12、新开户用户明细表
     * @return
     */
    List<NewAccountUserDetailsVO> getNewAccountUserDetails(String year);

    /**
     * 13、用户过户明细表
     * @return
     */
    List<UserTransferDetailsVO> getUserTransferDetails();

    /**
     * 14、用户换表修改表数明细表
     * @return
     */
    List<UserChangeTableCountVO> getUserChangeTableCount();

    /**
     * 15、用水类型更改明细表
     * @return
     */
    List<WaterTypeChangeDetailsVO> getWaterTypeChangeDetails();
}
