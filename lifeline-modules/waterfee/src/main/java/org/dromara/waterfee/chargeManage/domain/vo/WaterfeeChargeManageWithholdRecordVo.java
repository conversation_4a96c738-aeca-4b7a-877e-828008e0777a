package org.dromara.waterfee.chargeManage.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 代扣记录视图对象 waterfee_charge_manage_withhold_record
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManageWithholdRecord.class)
public class WaterfeeChargeManageWithholdRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long userId;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long amount;

    /**
     * 扣款时间
     */
    @ExcelProperty(value = "扣款时间")
    private Date withholdTime;

    /**
     * 状态（PENDING、SUCCESS、FAILED）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "P=ENDING、SUCCESS、FAILED")
    private String status;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
