package org.dromara.waterfee.meterReading.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 抄表任务视图对象 waterfee_meter_reading_task
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeReadingTask.class)
public class WaterfeeReadingTaskVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @ExcelProperty(value = "任务ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 任务名称
     */
    @ExcelProperty(value = "任务名称")
    private String taskName;

    /**
     * 营业区域ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long businessAreaId;

    /**
     * 营业区域名称
     */
    @ExcelProperty(value = "营业区域")
    private String businessAreaName;

    /**
     * 抄表手册ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long meterBookId;

    /**
     * 抄表手册名称
     */
    @ExcelProperty(value = "抄表手册")
    private String meterBookName;

    /**
     * 抄表员ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long readerId;

    /**
     * 抄表员姓名
     */
    @ExcelProperty(value = "抄表员")
    private String readerName;

    /**
     * 抄表方式（字典waterfee_reading_method）
     */
    @ExcelProperty(value = "抄表方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_reading_method")
    private String readingMethod;

    /**
     * 抄表周期（字典waterfee_reading_cycle）
     */
    @ExcelProperty(value = "抄表周期", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_reading_cycle")
    private String readingCycle;

    /**
     * 抄表例日（具体安排哪一天进行抄表，通常是设置成每个月固定的一天，比如每月5号）
     */
    @ExcelProperty(value = "抄表例日")
    private Integer readingDay;

    /**
     * 抄表基准日（系统计算账单的基准日期，一般也设置成每月的某一天，比如20号）
     */
    @ExcelProperty(value = "抄表基准日")
    private Integer baseDay;

    /**
     * 是否循环（0-否 1-是）
     */
    @ExcelProperty(value = "是否循环", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=否,1=是")
    private String isCycle;

    /**
     * 任务状态（0-暂停 1-正常）
     */
    @ExcelProperty(value = "任务状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "waterfee_task_status")
    private String taskStatus;

    /**
     * 开始日期
     */
    @ExcelProperty(value = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @ExcelProperty(value = "结束日期")
    private Date endDate;

    /**
     * 上次执行时间
     */
    @ExcelProperty(value = "上次执行时间")
    private Date lastExecuteTime;

    /**
     * 下次执行时间
     */
    @ExcelProperty(value = "下次执行时间")
    private Date nextExecuteTime;

    /**
     * 表册关联用户数量
     */
    @ExcelProperty(value = "表册关联用户数")
    private Integer bookUserNum;

    /**
     * 表册关联水表数量
     */
    @ExcelProperty(value = "表册关联水表数")
    private Integer planReadingNum;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否审核（0-未审核 1-已审核）
     */
    @ExcelProperty(value = "是否审核", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未审核,1=已审核")
    private String isAudited;

    /**
     * 实际抄表数
     */
    @ExcelProperty(value = "实际抄表数")
    private Integer actualReadingNum;

    /**
     * 抄表月份
     */
    @ExcelProperty(value = "抄表月份")
    private String readingMonth;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date auditTime;

    /**
     * 审核人姓名
     */
    @ExcelProperty(value = "审核人")
    private String auditorName;
}
