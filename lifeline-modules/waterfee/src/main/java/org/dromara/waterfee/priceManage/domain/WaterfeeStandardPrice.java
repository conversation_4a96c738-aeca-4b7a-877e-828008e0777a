package org.dromara.waterfee.priceManage.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 标准价格对象 waterfee_standard_price
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_standard_price")
public class WaterfeeStandardPrice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 价格名称
     */
    private String name;

    /**
     * 价格
     */
    private Long price;

    /**
     * 用水性质
     */
    private String waterUseType;

    /**
     * 是否关联违约金
     */
    private Boolean penaltyEnabled;

    /**
     * 违约金ID
     */
    private Long penaltyId;

    /**
     * 是否关联附加费
     */
    private Boolean additionalEnabled;

    /**
     * 附加费ID列表（JSON数组）
     */
    private String additionalFeeIds;

    /**
     * 描述
     */
    private String description;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
