package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageThirdParty;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageThirdPartyVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageThirdPartyBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageThirdPartyService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 第三方对账记录
 * 前端访问路由地址为:/waterfee/chargeManageThirdParty
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManageThirdParty")
public class WaterfeeChargeManageThirdPartyController extends BaseController {

    private final IWaterfeeChargeManageThirdPartyService waterfeeChargeManageThirdPartyService;

    /**
     * 查询第三方对账记录列表
     */
    @SaCheckPermission("waterfee:chargeManageThirdParty:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManageThirdPartyVo> list(WaterfeeChargeManageThirdPartyBo bo, PageQuery pageQuery) {
        return waterfeeChargeManageThirdPartyService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出第三方对账记录列表
     */
    @SaCheckPermission("waterfee:chargeManageThirdParty:export")
    @Log(title = "第三方对账记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManageThirdPartyBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManageThirdPartyVo> list = waterfeeChargeManageThirdPartyService.queryList(bo);
        ExcelUtil.exportExcel(list, "第三方对账记录", WaterfeeChargeManageThirdPartyVo.class, response);
    }

    /**
     * 获取第三方对账记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManageThirdParty:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManageThirdParty> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManageThirdPartyService.queryById(id));
    }

    /**
     * 新增第三方对账记录
     */
    @SaCheckPermission("waterfee:chargeManageThirdParty:add")
    @Log(title = "第三方对账记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManageThirdPartyBo bo) {
        return toAjax(waterfeeChargeManageThirdPartyService.insertByBo(bo));
    }

    /**
     * 修改第三方对账记录
     */
    @SaCheckPermission("waterfee:chargeManageThirdParty:edit")
    @Log(title = "第三方对账记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManageThirdPartyBo bo) {
        return toAjax(waterfeeChargeManageThirdPartyService.updateByBo(bo));
    }

    /**
     * 删除第三方对账记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManageThirdParty:remove")
    @Log(title = "第三方对账记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManageThirdPartyService.deleteWithValidByIds(List.of(ids), true));
    }
}
