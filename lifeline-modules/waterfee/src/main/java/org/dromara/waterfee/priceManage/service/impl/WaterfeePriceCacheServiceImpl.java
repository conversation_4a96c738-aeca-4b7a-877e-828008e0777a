package org.dromara.waterfee.priceManage.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeStandardPriceVo;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceConfigMapper;
import org.dromara.waterfee.priceManage.mapper.WaterfeePriceTierMapper;
import org.dromara.waterfee.priceManage.mapper.WaterfeeStandardPriceMapper;
import org.dromara.waterfee.priceManage.service.WaterfeePriceCacheService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 价格方案缓存服务实现
 *
 * <AUTHOR> Assistant
 * @date 2023-11-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WaterfeePriceCacheServiceImpl implements WaterfeePriceCacheService {

    private final WaterfeeStandardPriceMapper standardPriceMapper;
    private final WaterfeePriceConfigMapper ladderPriceMapper;
    private final WaterfeePriceTierMapper priceTierMapper;

    // Redis缓存键前缀
    private static final String CACHE_KEY_PREFIX = "waterfee:price:";
    private static final String STANDARD_PRICE_KEY = CACHE_KEY_PREFIX + "standard:";
    private static final String LADDER_PRICE_KEY = CACHE_KEY_PREFIX + "ladder:";
    private static final String PRICE_TYPE_KEY = CACHE_KEY_PREFIX + "type:";
    private static final String ALL_STANDARD_PRICES_KEY = CACHE_KEY_PREFIX + "all:standard";
    private static final String ALL_LADDER_PRICES_KEY = CACHE_KEY_PREFIX + "all:ladder";
    private static final String ALL_PRICE_TYPES_KEY = CACHE_KEY_PREFIX + "all:types";

    // 缓存过期时间（1天）
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofHours(24);

    @Override
    public void initPriceCache() {
        log.info("开始初始化价格方案缓存");
        try {
            // 清除旧缓存
            clearAllPriceCache();

            // 加载所有标准价格方案
            List<WaterfeeStandardPriceVo> standardPrices = standardPriceMapper.selectVoList();
            if (standardPrices != null && !standardPrices.isEmpty()) {
                // 缓存所有标准价格方案
                RedisUtils.setCacheObject(ALL_STANDARD_PRICES_KEY, standardPrices, CACHE_EXPIRE_TIME);

                // 缓存每个标准价格方案
                for (WaterfeeStandardPriceVo price : standardPrices) {
                    if (price.getId() != null) {
                        String key = STANDARD_PRICE_KEY + price.getId();
                        RedisUtils.setCacheObject(key, price, CACHE_EXPIRE_TIME);

                        // 缓存价格方案类型
                        String typeKey = PRICE_TYPE_KEY + price.getId();
                        RedisUtils.setCacheObject(typeKey, "STANDARD", CACHE_EXPIRE_TIME);
                    }
                }
            }

            // 加载所有阶梯价格方案
            List<WaterfeePriceConfigVo> ladderPrices = ladderPriceMapper.selectVoList();
            if (ladderPrices != null && !ladderPrices.isEmpty()) {
                // 为每个阶梯价格方案加载阶梯价格详情
                for (WaterfeePriceConfigVo price : ladderPrices) {
                    if (price.getId() != null) {
                        // 加载阶梯价格详情
                        price.setPriceTiers(priceTierMapper.selectTiersByPriceConfigId(price.getId()));
                    }
                }

                // 缓存所有阶梯价格方案
                RedisUtils.setCacheObject(ALL_LADDER_PRICES_KEY, ladderPrices, CACHE_EXPIRE_TIME);

                // 缓存每个阶梯价格方案
                for (WaterfeePriceConfigVo price : ladderPrices) {
                    if (price.getId() != null) {
                        String key = LADDER_PRICE_KEY + price.getId();
                        RedisUtils.setCacheObject(key, price, CACHE_EXPIRE_TIME);

                        // 缓存价格方案类型
                        String typeKey = PRICE_TYPE_KEY + price.getId();
                        RedisUtils.setCacheObject(typeKey, "LADDER", CACHE_EXPIRE_TIME);
                    }
                }
            }

            // 缓存所有价格方案类型映射
            Map<Long, String> priceTypes = new HashMap<>();
            if (standardPrices != null) {
                for (WaterfeeStandardPriceVo price : standardPrices) {
                    if (price.getId() != null) {
                        priceTypes.put(price.getId(), "STANDARD");
                    }
                }
            }
            if (ladderPrices != null) {
                for (WaterfeePriceConfigVo price : ladderPrices) {
                    if (price.getId() != null) {
                        priceTypes.put(price.getId(), "LADDER");
                    }
                }
            }
            RedisUtils.setCacheObject(ALL_PRICE_TYPES_KEY, priceTypes, CACHE_EXPIRE_TIME);

            log.info("价格方案缓存初始化完成，标准价格方案: {}, 阶梯价格方案: {}",
                standardPrices != null ? standardPrices.size() : 0,
                ladderPrices != null ? ladderPrices.size() : 0);
        } catch (Exception e) {
            log.error("初始化价格方案缓存失败", e);
        }
    }

    @Override
    public void refreshPriceCache() {
        log.info("开始刷新价格方案缓存");
        initPriceCache();
    }

    @Override
    public WaterfeeStandardPriceVo getStandardPrice(Long priceId) {
        if (priceId == null) {
            return null;
        }

        String key = STANDARD_PRICE_KEY + priceId;
        WaterfeeStandardPriceVo price = RedisUtils.getCacheObject(key);

        if (price == null) {
            // 缓存未命中，从数据库加载
            price = standardPriceMapper.selectVoById(priceId);
            if (price != null) {
                // 更新缓存
                RedisUtils.setCacheObject(key, price, CACHE_EXPIRE_TIME);

                // 更新价格方案类型缓存
                String typeKey = PRICE_TYPE_KEY + priceId;
                RedisUtils.setCacheObject(typeKey, "STANDARD", CACHE_EXPIRE_TIME);
            }
        }

        return price;
    }

    @Override
    public WaterfeePriceConfigVo getLadderPrice(Long priceId) {
        if (priceId == null) {
            return null;
        }

        String key = LADDER_PRICE_KEY + priceId;
        WaterfeePriceConfigVo price = RedisUtils.getCacheObject(key);

        if (price == null) {
            // 缓存未命中，从数据库加载
            price = ladderPriceMapper.selectVoById(priceId);
            if (price != null) {
                // 加载阶梯价格详情
                price.setPriceTiers(priceTierMapper.selectTiersByPriceConfigId(priceId));

                // 更新缓存
                RedisUtils.setCacheObject(key, price, CACHE_EXPIRE_TIME);

                // 更新价格方案类型缓存
                String typeKey = PRICE_TYPE_KEY + priceId;
                RedisUtils.setCacheObject(typeKey, "LADDER", CACHE_EXPIRE_TIME);
            }
        }

        return price;
    }

    @Override
    public String getPriceType(Long priceId) {
        if (priceId == null) {
            return null;
        }

        String key = PRICE_TYPE_KEY + priceId;
        String type = RedisUtils.getCacheObject(key);

        if (StringUtils.isEmpty(type)) {
            // 缓存未命中，尝试从标准价格方案中查找
            WaterfeeStandardPriceVo standardPrice = standardPriceMapper.selectVoById(priceId);
            if (standardPrice != null) {
                type = "STANDARD";
                RedisUtils.setCacheObject(key, type, CACHE_EXPIRE_TIME);
            } else {
                // 尝试从阶梯价格方案中查找
                WaterfeePriceConfigVo ladderPrice = ladderPriceMapper.selectVoById(priceId);
                if (ladderPrice != null) {
                    type = "LADDER";
                    RedisUtils.setCacheObject(key, type, CACHE_EXPIRE_TIME);
                }
            }
        }

        return type;
    }

    @Override
    public List<WaterfeeStandardPriceVo> getAllStandardPrices() {
        List<WaterfeeStandardPriceVo> prices = RedisUtils.getCacheObject(ALL_STANDARD_PRICES_KEY);

        if (prices == null || prices.isEmpty()) {
            // 缓存未命中，从数据库加载
            prices = standardPriceMapper.selectVoList();
            if (prices != null && !prices.isEmpty()) {
                // 更新缓存
                RedisUtils.setCacheObject(ALL_STANDARD_PRICES_KEY, prices, CACHE_EXPIRE_TIME);
            } else {
                prices = new ArrayList<>();
            }
        }

        return prices;
    }

    @Override
    public List<WaterfeePriceConfigVo> getAllLadderPrices() {
        List<WaterfeePriceConfigVo> prices = RedisUtils.getCacheObject(ALL_LADDER_PRICES_KEY);

        if (prices == null || prices.isEmpty()) {
            // 缓存未命中，从数据库加载
            prices = ladderPriceMapper.selectVoList();
            if (prices != null && !prices.isEmpty()) {
                // 为每个阶梯价格方案加载阶梯价格详情
                for (WaterfeePriceConfigVo price : prices) {
                    if (price.getId() != null) {
                        price.setPriceTiers(priceTierMapper.selectTiersByPriceConfigId(price.getId()));
                    }
                }

                // 更新缓存
                RedisUtils.setCacheObject(ALL_LADDER_PRICES_KEY, prices, CACHE_EXPIRE_TIME);
            } else {
                prices = new ArrayList<>();
            }
        }

        return prices;
    }

    @Override
    public Map<Long, String> getAllPriceTypes() {
        Map<Long, String> priceTypes = RedisUtils.getCacheObject(ALL_PRICE_TYPES_KEY);

        if (priceTypes == null || priceTypes.isEmpty()) {
            // 缓存未命中，重新构建
            priceTypes = new HashMap<>();

            // 加载所有标准价格方案
            List<WaterfeeStandardPriceVo> standardPrices = standardPriceMapper.selectVoList();
            if (standardPrices != null) {
                for (WaterfeeStandardPriceVo price : standardPrices) {
                    if (price.getId() != null) {
                        priceTypes.put(price.getId(), "STANDARD");
                    }
                }
            }

            // 加载所有阶梯价格方案
            List<WaterfeePriceConfigVo> ladderPrices = ladderPriceMapper.selectVoList();
            if (ladderPrices != null) {
                for (WaterfeePriceConfigVo price : ladderPrices) {
                    if (price.getId() != null) {
                        priceTypes.put(price.getId(), "LADDER");
                    }
                }
            }

            // 更新缓存
            if (!priceTypes.isEmpty()) {
                RedisUtils.setCacheObject(ALL_PRICE_TYPES_KEY, priceTypes, CACHE_EXPIRE_TIME);
            }
        }

        return priceTypes;
    }

    @Override
    public void updateStandardPriceCache(WaterfeeStandardPriceVo priceVo) {
        if (priceVo == null || priceVo.getId() == null) {
            return;
        }

        // 更新单个标准价格方案缓存
        String key = STANDARD_PRICE_KEY + priceVo.getId();
        RedisUtils.setCacheObject(key, priceVo, CACHE_EXPIRE_TIME);

        // 更新价格方案类型缓存
        String typeKey = PRICE_TYPE_KEY + priceVo.getId();
        RedisUtils.setCacheObject(typeKey, "STANDARD", CACHE_EXPIRE_TIME);

        // 更新所有标准价格方案缓存
        List<WaterfeeStandardPriceVo> allPrices = getAllStandardPrices();
        boolean found = false;
        for (int i = 0; i < allPrices.size(); i++) {
            if (allPrices.get(i).getId().equals(priceVo.getId())) {
                allPrices.set(i, priceVo);
                found = true;
                break;
            }
        }
        if (!found) {
            allPrices.add(priceVo);
        }
        RedisUtils.setCacheObject(ALL_STANDARD_PRICES_KEY, allPrices, CACHE_EXPIRE_TIME);

        // 更新所有价格方案类型映射
        Map<Long, String> priceTypes = getAllPriceTypes();
        priceTypes.put(priceVo.getId(), "STANDARD");
        RedisUtils.setCacheObject(ALL_PRICE_TYPES_KEY, priceTypes, CACHE_EXPIRE_TIME);
    }

    @Override
    public void updateLadderPriceCache(WaterfeePriceConfigVo priceVo) {
        if (priceVo == null || priceVo.getId() == null) {
            return;
        }

        // 更新单个阶梯价格方案缓存
        String key = LADDER_PRICE_KEY + priceVo.getId();
        RedisUtils.setCacheObject(key, priceVo, CACHE_EXPIRE_TIME);

        // 更新价格方案类型缓存
        String typeKey = PRICE_TYPE_KEY + priceVo.getId();
        RedisUtils.setCacheObject(typeKey, "LADDER", CACHE_EXPIRE_TIME);

        // 更新所有阶梯价格方案缓存
        List<WaterfeePriceConfigVo> allPrices = getAllLadderPrices();
        boolean found = false;
        for (int i = 0; i < allPrices.size(); i++) {
            if (allPrices.get(i).getId().equals(priceVo.getId())) {
                allPrices.set(i, priceVo);
                found = true;
                break;
            }
        }
        if (!found) {
            allPrices.add(priceVo);
        }
        RedisUtils.setCacheObject(ALL_LADDER_PRICES_KEY, allPrices, CACHE_EXPIRE_TIME);

        // 更新所有价格方案类型映射
        Map<Long, String> priceTypes = getAllPriceTypes();
        priceTypes.put(priceVo.getId(), "LADDER");
        RedisUtils.setCacheObject(ALL_PRICE_TYPES_KEY, priceTypes, CACHE_EXPIRE_TIME);
    }

    @Override
    public void removeStandardPriceCache(Long priceId) {
        if (priceId == null) {
            return;
        }

        // 删除单个标准价格方案缓存
        String key = STANDARD_PRICE_KEY + priceId;
        RedisUtils.deleteObject(key);

        // 删除价格方案类型缓存
        String typeKey = PRICE_TYPE_KEY + priceId;
        RedisUtils.deleteObject(typeKey);

        // 更新所有标准价格方案缓存
        List<WaterfeeStandardPriceVo> allPrices = getAllStandardPrices();
        allPrices = allPrices.stream()
            .filter(price -> !priceId.equals(price.getId()))
            .collect(Collectors.toList());
        RedisUtils.setCacheObject(ALL_STANDARD_PRICES_KEY, allPrices, CACHE_EXPIRE_TIME);

        // 更新所有价格方案类型映射
        Map<Long, String> priceTypes = getAllPriceTypes();
        priceTypes.remove(priceId);
        RedisUtils.setCacheObject(ALL_PRICE_TYPES_KEY, priceTypes, CACHE_EXPIRE_TIME);
    }

    @Override
    public void removeLadderPriceCache(Long priceId) {
        if (priceId == null) {
            return;
        }

        // 删除单个阶梯价格方案缓存
        String key = LADDER_PRICE_KEY + priceId;
        RedisUtils.deleteObject(key);

        // 删除价格方案类型缓存
        String typeKey = PRICE_TYPE_KEY + priceId;
        RedisUtils.deleteObject(typeKey);

        // 更新所有阶梯价格方案缓存
        List<WaterfeePriceConfigVo> allPrices = getAllLadderPrices();
        allPrices = allPrices.stream()
            .filter(price -> !priceId.equals(price.getId()))
            .collect(Collectors.toList());
        RedisUtils.setCacheObject(ALL_LADDER_PRICES_KEY, allPrices, CACHE_EXPIRE_TIME);

        // 更新所有价格方案类型映射
        Map<Long, String> priceTypes = getAllPriceTypes();
        priceTypes.remove(priceId);
        RedisUtils.setCacheObject(ALL_PRICE_TYPES_KEY, priceTypes, CACHE_EXPIRE_TIME);
    }

    /**
     * 清除所有价格方案缓存
     */
    private void clearAllPriceCache() {
        // 获取所有标准价格方案
        List<WaterfeeStandardPriceVo> standardPrices = standardPriceMapper.selectVoList();
        if (standardPrices != null) {
            for (WaterfeeStandardPriceVo price : standardPrices) {
                if (price.getId() != null) {
                    String key = STANDARD_PRICE_KEY + price.getId();
                    RedisUtils.deleteObject(key);

                    String typeKey = PRICE_TYPE_KEY + price.getId();
                    RedisUtils.deleteObject(typeKey);
                }
            }
        }

        // 获取所有阶梯价格方案
        List<WaterfeePriceConfigVo> ladderPrices = ladderPriceMapper.selectVoList();
        if (ladderPrices != null) {
            for (WaterfeePriceConfigVo price : ladderPrices) {
                if (price.getId() != null) {
                    String key = LADDER_PRICE_KEY + price.getId();
                    RedisUtils.deleteObject(key);

                    String typeKey = PRICE_TYPE_KEY + price.getId();
                    RedisUtils.deleteObject(typeKey);
                }
            }
        }

        // 删除所有价格方案列表缓存
        RedisUtils.deleteObject(ALL_STANDARD_PRICES_KEY);
        RedisUtils.deleteObject(ALL_LADDER_PRICES_KEY);
        RedisUtils.deleteObject(ALL_PRICE_TYPES_KEY);
    }
}
