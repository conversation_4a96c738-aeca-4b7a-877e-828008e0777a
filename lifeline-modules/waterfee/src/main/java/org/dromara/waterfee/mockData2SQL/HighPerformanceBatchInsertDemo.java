package org.dromara.waterfee.mockData2SQL;

import org.dromara.common.core.domain.R;
import org.dromara.waterfee.counter.domain.bo.WaterfeeDepositBo;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.mockData2SQL.utils.RandomDataGenerator;
import org.dromara.waterfee.meter.domain.bo.WaterfeeMeterBo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.user.domain.bo.WaterfeeUserBo;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 高性能批量插入演示程序
 * 专门用于处理大量数据插入
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/demo")
public class HighPerformanceBatchInsertDemo {

    @Autowired
    private IWaterfeeUserService waterfeeUserService;

    @Autowired
    private IWaterfeeMeterService waterfeeMeterService;

    @Autowired
    private IWaterfeeCounterPaymentService counterPaymentService;

    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failCount = new AtomicInteger(0);

    /**
     * 插入指定数量的记录（高性能版本）
     *
     * @param count 插入数量
     * @return 插入结果
     */
    @GetMapping("/insertBatchRecordsHP")
    @Transactional(rollbackFor = Exception.class)
    public R<String> insertBatchRecordsHP(@RequestParam(defaultValue = "1000") Integer count) {
        return performBatchInsert(count);
    }

    /**
     * 执行批量插入
     *
     * @param count 插入数量
     * @return 插入结果
     */
    private R<String> performBatchInsert(Integer count) {
        if (count <= 0 || count > 100000) {
            return R.fail("插入数量必须在1-100000之间");
        }

        // 重置计数器
        processedCount.set(0);
        successCount.set(0);
        failCount.set(0);

        long startTime = System.currentTimeMillis();
        StringBuilder errorLog = new StringBuilder();

        System.out.println("=== 开始高性能批量插入 " + count + " 条记录 ===");

        // 分批处理，每批200条
        int batchSize = 200;
        int totalBatches = (count + batchSize - 1) / batchSize;

        for (int batch = 0; batch < totalBatches; batch++) {
            int startIndex = batch * batchSize;
            int endIndex = Math.min(startIndex + batchSize, count);

            processBatch(startIndex, endIndex, errorLog);

            // 每10批或最后一批输出进度
            if (batch % 10 == 0 || batch == totalBatches - 1) {
                reportProgress(batch + 1, totalBatches, startTime);
            }
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        return buildResult(count, totalTime, errorLog);
    }

    /**
     * 处理单批数据
     */
    private void processBatch(int startIndex, int endIndex, StringBuilder errorLog) {
        for (int i = startIndex; i < endIndex; i++) {
            try {
                if (createSingleRecord(i + 1)) {
                    successCount.incrementAndGet();
                } else {
                    failCount.incrementAndGet();
                    if (errorLog.length() < 5000) {
                        errorLog.append("记录").append(i + 1).append(": 创建失败\n");
                    }
                }
            } catch (Exception e) {
                failCount.incrementAndGet();
                if (errorLog.length() < 5000) {
                    errorLog.append("记录").append(i + 1).append(": 异常 - ").append(e.getMessage()).append("\n");
                }
            }
            processedCount.incrementAndGet();
        }
    }

    /**
     * 创建单条记录（用户+水表+充值）
     */
    private boolean createSingleRecord(int recordNumber) {
        try {
            // 1. 创建用户
            WaterfeeUserBo userBo = createUserBo();
            Boolean userResult = waterfeeUserService.insertByBo(userBo);
            if (!userResult) {
                return false;
            }

            // 2. 创建水表
            WaterfeeMeterBo meterBo = createMeterBo(userBo);
            Boolean meterResult = waterfeeMeterService.insertByBo(meterBo);
            if (!meterResult) {
                return false;
            }

            // 3. 充值（充值失败不影响整体成功）
            try {
                WaterfeeDepositBo depositBo = createDepositBo(userBo.getUserId());
                counterPaymentService.addDeposit(depositBo);
            } catch (Exception e) {
                // 充值失败不影响用户和水表创建的成功
                System.out.println("记录" + recordNumber + "充值失败: " + e.getMessage());
            }

            return true;
        } catch (Exception e) {
            throw new RuntimeException("创建记录失败", e);
        }
    }

    /**
     * 创建用户业务对象
     */
    private WaterfeeUserBo createUserBo() {
        WaterfeeUserBo userBo = new WaterfeeUserBo();
        userBo.setUserName(RandomDataGenerator.generateRandomUserName());
        userBo.setPhoneNumber(RandomDataGenerator.generateRandomPhoneNumber());
        userBo.setCertificateType("identity_card");
        userBo.setCertificateNumber(RandomDataGenerator.generateRandomIdCard());
        userBo.setUserStatus("normal");
        userBo.setAddress(RandomDataGenerator.generateRandomAddress());
        userBo.setSupplyDate(new Date());
        userBo.setAreaId(1905058024441294849L);
        userBo.setCommunityId(1L);
        userBo.setUseWaterNature("resident");
        userBo.setUseWaterNumber(3L);
        userBo.setInvoiceType("VAT_invoice");
        userBo.setPriceUseWaterNature("resident");
        userBo.setBillingMethod("1933328427904176130");
        return userBo;
    }

    /**
     * 创建水表业务对象
     */
    private WaterfeeMeterBo createMeterBo(WaterfeeUserBo userBo) {
        WaterfeeMeterBo meterBo = new WaterfeeMeterBo();
        meterBo.setMeterNo(RandomDataGenerator.generateRandomMeterNo());
        meterBo.setMeterType(2);
        meterBo.setBusinessAreaId(1905058024441294849L);
        meterBo.setMeterBookId(2L);
        meterBo.setUserId(userBo.getUserId());
        meterBo.setUserNo(userBo.getUserNo());
        meterBo.setInstallDate(new Date());
        meterBo.setInstallAddress(userBo.getAddress());
        return meterBo;
    }

    /**
     * 创建充值业务对象
     */
    private WaterfeeDepositBo createDepositBo(Long userId) {
        WaterfeeDepositBo depositBo = new WaterfeeDepositBo();
        depositBo.setUserId(userId);
        depositBo.setAmount(new BigDecimal("10000.00"));
        depositBo.setPaymentMethod("CASH");
        depositBo.setRemark("批量创建赠送");
        depositBo.setTollCollector("系统自动");
        return depositBo;
    }

    /**
     * 报告进度
     */
    private void reportProgress(int currentBatch, int totalBatches, long startTime) {
        long currentTime = System.currentTimeMillis();
        long elapsed = currentTime - startTime;
        double progress = (double) currentBatch / totalBatches * 100;
        int processed = processedCount.get();
        int success = successCount.get();
        int fail = failCount.get();

        double speed = processed > 0 ? (double) processed / (elapsed / 1000.0) : 0;

        System.out.printf(
            "批次: %d/%d (%.1f%%) | 已处理: %d | 成功: %d | 失败: %d | 速度: %.1f条/秒 | 耗时: %d秒%n",
            currentBatch, totalBatches, progress, processed, success, fail, speed, elapsed / 1000
        );
    }

    /**
     * 构建返回结果
     */
    private R<String> buildResult(int totalCount, long totalTime, StringBuilder errorLog) {
        int success = successCount.get();
        int fail = failCount.get();
        double successRate = (double) success / totalCount * 100;
        double avgSpeed = totalCount / (totalTime / 1000.0);

        StringBuilder result = new StringBuilder();
        result.append("=== 高性能批量插入完成 ===\n\n");
        result.append("总记录数: ").append(totalCount).append("\n");
        result.append("成功创建: ").append(success).append(" 条\n");
        result.append("失败: ").append(fail).append(" 条\n");
        result.append("成功率: ").append(String.format("%.2f%%", successRate)).append("\n");
        result.append("总耗时: ").append(totalTime / 1000).append(" 秒\n");
        result.append("平均速度: ").append(String.format("%.1f", avgSpeed)).append(" 条/秒\n");

        if (!errorLog.isEmpty()) {
            result.append("\n错误详情:\n").append(errorLog.toString());
            if (errorLog.length() >= 5000) {
                result.append("...(错误信息过多，已截断)\n");
            }
        }

        System.out.println("=== 批量插入统计 ===");
        System.out.println("成功: " + success + " 条, 失败: " + fail + " 条");
        System.out.println("成功率: " + String.format("%.2f%%", successRate));
        System.out.println("总耗时: " + totalTime / 1000 + " 秒");
        System.out.println("平均速度: " + String.format("%.1f", avgSpeed) + " 条/秒");

        return R.ok(result.toString());
    }

    /**
     * 获取当前插入进度
     */
    @GetMapping("/getInsertProgress")
    public R<String> getInsertProgress() {
        String progress = "当前插入进度:\n" +
            "已处理: " + processedCount.get() + " 条\n" +
            "成功: " + successCount.get() + " 条\n" +
            "失败: " + failCount.get() + " 条\n";

        return R.ok(progress);
    }
}
