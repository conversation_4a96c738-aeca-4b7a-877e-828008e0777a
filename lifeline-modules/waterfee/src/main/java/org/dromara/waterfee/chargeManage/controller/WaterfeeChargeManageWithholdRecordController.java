package org.dromara.waterfee.chargeManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdRecord;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageWithholdRecordVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageWithholdRecordBo;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageWithholdRecordService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 代扣记录
 * 前端访问路由地址为:/waterfee/chargeManageWithholdRecord
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chargeManageWithholdRecord")
public class WaterfeeChargeManageWithholdRecordController extends BaseController {

    private final IWaterfeeChargeManageWithholdRecordService waterfeeChargeManageWithholdRecordService;

    /**
     * 查询代扣记录列表
     */
    @SaCheckPermission("waterfee:chargeManageWithholdRecord:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeChargeManageWithholdRecordVo> list(WaterfeeChargeManageWithholdRecordBo bo, PageQuery pageQuery) {
        return waterfeeChargeManageWithholdRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出代扣记录列表
     */
    @SaCheckPermission("waterfee:chargeManageWithholdRecord:export")
    @Log(title = "代扣记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeChargeManageWithholdRecordBo bo, HttpServletResponse response) {
        List<WaterfeeChargeManageWithholdRecordVo> list = waterfeeChargeManageWithholdRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "代扣记录", WaterfeeChargeManageWithholdRecordVo.class, response);
    }

    /**
     * 获取代扣记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:chargeManageWithholdRecord:query")
    @GetMapping("/{id}")
    public R<WaterfeeChargeManageWithholdRecord> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeChargeManageWithholdRecordService.queryById(id));
    }

    /**
     * 新增代扣记录
     */
    @SaCheckPermission("waterfee:chargeManageWithholdRecord:add")
    @Log(title = "代扣记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeChargeManageWithholdRecordBo bo) {
        return toAjax(waterfeeChargeManageWithholdRecordService.insertByBo(bo));
    }

    /**
     * 修改代扣记录
     */
    @SaCheckPermission("waterfee:chargeManageWithholdRecord:edit")
    @Log(title = "代扣记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeChargeManageWithholdRecordBo bo) {
        return toAjax(waterfeeChargeManageWithholdRecordService.updateByBo(bo));
    }

    /**
     * 删除代扣记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:chargeManageWithholdRecord:remove")
    @Log(title = "代扣记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeChargeManageWithholdRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
