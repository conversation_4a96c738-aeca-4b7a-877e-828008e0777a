package org.dromara.waterfee.priceManage.domain.vo;

import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 阶梯价格配置视图对象 waterfee_price_config
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeePriceConfig.class)
public class WaterfeePriceConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 价格名称 (例如：居民阶梯价)
     */
    @ExcelProperty(value = "价格名称 (例如：居民阶梯价)")
    private String name;

    /**
     * 用水性质 (例如：'1' 代表居民)
     */
    @ExcelProperty(value = "用水性质 (例如：'1' 代表居民)")
    private String waterUseType;

    /**
     * 计算方式 (例如：按年、按月、按人口)
     */
    @ExcelProperty(value = "计算方式 (例如：按年、按月、按人口)")
    private String calculationMethod;

    /**
     * 是否按人口计算 (0:否, 1:是)
     */
    @ExcelProperty(value = "是否按人口计算 (0:否, 1:是)")
    private Long isPopulation;

    /**
     * 人口数量 (当is_population为1时有效)
     */
    @ExcelProperty(value = "人口数量 (当is_population为1时有效)")
    private Long populationCount;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;

    /**
     * 阶梯价格列表
     */
    @ExcelProperty(value = "阶梯价格列表")
    private List<WaterfeePriceTier> priceTiers;


}
