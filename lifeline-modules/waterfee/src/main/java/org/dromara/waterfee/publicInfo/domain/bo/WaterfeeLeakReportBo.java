package org.dromara.waterfee.publicInfo.domain.bo;

import org.dromara.waterfee.publicInfo.domain.WaterfeeLeakReport;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 偷漏水举报业务对象 waterfee_leak_report
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeLeakReport.class, reverseConvertGenerate = false)
public class WaterfeeLeakReportBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long reportId;

    /**
     * 上报人姓名
     */
    private String reporterName;

    /**
     * 上报人电话
     */
    private String reporterPhone;

    /**
     * 举报时间
     */
    private Date reportTime;

    /**
     * 问题描述
     */
    private String description;

    /**
     * 附件
     */
    private String file;

    /**
     * 经度
     */
    private String lon;

    /**
     * 维度
     */
    private String lat;

    /**
     * 备注
     */
    private String remark;


}
