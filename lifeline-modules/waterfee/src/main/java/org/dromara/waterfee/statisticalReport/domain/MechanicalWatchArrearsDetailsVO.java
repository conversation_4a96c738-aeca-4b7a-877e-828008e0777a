package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MechanicalWatchArrearsDetailsVO {
    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编码（用水编码）")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "电话")
    private String phoneNumber;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "身份证号（信用代码）")
    private String certificateNumber;

    /**
     * 客户性质（字典waterfee_user_customer_nature）
     */
    @ExcelProperty(value = "用户类型")
    private String customerNature;

    /**
     * 用水类型
     */
    @ExcelProperty(value = "用水类型")
    private String useWaterNature;

    /**
     * 欠费起始时间
     */
    @ExcelProperty(value = "欠费起始时间")
    private String billingPeriodStart;

    /**
     * 欠费截止时间
     */
    @ExcelProperty(value = "欠费截止时间")
    private String billingPeriodEnd;

    /**
     * 上期表数
     */
    @ExcelProperty(value = "上期表数")
    private Double previousReadingValue;

    /**
     *  本期表数
     */
    @ExcelProperty(value = "本期表数")
    private Double currentReadingValue;

    /**
     * 水量
     */
    @ExcelProperty(value = "水量")
    private BigDecimal consumptionVolume;

    /**
     * 水费金额
     */
    @ExcelProperty(value = "水费金额")
    private Double waterBillOnly;

    /**
     * 水资源费金额
     */
    @ExcelProperty(value = "水资源费金额")
    private BigDecimal waterResourceTax;

    /**
     * 污水费金额
     */
    @ExcelProperty(value = "污水费金额")
    private BigDecimal sewageTreatmentFee;

    /**
     * 合计金额
     */
    @ExcelProperty(value = "合计金额")
    private BigDecimal totalAmount;
}
