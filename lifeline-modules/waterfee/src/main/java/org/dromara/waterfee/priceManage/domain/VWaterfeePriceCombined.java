package org.dromara.waterfee.priceManage.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-26 09:24
 **/

@Data
public class VWaterfeePriceCombined {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 价格名称 (例如：居民阶梯价)
     */
    @TableField(value = "name")
    String name;

    /**
     * 用水性质 (例如：'1' 代表居民)
     */
    @TableField(value = "water_use_type")
    String waterUseType;

    /**
     * 计算方式 (例如：按年、按月、按人口)
     */
    @TableField(value = "priceType")
    String priceType;
}
