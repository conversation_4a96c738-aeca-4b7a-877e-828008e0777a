package org.dromara.waterfee.publicInfo.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.core.domain.R;
import org.dromara.waterfee.publicInfo.domain.bo.WaterfeePublicInfoBo;
import org.dromara.waterfee.publicInfo.domain.vo.WaterfeePublicInfoVo;
import org.dromara.waterfee.publicInfo.service.IWaterfeePublicInfoService;

/**
 * 公共信息控制器
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/publicInfo")
public class WaterfeePublicInfoController extends BaseController {

  private final IWaterfeePublicInfoService publicInfoService;

  /**
   * 查询公共信息列表
   */
  @SaCheckPermission("waterfee:publicInfo:list")
  @GetMapping("/list")
  public TableDataInfo<WaterfeePublicInfoVo> list(WaterfeePublicInfoBo bo, PageQuery pageQuery) {
    return publicInfoService.queryPageList(bo, pageQuery);
  }

  /**
   * 获取公共信息详细信息
   *
   * @param infoId 主键
   */
  @SaCheckPermission("waterfee:publicInfo:query")
  @GetMapping("/{infoId}")
  public R<WaterfeePublicInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long infoId) {
    return R.ok(publicInfoService.queryById(infoId));
  }

  /**
   * 新增公共信息
   */
  @SaCheckPermission("waterfee:publicInfo:add")
  @Log(title = "公共信息", businessType = BusinessType.INSERT)
  @RepeatSubmit()
  @PostMapping()
  public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeePublicInfoBo bo) {
    return toAjax(publicInfoService.insertByBo(bo));
  }

  /**
   * 新增公共信息并同时生成草稿信息并发布草稿到微信
   */
  @SaCheckPermission("waterfee:publicInfo:addWithWechat")
  @Log(title = "新增公共信息并发布微信草稿", businessType = BusinessType.INSERT)
  @RepeatSubmit()
  @PostMapping("/withWechat")
  public R<Void> addWithWechat(@Validated(AddGroup.class) @RequestBody WaterfeePublicInfoBo bo) {
    return toAjax(publicInfoService.insertAndCreateWechatDraft(bo));
  }

  /**
   * 修改公共信息
   */
  @SaCheckPermission("waterfee:publicInfo:edit")
  @Log(title = "公共信息", businessType = BusinessType.UPDATE)
  @RepeatSubmit()
  @PutMapping()
  public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeePublicInfoBo bo) {
    return toAjax(publicInfoService.updateByBo(bo));
  }

  /**
   * 修改公共信息并同时修改草稿信息
   */
  @SaCheckPermission("waterfee:publicInfo:editWithWechat")
  @Log(title = "修改公共信息并同步微信草稿", businessType = BusinessType.UPDATE)
  @RepeatSubmit()
  @PutMapping("/withWechat")
  public R<Void> editWithWechat(@Validated(EditGroup.class) @RequestBody WaterfeePublicInfoBo bo) {
    return toAjax(publicInfoService.updateAndSyncWechatDraft(bo));
  }

  /**
   * 发布公共信息
   */
  @SaCheckPermission("waterfee:publicInfo:edit")
  @Log(title = "发布公共信息", businessType = BusinessType.UPDATE)
  @PutMapping("/publish/{infoId}")
  public R<Void> publish(@NotNull(message = "主键不能为空") @PathVariable Long infoId) {
    return toAjax(publicInfoService.publishInfo(infoId));
  }

  /**
   * 发布公共信息并同时发布微信草稿成公众号推文
   */
  @SaCheckPermission("waterfee:publicInfo:publishWithWechat")
  @Log(title = "发布公共信息并发布微信文章", businessType = BusinessType.UPDATE)
  @PutMapping("/publishWithWechat/{infoId}")
  public R<Void> publishWithWechat(@NotNull(message = "主键不能为空") @PathVariable Long infoId) {
    return toAjax(publicInfoService.publishInfoAndWechatArticle(infoId));
  }

  /**
   * 删除公共信息
   *
   * @param infoIds 主键串
   */
  @SaCheckPermission("waterfee:publicInfo:remove")
  @Log(title = "公共信息", businessType = BusinessType.DELETE)
  @DeleteMapping("/{infoIds}")
  public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] infoIds) {
    return toAjax(publicInfoService.deleteWithValidByIds(Arrays.asList(infoIds)));
  }

  /**
   * 删除公共信息及关联的微信草稿和推文
   *
   * @param infoIds 主键串
   */
  @SaCheckPermission("waterfee:publicInfo:removeWithWechat")
  @Log(title = "删除公共信息及微信草稿", businessType = BusinessType.DELETE)
  @DeleteMapping("/withWechat/{infoIds}")
  public R<Void> removeWithWechat(@NotEmpty(message = "主键不能为空") @PathVariable Long[] infoIds) {
    return toAjax(publicInfoService.deleteWithWechatDraftByIds(Arrays.asList(infoIds)));
  }

  /**
   * 查询停水通知列表
   */
  @GetMapping("/waterOutage")
  public R<List<WaterfeePublicInfoVo>> getWaterOutageList() {
    return R.ok(publicInfoService.queryListByType("1"));
  }

  /**
   * 查询供水公告列表
   */
  @GetMapping("/waterSupplyNotice")
  public R<List<WaterfeePublicInfoVo>> getWaterSupplyNoticeList() {
    return R.ok(publicInfoService.queryListByType("2"));
  }

  /**
   * 查询业务常识列表
   */
  @GetMapping("/businessKnowledge")
  public R<List<WaterfeePublicInfoVo>> getBusinessKnowledgeList() {
    return R.ok(publicInfoService.queryListByType("3"));
  }

  /**
   * 查询水费标准列表
   */
  @GetMapping("/waterFeeStandard")
  public R<List<WaterfeePublicInfoVo>> getWaterFeeStandardList() {
    return R.ok(publicInfoService.queryListByType("4"));
  }

  /**
   * 查询营商环境列表
   */
  @GetMapping("/businessEnvironment")
  public R<List<WaterfeePublicInfoVo>> getBusinessEnvironmentList() {
    return R.ok(publicInfoService.queryListByType("5"));
  }

  /**
   * 查询公司简介列表
   */
  @GetMapping("/companyProfile")
  public R<List<WaterfeePublicInfoVo>> getCompanyProfileList() {
    return R.ok(publicInfoService.queryListByType("6"));
  }

  /**
   * 增加信息点击量
   */
  @PutMapping("/view/{infoId}")
  public R<Void> increaseViewCount(@NotNull(message = "信息ID不能为空") @PathVariable Long infoId) {
    return toAjax(publicInfoService.increaseViewCount(infoId));
  }
}
