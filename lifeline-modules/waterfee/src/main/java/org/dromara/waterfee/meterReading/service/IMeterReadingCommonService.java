package org.dromara.waterfee.meterReading.service;

import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeReadingTaskBo;
import org.dromara.waterfee.meterReading.domain.vo.MeterReadingRecordVo;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeReadingTaskVo;

import java.util.List;
import java.util.Map;

/**
 * 抄表共享服务接口 - 用于解决循环依赖问题
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface IMeterReadingCommonService {

    /**
     * 创建抄表记录
     *
     * @param record 抄表记录
     * @return 结果
     */
    Boolean createMeterReadingRecord(WaterfeeMeterReadingRecord record);

    /**
     * 更新抄表任务状态
     *
     * @param taskId    任务ID
     * @param isAudited 是否已审核
     * @return 结果
     */
    Boolean updateTaskAuditStatus(Long taskId, String isAudited);

    /**
     * 计算表册关联的水表数量和用户数量
     *
     * @param meterBookId 表册ID
     * @return 水表数量和用户数量
     */
//    Map<String, Integer> calculateMeterAndUserCount(Long meterBookId);

    /**
     * 根据表册ID查询机械表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 机械表抄表记录列表
     */
    List<MeterReadingRecordVo> queryMechanicalMetersByBookId(Long meterBookId);

    /**
     * 根据表册ID查询智能表抄表记录
     *
     * @param meterBookId 表册ID
     * @return 智能表抄表记录列表
     */
    List<MeterReadingRecordVo> queryIntelligentMetersByBookId(Long meterBookId);

    /**
     * 根据表册ID和任务ID查询抄表记录
     *
     * @param meterBookId 表册ID
     * @param taskId      任务ID
     * @return 抄表记录列表
     */
    List<WaterfeeMeterReadingRecord> selectByBookIdAndTaskId(Long meterBookId, Long taskId);

    /**
     * 查询抄表任务
     *
     * @param taskId 任务ID
     * @return 抄表任务
     */
    WaterfeeReadingTaskVo queryTaskById(Long taskId);

    /**
     * 更新抄表任务
     *
     * @param bo 抄表任务
     * @return 结果
     */
    Boolean updateTask(WaterfeeReadingTaskBo bo);

    /**
     * 根据水表编号查询最新一次的抄表记录（实体）
     *
     * @param meterNo 水表编号
     * @return 抄表记录
     */
    WaterfeeMeterReadingRecord queryLatestEntityByMeterNo(String meterNo);

    /**
     * 根据任务ID查询抄表任务
     *
     * @param taskId 任务ID
     * @return 抄表任务
     */
    WaterfeeReadingTask getTaskById(Long taskId);

    /**
     * 统计指定任务的抄表记录数量
     *
     * @param taskId 任务ID
     * @return 记录数量
     */
    int countRecordsByTaskId(Long taskId);
}
