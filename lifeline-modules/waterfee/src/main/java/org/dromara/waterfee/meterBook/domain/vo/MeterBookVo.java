package org.dromara.waterfee.meterBook.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.waterfee.meterBook.domain.MeterBook;

import java.io.Serial;
import java.io.Serializable;

/**
 * u6284u8868u624bu518cu89c6u56feu5bf9u8c61
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MeterBook.class)
public class MeterBookVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * u4e3bu952eID
     */
    @ExcelProperty(value = "ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * u7ba1u8f96u533au57dfID
     */
    @ExcelProperty(value = "u7ba1u8f96u533au57dfID")
    private Long areaId;

    /**
     * u7ba1u8f96u533au57dfu540du79f0
     */
    @ExcelProperty(value = "u7ba1u8f96u533au57df")
    private String areaName;

    /**
     * u624bu518cu7f16u53f7
     */
    @ExcelProperty(value = "u624bu518cu7f16u53f7")
    private String bookNo;

    /**
     * u624bu518cu540du79f0
     */
    @ExcelProperty(value = "u624bu518cu540du79f0")
    private String bookName;

    /**
     * u6284u8868u65b9u5f0fuff08u4ebau5de5u6284u8868u3001u8fdcu4f20u6284u8868uff09
     */
    @ExcelProperty(value = "u6284u8868u65b9u5f0f", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wf_read_type")
    private String readType;

    /**
     * u6284u8868u5468u671fuff081u4e00u6708u4e00u6284u30012u4e24u6708u4e00u62843u4e09u6708u4e00u6284uff09
     */
    @ExcelProperty(value = "u6284u8868u5468u671f", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wf_read_cycle")
    private String readCycle;

    /**
     * u6284u8868u4f8bu65e5
     */
    @ExcelProperty(value = "u6284u8868u4f8bu65e5")
    private Integer readDay;

    /**
     * u6284u8868u57fau51c6u65e5
     */
    @ExcelProperty(value = "u6284u8868u57fau51c6u65e5")
    private Integer readBaseDay;

    /**
     * u6284u8868u5458
     */
    @ExcelProperty(value = "u6284u8868u5458ID")
    private Long reader;

    /**
     * u6284u8868u5458u540du79f0
     */
    @ExcelProperty(value = "u6284u8868u5458")
    private String readerName;

    /**
     * u6284u8868u7ec4u957f
     */
    @ExcelProperty(value = "u6284u8868u7ec4u957fID")
    private Long readerLeader;

    /**
     * u6284u8868u7ec4u957fu540du79f0
     */
    @ExcelProperty(value = "u6284u8868u7ec4u957f")
    private String readerLeaderName;

    /**
     * u5907u6ce8
     */
    @ExcelProperty(value = "u5907u6ce8")
    private String remark;

}
