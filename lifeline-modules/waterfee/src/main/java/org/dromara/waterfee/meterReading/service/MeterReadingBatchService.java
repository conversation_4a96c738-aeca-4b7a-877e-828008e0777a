package org.dromara.waterfee.meterReading.service;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.mapper.OptimizedBatchMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 抄表记录批量处理服务
 * 专门用于优化大批量抄表记录的处理性能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeterReadingBatchService {

    private final IMeterReadingRecordService meterReadingRecordService;
    private final IWaterfeeMeterService waterfeeMeterService;
    private final HighPerformanceBillProcessingService highPerformanceBillProcessingService;
    private final OptimizedBatchMapper optimizedBatchMapper;

    // 批处理配置
    private static final int DEFAULT_BATCH_SIZE = 500;
    private static final int MAX_RETRY_TIMES = 3;

    /**
     * 批量处理抄表记录
     *
     * @param meterNos 水表编号列表
     * @param taskId   任务ID
     * @param now      当前时间
     * @return 处理结果统计
     */
    public BatchProcessResult batchProcessMeterReadings(List<String> meterNos, Long taskId, Date now) {
        long startTime = System.currentTimeMillis();

        BatchProcessResult result = new BatchProcessResult();
        result.setTotalCount(meterNos.size());

        try {
            // 1. 批量获取水表信息
            Map<String, WaterfeeMeterVo> meterMap = batchGetMeterInfo(meterNos);
            log.info("批量获取水表信息完成，数量: {}", meterMap.size());

            // 2. 批量获取最新抄表记录
            Map<String, WaterfeeMeterReadingRecord> latestRecordMap = batchGetLatestRecords(meterNos);
            log.info("批量获取最新抄表记录完成，数量: {}", latestRecordMap.size());

            // 3. 分批处理
            List<List<String>> batches = partitionList(meterNos);
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);

            // 4. 并行处理各批次
            List<CompletableFuture<BatchResult>> futures = batches.stream()
                .map(batch -> processBatchAsync(batch, meterMap, latestRecordMap, taskId, now))
                .toList();

            // 5. 等待所有批次完成并汇总结果
            List<BatchResult> batchResults = futures.stream()
                .map(CompletableFuture::join)
                .toList();

            // 6. 汇总统计结果
            for (BatchResult batchResult : batchResults) {
                successCount.addAndGet(batchResult.getSuccessCount());
                failCount.addAndGet(batchResult.getFailCount());
                result.getErrorMessages().addAll(batchResult.getErrorMessages());

                // 汇总账单处理统计
                result.setBillsGenerated(result.getBillsGenerated() + batchResult.getBillsGenerated());
                result.setBillsAudited(result.getBillsAudited() + batchResult.getBillsAudited());
                result.setPaymentsProcessed(result.getPaymentsProcessed() + batchResult.getPaymentsProcessed());
                result.setPaymentsSucceeded(result.getPaymentsSucceeded() + batchResult.getPaymentsSucceeded());
                result.setPaymentsFailed(result.getPaymentsFailed() + batchResult.getPaymentsFailed());
            }

            result.setSuccessCount(successCount.get());
            result.setFailCount(failCount.get());
            result.setProcessTime(System.currentTimeMillis() - startTime);

            log.info("批量处理抄表记录完成 - 总数: {}, 成功: {}, 失败: {}, 耗时: {}ms, " +
                    "账单生成: {}, 账单审核: {}, 支付处理: {}, 支付成功: {}, 支付失败: {}",
                result.getTotalCount(), result.getSuccessCount(), result.getFailCount(), result.getProcessTime(),
                result.getBillsGenerated(), result.getBillsAudited(), result.getPaymentsProcessed(),
                result.getPaymentsSucceeded(), result.getPaymentsFailed());

        } catch (Exception e) {
            log.error("批量处理抄表记录异常", e);
            result.setFailCount(meterNos.size());
            result.getErrorMessages().add("批量处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 异步处理单个批次
     */
    private CompletableFuture<BatchResult> processBatchAsync(List<String> batch,
                                                             Map<String, WaterfeeMeterVo> meterMap,
                                                             Map<String, WaterfeeMeterReadingRecord> latestRecordMap,
                                                             Long taskId, Date now) {
        return CompletableFuture.supplyAsync(() -> {
            return processBatch(batch, meterMap, latestRecordMap, taskId, now);
        });
    }

    /**
     * 处理单个批次
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public BatchResult processBatch(List<String> batch,
                                    Map<String, WaterfeeMeterVo> meterMap,
                                    Map<String, WaterfeeMeterReadingRecord> latestRecordMap,
                                    Long taskId, Date now) {

        BatchResult result = new BatchResult();
        List<WaterfeeMeterReadingRecord> recordsToInsert = new ArrayList<>();

        for (String meterNo : batch) {
            try {
                WaterfeeMeterVo meter = meterMap.get(meterNo);
                if (meter == null) {
                    result.addError("水表不存在: " + meterNo);
                    continue;
                }

                // 检查机械表季度重复
                if (!Integer.valueOf(2).equals(meter.getMeterType())) {
                    boolean exists = meterReadingRecordService.existsByQuarterAndMeterNo(now, taskId, meterNo);
                    if (exists) {
                        log.debug("机械表本季度已有记录，跳过: {}", meterNo);
                        result.incrementSuccess();
                        continue;
                    }
                }

                WaterfeeMeterReadingRecord record = createOptimizedRecord(meter, latestRecordMap.get(meterNo), taskId, now);
                if (record != null) {
                    recordsToInsert.add(record);
                }

            } catch (Exception e) {
                result.addError("处理水表异常 " + meterNo + ": " + e.getMessage());
                log.error("处理水表记录异常，水表编号: {}", meterNo, e);
            }
        }

        // 批量插入记录
        if (!recordsToInsert.isEmpty()) {
            try {
                batchInsertRecords(recordsToInsert);
                result.setSuccessCount(result.getSuccessCount() + recordsToInsert.size());

                // 使用超高性能的批量账单处理
                HighPerformanceBillProcessingService.HighPerformanceResult billResult =
                    highPerformanceBillProcessingService.ultraHighPerformanceBatchProcessBills(recordsToInsert);

                // 更新结果统计
                updateResultWithHighPerformanceBillProcessing(result, billResult);

            } catch (Exception e) {
                result.setFailCount(result.getFailCount() + recordsToInsert.size());
                result.addError("批量插入失败: " + e.getMessage());
                log.error("批量插入抄表记录失败", e);
            }
        }

        return result;
    }

    /**
     * 批量获取水表信息
     */
    private Map<String, WaterfeeMeterVo> batchGetMeterInfo(List<String> meterNos) {
        List<WaterfeeMeterVo> meters = waterfeeMeterService.queryByNos(meterNos);
        return meters.stream().collect(Collectors.toMap(WaterfeeMeterVo::getMeterNo, meter -> meter));
    }

    /**
     * 批量获取最新抄表记录（优化版本）
     */
    private Map<String, WaterfeeMeterReadingRecord> batchGetLatestRecords(List<String> meterNos) {
        if (meterNos.isEmpty()) {
            return new ConcurrentHashMap<>();
        }

        try {
            // 使用优化的批量查询方法，一次SQL查询获取所有最新记录
            return meterReadingRecordService.batchQueryLatestEntityByMeterNos(meterNos);
        } catch (Exception e) {
            log.error("批量获取最新抄表记录失败，回退到单条查询", e);

            // 回退到原有方法，但使用更小的并发度避免数据库压力
            Map<String, WaterfeeMeterReadingRecord> recordMap = new ConcurrentHashMap<>();
            meterNos.stream().forEach(meterNo -> {
                try {
                    WaterfeeMeterReadingRecord latest = meterReadingRecordService.queryLatestEntityByMeterNo(meterNo);
                    if (latest != null) {
                        recordMap.put(meterNo, latest);
                    }
                } catch (Exception ex) {
                    log.warn("获取最新抄表记录失败，水表编号: {}", meterNo, ex);
                }
            });
            return recordMap;
        }
    }

    /**
     * 创建优化的抄表记录
     */
    private WaterfeeMeterReadingRecord createOptimizedRecord(WaterfeeMeterVo meter,
                                                             WaterfeeMeterReadingRecord latestRecord,
                                                             Long taskId, Date now) {
        WaterfeeMeterReadingRecord record = new WaterfeeMeterReadingRecord();

        record.setMeterNo(meter.getMeterNo());
        record.setMeterBookId(meter.getMeterBookId());
        record.setMeterType(meter.getMeterType() != null ? meter.getMeterType().toString() : null);
        record.setTaskId(taskId);
        record.setSourceType("normal");
        record.setReadingTime(now);
        record.setOldMeterStopReading(0.0);
        record.setWaterUsage(null);

        // 设置上期数据
        if (latestRecord != null) {
            record.setLastReading(latestRecord.getCurrentReading());
            record.setLastReadingTime(latestRecord.getReadingTime());
        } else {
            record.setLastReading(meter.getInitialReading() != null ? meter.getInitialReading().doubleValue() : 0.0);
            record.setLastReadingTime(meter.getInstallDate());
        }

        // 智能表读取当前读数
        if (Integer.valueOf(2).equals(meter.getMeterType())) {
            try {
                Double currentReading = readIntelligentMeter(meter.getMeterId());
                record.setCurrentReading(currentReading);
            } catch (Exception e) {
                log.error("读取智能表数据失败，水表编号: {}", meter.getMeterNo(), e);
                return null;
            }
        } else {
            record.setCurrentReading(null); // 机械表等待人工抄表
        }

        return record;
    }

    /**
     * 批量插入记录（优化版本）
     */
    private void batchInsertRecords(List<WaterfeeMeterReadingRecord> records) {
        if (records.isEmpty()) {
            return;
        }

        try {
            // 使用真正的批量插入，而不是循环单条插入
            // 分批插入以避免单次插入过多数据
            int batchSize = 200;
            for (int i = 0; i < records.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, records.size());
                List<WaterfeeMeterReadingRecord> batch = records.subList(i, endIndex);

                // 使用优化的批量插入功能
                optimizedBatchMapper.batchInsertReadingRecords(batch);
                log.debug("批量插入抄表记录成功，数量: {}", batch.size());
            }

            log.info("批量插入抄表记录完成，总数量: {}", records.size());
        } catch (Exception e) {
            log.error("批量插入失败，回退到单条插入", e);
            // 回退到单条插入
            for (WaterfeeMeterReadingRecord record : records) {
                try {
                    meterReadingRecordService.insertByEntity(record);
                } catch (Exception ex) {
                    log.error("单条插入抄表记录失败，水表编号: {}", record.getMeterNo(), ex);
                }
            }
        }
    }

    /**
     * 更新结果统计（高性能账单处理）
     */
    private void updateResultWithHighPerformanceBillProcessing(BatchResult result,
                                                               HighPerformanceBillProcessingService.HighPerformanceResult billResult) {
        // 直接设置统计值
        result.setBillsGenerated(result.getBillsGenerated() + billResult.getBillsGenerated());
        result.setBillsAudited(result.getBillsAudited() + billResult.getBillsAudited());
        result.setPaymentsProcessed(result.getPaymentsProcessed() + billResult.getPaymentsProcessed());
        result.setPaymentsSucceeded(result.getPaymentsSucceeded() + billResult.getPaymentsSucceeded());
        result.setPaymentsFailed(result.getPaymentsFailed() + billResult.getPaymentsFailed());

        // 添加错误信息
        result.getErrorMessages().addAll(billResult.getErrorMessages());

        log.info("高性能账单处理完成 - 生成: {}, 审核: {}, 支付处理: {}, 支付成功: {}, 支付失败: {}, 耗时: {}ms, 吞吐量: {}/s, 性能等级: {}",
            billResult.getBillsGenerated(), billResult.getBillsAudited(),
            billResult.getPaymentsProcessed(), billResult.getPaymentsSucceeded(),
            billResult.getPaymentsFailed(), billResult.getProcessingTime(),
            String.format("%.2f", billResult.getThroughput()), billResult.getPerformanceLevel());
    }

    /**
     * 读取智能表数据
     */
    private Double readIntelligentMeter(Long meterId) {
        // TODO: 实际应用中需要调用物联网平台API获取智能表数据
        return 200.0; // 模拟返回
    }

    /**
     * 分割列表
     */
    private <T> List<List<T>> partitionList(List<T> list) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += MeterReadingBatchService.DEFAULT_BATCH_SIZE) {
            partitions.add(list.subList(i, Math.min(i + MeterReadingBatchService.DEFAULT_BATCH_SIZE, list.size())));
        }
        return partitions;
    }

    /**
     * 批处理结果
     */
    @Setter
    @Getter
    public static class BatchProcessResult {
        // getters and setters
        private int totalCount;
        private int successCount;
        private int failCount;
        private long processTime;
        private List<String> errorMessages = new ArrayList<>();

        // 账单相关统计
        // 账单处理统计
        private int billsGenerated = 0;
        private int billsAudited = 0;
        private int paymentsProcessed = 0;
        private int paymentsSucceeded = 0;
        private int paymentsFailed = 0;

        public void incrementBillsGenerated() {
            this.billsGenerated++;
        }

        public void incrementBillsGenerated(int count) {
            this.billsGenerated += count;
        }

        public void incrementBillsAudited() {
            this.billsAudited++;
        }

        public void incrementBillsAudited(int count) {
            this.billsAudited += count;
        }

        public void incrementPaymentsProcessed() {
            this.paymentsProcessed++;
        }

        public void incrementPaymentsProcessed(int count) {
            this.paymentsProcessed += count;
        }

        public void incrementPaymentsSucceeded() {
            this.paymentsSucceeded++;
        }

        public void incrementPaymentsSucceeded(int count) {
            this.paymentsSucceeded += count;
        }

        public void incrementPaymentsFailed() {
            this.paymentsFailed++;
        }

        public void incrementPaymentsFailed(int count) {
            this.paymentsFailed += count;
        }
    }

    /**
     * 单批次处理结果
     */
    @Getter
    public static class BatchResult {
        // getters and setters
        @Setter
        private int successCount = 0;
        @Setter
        private int failCount = 0;
        private final List<String> errorMessages = new ArrayList<>();

        // 账单处理统计
        private int billsGenerated = 0;
        private int billsAudited = 0;
        private int paymentsProcessed = 0;
        private int paymentsSucceeded = 0;
        private int paymentsFailed = 0;

        public void incrementSuccess() {
            successCount++;
        }

        public void addError(String error) {
            failCount++;
            errorMessages.add(error);
        }

        // 账单相关操作
        public void incrementBillsGenerated() {
            this.billsGenerated++;
        }

        public void incrementBillsAudited() {
            this.billsAudited++;
        }

        public void incrementPaymentsProcessed() {
            this.paymentsProcessed++;
        }

        public void incrementPaymentsSucceeded() {
            this.paymentsSucceeded++;
        }

        public void incrementPaymentsFailed() {
            this.paymentsFailed++;
        }

        public void setBillsGenerated(int i) {
            this.billsGenerated += i;
        }

        public void setBillsAudited(int i) {
            this.billsAudited += i;
        }

        public void setPaymentsProcessed(int i) {
            this.paymentsProcessed += i;
        }

        public void setPaymentsSucceeded(int i) {
            this.paymentsSucceeded += i;
        }

        public void setPaymentsFailed(int i) {
            this.paymentsFailed += i;
        }
    }
}
