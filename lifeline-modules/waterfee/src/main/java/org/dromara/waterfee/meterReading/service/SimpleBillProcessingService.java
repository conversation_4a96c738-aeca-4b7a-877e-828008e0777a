package org.dromara.waterfee.meterReading.service;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.counter.service.IWaterfeeCounterPaymentService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 简化的账单处理服务
 * 用于解决编译问题，提供基本的账单处理优化功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimpleBillProcessingService {

    private final IMeterReadingRecordService meterReadingRecordService;
    private final IWaterfeeBillService waterfeeBillService;
    private final IWaterfeeCounterPaymentService waterfeeCounterPaymentService;

    // 批处理配置
    private static final int BILL_BATCH_SIZE = 200;
    private static final int PAYMENT_BATCH_SIZE = 100;
    private static final int MAX_RETRY_TIMES = 3;

    /**
     * 优化的批量账单处理
     */
    public BillProcessingResult optimizedBatchProcessBills(List<WaterfeeMeterReadingRecord> records) {
        long startTime = System.currentTimeMillis();
        BillProcessingResult result = new BillProcessingResult();
        result.setTotalRecords(records.size());

        try {
            log.info("开始优化的批量账单处理，记录数量: {}", records.size());

            // 诊断水表类型分布
            diagnoseMeterTypes(records);

            // 1. 分组处理：智能表和机械表
            Map<Boolean, List<WaterfeeMeterReadingRecord>> groupedRecords = records.stream()
                .collect(Collectors.groupingBy(record -> "2".equals(record.getMeterType())));

            List<WaterfeeMeterReadingRecord> intelligentRecords = groupedRecords.getOrDefault(true, new ArrayList<>());
            List<WaterfeeMeterReadingRecord> mechanicalRecords = groupedRecords.getOrDefault(false, new ArrayList<>());

            log.info("水表分组结果 - 智能表: {} 个, 机械表: {} 个", intelligentRecords.size(), mechanicalRecords.size());

            // 2. 并行处理智能表和机械表
            CompletableFuture<BillProcessingResult> intelligentFuture = CompletableFuture.supplyAsync(() ->
                processIntelligentMeterBills(intelligentRecords));

            CompletableFuture<BillProcessingResult> mechanicalFuture = CompletableFuture.supplyAsync(() ->
                processMechanicalMeterBills(mechanicalRecords));

            // 3. 等待并合并结果
            BillProcessingResult intelligentResult = intelligentFuture.join();
            BillProcessingResult mechanicalResult = mechanicalFuture.join();

            mergeResults(result, intelligentResult, mechanicalResult);

            result.setProcessingTime(System.currentTimeMillis() - startTime);

            // 输出详细的性能统计
            logPerformanceMetrics(result);

            log.info("优化的批量账单处理完成 - 总数: {}, 账单生成: {}, 审核: {}, 支付成功: {}, 耗时: {}ms",
                result.getTotalRecords(), result.getBillsGenerated(), result.getBillsAudited(),
                result.getPaymentsSucceeded(), result.getProcessingTime());

        } catch (Exception e) {
            log.error("优化的批量账单处理异常", e);
            result.getErrorMessages().add("批量账单处理异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 处理智能表账单
     */
    private BillProcessingResult processIntelligentMeterBills(List<WaterfeeMeterReadingRecord> records) {
        BillProcessingResult result = new BillProcessingResult();
        if (records.isEmpty()) {
            return result;
        }

        log.info("开始处理智能表账单，数量: {}", records.size());

        AtomicInteger billsGenerated = new AtomicInteger(0);
        AtomicInteger billsAudited = new AtomicInteger(0);
        AtomicInteger paymentsProcessed = new AtomicInteger(0);
        AtomicInteger paymentsSucceeded = new AtomicInteger(0);
        AtomicInteger paymentsFailed = new AtomicInteger(0);

        // 分批并行处理
        List<List<WaterfeeMeterReadingRecord>> batches = partitionList(records);

        List<CompletableFuture<Void>> futures = batches.stream()
            .map(batch -> CompletableFuture.runAsync(() -> {
                processBatch(batch, billsGenerated, billsAudited, paymentsProcessed, paymentsSucceeded, paymentsFailed, true);
            }))
            .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        result.setBillsGenerated(billsGenerated.get());
        result.setBillsAudited(billsAudited.get());
        result.setPaymentsProcessed(paymentsProcessed.get());
        result.setPaymentsSucceeded(paymentsSucceeded.get());
        result.setPaymentsFailed(paymentsFailed.get());

        return result;
    }

    /**
     * 处理机械表账单
     */
    private BillProcessingResult processMechanicalMeterBills(List<WaterfeeMeterReadingRecord> records) {
        BillProcessingResult result = new BillProcessingResult();
        if (records.isEmpty()) {
            return result;
        }

        log.info("开始处理机械表账单，数量: {}", records.size());

        AtomicInteger billsGenerated = new AtomicInteger(0);
        AtomicInteger billsAudited = new AtomicInteger(0);
        AtomicInteger paymentsProcessed = new AtomicInteger(0);
        AtomicInteger paymentsSucceeded = new AtomicInteger(0);
        AtomicInteger paymentsFailed = new AtomicInteger(0);

        // 分批并行处理（机械表不需要自动支付）
        List<List<WaterfeeMeterReadingRecord>> batches = partitionList(records);

        List<CompletableFuture<Void>> futures = batches.stream()
            .map(batch -> CompletableFuture.runAsync(() -> {
                processBatch(batch, billsGenerated, billsAudited, paymentsProcessed, paymentsSucceeded, paymentsFailed, false);
            }))
            .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        result.setBillsGenerated(billsGenerated.get());
        result.setBillsAudited(billsAudited.get());
        result.setPaymentsProcessed(paymentsProcessed.get());
        result.setPaymentsSucceeded(paymentsSucceeded.get());
        result.setPaymentsFailed(paymentsFailed.get());

        return result;
    }

    /**
     * 处理批次
     */
    private void processBatch(List<WaterfeeMeterReadingRecord> batch,
                              AtomicInteger billsGenerated, AtomicInteger billsAudited,
                              AtomicInteger paymentsProcessed, AtomicInteger paymentsSucceeded, AtomicInteger paymentsFailed,
                              boolean enableAutoPayment) {

        for (WaterfeeMeterReadingRecord record : batch) {
            boolean success = false;
            int retryCount = 0;

            while (!success && retryCount < MAX_RETRY_TIMES) {
                try {
                    // 审核记录并获取账单ID
                    List<Long> billIds = meterReadingRecordService.auditRecordRtnBillIds(record);

                    if (billIds != null && !billIds.isEmpty()) {
                        billsGenerated.incrementAndGet();
                        billsAudited.incrementAndGet();

                        // 如果启用自动支付且是智能表（智能表类型为"2"）
                        if (enableAutoPayment && "2".equals(record.getMeterType())) {
                            log.debug("开始处理智能表自动支付，水表编号: {}, 水表类型: {}", record.getMeterNo(), record.getMeterType());
                            processAutoPayment(record, billIds, paymentsProcessed, paymentsSucceeded, paymentsFailed);
                        } else {
                            log.debug("跳过自动支付 - 水表编号: {}, 水表类型: {}, 自动支付启用: {}",
                                record.getMeterNo(), record.getMeterType(), enableAutoPayment);
                        }

                        success = true;
                    } else {
                        log.warn("账单审核未返回账单ID，记录ID: {}", record.getRecordId());
                        success = true; // 不重试
                    }

                } catch (Exception e) {
                    retryCount++;
                    if (retryCount >= MAX_RETRY_TIMES) {
                        log.error("处理记录失败，已达最大重试次数，记录ID: {}", record.getRecordId(), e);
                    } else {
                        log.warn("处理记录失败，第{}次重试，记录ID: {}", retryCount, record.getRecordId(), e);
                        try {
                            Thread.sleep(100L * retryCount); // 递增延迟
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理自动支付
     */
    private void processAutoPayment(WaterfeeMeterReadingRecord record, List<Long> billIds,
                                    AtomicInteger paymentsProcessed, AtomicInteger paymentsSucceeded, AtomicInteger paymentsFailed) {

        log.info("开始处理自动支付 - 水表编号: {}, 账单ID列表: {}", record.getMeterNo(), billIds);

        boolean paymentSuccess = false;
        int paymentRetryCount = 0;
        final int MAX_PAYMENT_RETRY = 2;

        while (!paymentSuccess && paymentRetryCount < MAX_PAYMENT_RETRY) {
            try {
                // 获取账单信息
                Long billId = billIds.get(0);
                log.debug("正在获取账单信息，账单ID: {}", billId);

                WaterfeeBillVo bill = waterfeeBillService.queryById(billId);
                if (bill == null) {
                    log.warn("获取账单信息失败，账单ID: {}, 水表编号: {}", billId, record.getMeterNo());
                    paymentsProcessed.incrementAndGet();
                    paymentsFailed.incrementAndGet();
                    return;
                }

                log.debug("获取到账单信息 - 账单号: {}, 客户ID: {}, 金额: {}, 状态: {}",
                    bill.getBillNumber(), bill.getCustomerId(), bill.getTotalAmount(), bill.getBillStatus());

                // 检查账单状态 - 只有已发行状态的账单才能支付
                if (!"ISSUED".equals(bill.getBillStatus())) {
                    log.info("账单状态不允许支付，跳过自动支付 - 账单号: {}, 状态: {}",
                        bill.getBillNumber(), bill.getBillStatus());
                    paymentsProcessed.incrementAndGet();
                    paymentsFailed.incrementAndGet();
                    return;
                }

                paymentsProcessed.incrementAndGet();
                log.info("开始执行自动支付 - 客户ID: {}, 账单ID: {}, 金额: {}",
                    bill.getCustomerId(), billIds, bill.getTotalAmount());

                // 执行自动支付
                boolean success;
                try {
                    success = waterfeeCounterPaymentService.autoPayBillByDeposit(
                        bill.getCustomerId(),
                        billIds,
                        bill.getTotalAmount(),
                        "智能表自动扣款：" + bill.getBillNumber()
                    );
                    log.info("自动支付调用完成 - 结果: {}, 水表编号: {}, 账单号: {}",
                        success, record.getMeterNo(), bill.getBillNumber());
                } catch (Exception paymentException) {
                    log.error("自动支付服务调用异常 - 水表编号: {}, 账单号: {}",
                        record.getMeterNo(), bill.getBillNumber(), paymentException);
                    success = false;
                }

                if (success) {
                    paymentsSucceeded.incrementAndGet();
                    paymentSuccess = true;
                    log.debug("自动支付成功，水表编号: {}, 账单号: {}, 金额: {}",
                        record.getMeterNo(), bill.getBillNumber(), bill.getTotalAmount());
                } else {
                    paymentRetryCount++;
                    if (paymentRetryCount >= MAX_PAYMENT_RETRY) {
                        paymentsFailed.incrementAndGet();
                        log.warn("自动支付失败，已达最大重试次数，水表编号: {}, 账单号: {}",
                            record.getMeterNo(), bill.getBillNumber());
                    } else {
                        log.warn("自动支付失败，第{}次重试，水表编号: {}, 账单号: {}",
                            paymentRetryCount, record.getMeterNo(), bill.getBillNumber());
                        try {
                            Thread.sleep(500L * paymentRetryCount); // 支付重试延迟
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }

            } catch (Exception e) {
                paymentRetryCount++;
                if (paymentRetryCount >= MAX_PAYMENT_RETRY) {
                    paymentsFailed.incrementAndGet();
                    log.error("自动支付异常，已达最大重试次数，水表编号: {}", record.getMeterNo(), e);
                } else {
                    log.warn("自动支付异常，第{}次重试，水表编号: {}", paymentRetryCount, record.getMeterNo(), e);
                    try {
                        Thread.sleep(500L * paymentRetryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 合并处理结果
     */
    private void mergeResults(BillProcessingResult target, BillProcessingResult... sources) {
        for (BillProcessingResult source : sources) {
            target.setBillsGenerated(target.getBillsGenerated() + source.getBillsGenerated());
            target.setBillsAudited(target.getBillsAudited() + source.getBillsAudited());
            target.setPaymentsProcessed(target.getPaymentsProcessed() + source.getPaymentsProcessed());
            target.setPaymentsSucceeded(target.getPaymentsSucceeded() + source.getPaymentsSucceeded());
            target.setPaymentsFailed(target.getPaymentsFailed() + source.getPaymentsFailed());
            target.getErrorMessages().addAll(source.getErrorMessages());
        }
    }

    /**
     * 输出性能监控指标
     */
    private void logPerformanceMetrics(BillProcessingResult result) {
        if (result.getTotalRecords() == 0) {
            return;
        }

        double processingSpeed = result.getProcessingTime() > 0 ?
            (result.getTotalRecords() * 1000.0) / result.getProcessingTime() : 0;
        double billGenerationRate = (result.getBillsGenerated() * 100.0) / result.getTotalRecords();
        double billAuditRate = result.getBillsGenerated() > 0 ?
            (result.getBillsAudited() * 100.0) / result.getBillsGenerated() : 0;
        double paymentSuccessRate = result.getPaymentsProcessed() > 0 ?
            (result.getPaymentsSucceeded() * 100.0) / result.getPaymentsProcessed() : 0;

        log.info("账单处理性能指标:");
        log.info("- 处理速度: {} 条/秒", processingSpeed);
        log.info("- 账单生成率: {}%", billGenerationRate);
        log.info("- 账单审核率: {}%", billAuditRate);
        log.info("- 支付成功率: {}%", paymentSuccessRate);
        log.info("- 平均每条记录处理时间: {} ms",
            result.getProcessingTime() / (double) result.getTotalRecords());

        // 性能评估
        if (processingSpeed > 100) {
            log.info("✅ 账单处理性能优秀");
        } else if (processingSpeed > 50) {
            log.info("⚠️ 账单处理性能良好");
        } else {
            log.warn("❌ 账单处理性能需要优化");
        }

        if (!result.getErrorMessages().isEmpty()) {
            log.warn("账单处理过程中出现 {} 个错误:", result.getErrorMessages().size());
            result.getErrorMessages().forEach(error -> log.warn("- {}", error));
        }
    }

    /**
     * 诊断水表类型分布
     */
    private void diagnoseMeterTypes(List<WaterfeeMeterReadingRecord> records) {
        Map<String, Long> typeCount = records.stream()
            .collect(Collectors.groupingBy(
                record -> record.getMeterType() != null ? record.getMeterType() : "null",
                Collectors.counting()
            ));

        log.info("水表类型分布诊断:");
        typeCount.forEach((type, count) -> {
            String typeName = switch (type) {
                case "1" -> "机械表";
                case "2" -> "智能表";
                case "null" -> "未设置类型";
                default -> "未知类型(" + type + ")";
            };
            log.info("- {}: {} 个", typeName, count);
        });

        // 检查是否有智能表
        long intelligentCount = records.stream()
            .filter(record -> "2".equals(record.getMeterType()))
            .count();

        if (intelligentCount == 0) {
            log.warn("⚠️ 警告：没有发现智能表记录，自动支付功能将不会执行！");
            log.warn("请检查：");
            log.warn("1. 数据库中水表记录的 meter_type 字段是否正确设置为 2（智能表）");
            log.warn("2. 抄表记录的 meter_type 字段是否正确从水表信息中获取");
        } else {
            log.info("✅ 发现 {} 个智能表记录，将执行自动支付", intelligentCount);
        }
    }

    /**
     * 分割列表
     */
    private <T> List<List<T>> partitionList(List<T> list) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += SimpleBillProcessingService.BILL_BATCH_SIZE) {
            partitions.add(list.subList(i, Math.min(i + SimpleBillProcessingService.BILL_BATCH_SIZE, list.size())));
        }
        return partitions;
    }

    /**
     * 账单处理结果
     */
    @Data
    public static class BillProcessingResult {
        private int totalRecords = 0;
        private int billsGenerated = 0;
        private int billsAudited = 0;
        private int paymentsProcessed = 0;
        private int paymentsSucceeded = 0;
        private int paymentsFailed = 0;
        private long processingTime = 0;
        private List<String> errorMessages = new ArrayList<>();
    }
}
