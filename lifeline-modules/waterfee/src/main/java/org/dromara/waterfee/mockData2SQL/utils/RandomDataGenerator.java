package org.dromara.waterfee.mockData2SQL.utils;

import cn.hutool.core.util.RandomUtil;

/**
 * 随机数据生成工具类
 * 用于生成演示数据
 *
 * <AUTHOR>
 */
public class RandomDataGenerator {

    /**
     * 常见的中文姓氏
     */
    private static final String[] SURNAMES = {
        "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
        "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
        "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧",
        "程", "曾", "彭", "蒋", "蔡", "余", "潘", "袁", "邓", "许",
        "傅", "沈", "曹", "吕", "薛", "雷", "白", "龙", "段", "史"
    };

    /**
     * 常见的中文名字字符
     */
    private static final String[] NAME_CHARS = {
        "伟", "芳", "娜", "秀", "敏", "静", "丽", "强", "磊", "军",
        "洋", "勇", "艳", "杰", "涛", "明", "超", "霞", "平", "刚",
        "桂", "英", "华", "玉", "梅", "文", "红", "金", "学", "雪",
        "莲", "燕", "云", "进", "建", "国", "庆", "祥", "福", "鹏",
        "辉", "鑫", "磊", "浩", "宇", "博", "涵", "轩", "宸", "睿"
    };

    /**
     * 常见的手机号前缀
     */
    private static final String[] PHONE_PREFIXES = {
        "130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
        "150", "151", "152", "153", "155", "156", "157", "158", "159",
        "180", "181", "182", "183", "184", "185", "186", "187", "188", "189",
        "170", "171", "172", "173", "174", "175", "176", "177", "178"
    };

    /**
     * 随机生成中文用户名
     *
     * @return 随机用户名
     */
    public static String generateRandomUserName() {
        // 随机选择姓氏
        String surname = SURNAMES[RandomUtil.randomInt(SURNAMES.length)];

        // 随机生成1-2个字的名字
        StringBuilder name = new StringBuilder();
        int nameLength = RandomUtil.randomInt(1, 3); // 1或2个字
        for (int i = 0; i < nameLength; i++) {
            name.append(NAME_CHARS[RandomUtil.randomInt(NAME_CHARS.length)]);
        }

        return surname + name.toString();
    }

    /**
     * 随机生成手机号码
     *
     * @return 随机手机号码
     */
    public static String generateRandomPhoneNumber() {
        String prefix = PHONE_PREFIXES[RandomUtil.randomInt(PHONE_PREFIXES.length)];
        String suffix = String.format("%08d", RandomUtil.randomInt(10000000, 99999999));
        return prefix + suffix;
    }

    /**
     * 随机生成身份证号码（仅用于演示，非真实身份证）
     *
     * @return 随机身份证号码
     */
    public static String generateRandomIdCard() {
        // 地区代码（北京朝阳区）
        String areaCode = "110105";

        // 出生年月日（1970-2000年）
        int year = RandomUtil.randomInt(1970, 2001);
        int month = RandomUtil.randomInt(1, 13);
        int day = RandomUtil.randomInt(1, 29); // 简化处理，避免月份天数问题

        String birthDate = String.format("%04d%02d%02d", year, month, day);

        // 顺序码（3位随机数）
        String sequenceCode = String.format("%03d", RandomUtil.randomInt(100, 999));

        // 前17位
        String first17 = areaCode + birthDate + sequenceCode;

        // 计算校验码（简化版本，实际应该按照国标算法）
        String[] checkCodes = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "X"};
        String checkCode = checkCodes[RandomUtil.randomInt(checkCodes.length)];

        return first17 + checkCode;
    }

    /**
     * 随机生成地址
     *
     * @return 随机地址
     */
    public static String generateRandomAddress() {
        String[] cities = {"北京市", "上海市", "广州市", "深圳市", "杭州市", "南京市", "武汉市", "成都市"};
        String[] districts = {"朝阳区", "海淀区", "西城区", "东城区", "丰台区", "石景山区", "通州区", "昌平区"};
        String[] communities = {"阳光小区", "绿园小区", "花园小区", "锦绣小区", "和谐小区", "幸福小区", "美好小区", "温馨小区"};

        String city = cities[RandomUtil.randomInt(cities.length)];
        String district = districts[RandomUtil.randomInt(districts.length)];
        String community = communities[RandomUtil.randomInt(communities.length)];
        int building = RandomUtil.randomInt(1, 20);
        int unit = RandomUtil.randomInt(1, 6);
        int room = RandomUtil.randomInt(101, 2999);

        return city + district + community + building + "号楼" + unit + "单元" + room + "室";
    }

    /**
     * 随机生成用户编号
     *
     * @return 随机用户编号
     */
    public static String generateRandomUserNo() {
        return "U" + System.currentTimeMillis() + RandomUtil.randomInt(100, 999);
    }

    /**
     * 随机生成水表编号
     *
     * @return 随机水表编号
     */
    public static String generateRandomMeterNo() {
        return "M" + System.currentTimeMillis() + RandomUtil.randomInt(100, 999);
    }
}
