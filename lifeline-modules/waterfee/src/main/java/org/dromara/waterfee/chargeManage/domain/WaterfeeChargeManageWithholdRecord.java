package org.dromara.waterfee.chargeManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 代扣记录对象 waterfee_charge_manage_withhold_record
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_charge_manage_withhold_record")
public class WaterfeeChargeManageWithholdRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 
     */
    private Long userId;

    /**
     * 
     */
    private Long amount;

    /**
     * 扣款时间
     */
    private Date withholdTime;

    /**
     * 状态（PENDING、SUCCESS、FAILED）
     */
    private String status;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    @TableLogic
    private String delFlag;


}
