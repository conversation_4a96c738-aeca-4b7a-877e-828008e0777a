package org.dromara.waterfee.meterReading.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;

import java.util.List;
import java.util.Map;

/**
 * 优化的批量数据库操作Mapper接口
 * 专门用于高性能的批量数据库操作
 */
@Mapper
public interface OptimizedBatchMapper {

    /**
     * 真正的批量插入抄表记录
     * 使用单条SQL语句插入多条记录，性能远超循环插入
     *
     * @param records 抄表记录列表
     * @return 插入成功的记录数
     */
    int batchInsertReadingRecords(@Param("records") List<WaterfeeMeterReadingRecord> records);

    /**
     * 批量查询水表信息
     * 使用IN查询一次性获取多个水表信息
     *
     * @param meterNos 水表编号列表
     * @return 水表信息列表
     */
    List<WaterfeeMeterVo> batchSelectMetersByNos(@Param("meterNos") List<String> meterNos);

    /**
     * 批量查询最新抄表记录
     * 使用窗口函数或子查询优化，一次性获取多个水表的最新记录
     *
     * @param meterNos 水表编号列表
     * @return 最新抄表记录列表
     */
    List<WaterfeeMeterReadingRecord> batchSelectLatestRecords(@Param("meterNos") List<String> meterNos);

    /**
     * 批量查询账单信息
     * 使用IN查询一次性获取多个账单信息
     *
     * @param billIds 账单ID列表
     * @return 账单信息列表
     */
    List<WaterfeeBillVo> batchSelectBillsByIds(@Param("billIds") List<Long> billIds);

    /**
     * 批量更新抄表记录状态
     * 使用CASE WHEN或批量更新语句
     *
     * @param recordIds 记录ID列表
     * @param status    新状态
     * @return 更新成功的记录数
     */
    int batchUpdateRecordStatus(@Param("recordIds") List<Long> recordIds, @Param("status") String status);

    /**
     * 批量查询用户信息
     * 根据水表编号批量查询对应的用户信息
     *
     * @param meterNos 水表编号列表
     * @return 用户信息映射（水表编号 -> 用户信息）
     */
    List<Map<String, Object>> batchSelectUserInfoByMeterNos(@Param("meterNos") List<String> meterNos);

    /**
     * 批量查询价格配置
     * 根据用户类型批量查询价格配置
     *
     * @param userTypes 用户类型列表
     * @return 价格配置映射
     */
    List<Map<String, Object>> batchSelectPriceConfigByUserTypes(@Param("userTypes") List<String> userTypes);

    /**
     * 批量检查季度重复记录
     * 一次性检查多个水表在指定季度是否已有记录
     *
     * @param meterNos 水表编号列表
     * @param taskId   任务ID
     * @param quarterStart 季度开始时间
     * @param quarterEnd   季度结束时间
     * @return 已存在记录的水表编号列表
     */
    List<String> batchCheckQuarterDuplicates(@Param("meterNos") List<String> meterNos,
                                           @Param("taskId") Long taskId,
                                           @Param("quarterStart") String quarterStart,
                                           @Param("quarterEnd") String quarterEnd);

    /**
     * 批量统计抄表记录
     * 按水表编号统计各种状态的记录数量
     *
     * @param meterNos 水表编号列表
     * @return 统计结果列表
     */
    List<Map<String, Object>> batchStatisticsRecords(@Param("meterNos") List<String> meterNos);

    /**
     * 批量查询账单审核状态
     * 一次性查询多个账单的审核状态
     *
     * @param billIds 账单ID列表
     * @return 账单审核状态映射
     */
    List<Map<String, Object>> batchSelectBillAuditStatus(@Param("billIds") List<Long> billIds);

    /**
     * 批量更新账单状态
     * 批量更新多个账单的状态
     *
     * @param billIds 账单ID列表
     * @param status  新状态
     * @param updateBy 更新人
     * @return 更新成功的记录数
     */
    int batchUpdateBillStatus(@Param("billIds") List<Long> billIds,
                             @Param("status") String status,
                             @Param("updateBy") Long updateBy);

    /**
     * 批量查询客户余额
     * 一次性查询多个客户的账户余额
     *
     * @param customerIds 客户ID列表
     * @return 客户余额映射
     */
    List<Map<String, Object>> batchSelectCustomerBalance(@Param("customerIds") List<Long> customerIds);

    /**
     * 批量插入支付记录
     * 批量插入多条支付记录
     *
     * @param paymentRecords 支付记录数据
     * @return 插入成功的记录数
     */
    int batchInsertPaymentRecords(@Param("records") List<Map<String, Object>> paymentRecords);

    /**
     * 批量查询智能表读数
     * 模拟批量查询智能表的当前读数
     *
     * @param meterIds 水表ID列表
     * @return 水表读数映射
     */
    List<Map<String, Object>> batchSelectIntelligentMeterReadings(@Param("meterIds") List<Long> meterIds);
}
