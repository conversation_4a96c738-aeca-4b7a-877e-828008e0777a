package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageArSplit;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 应收账分账记录业务对象 waterfee_charge_manage_ar_split
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManageArSplit.class, reverseConvertGenerate = false)
public class WaterfeeChargeManageArSplitBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 账单ID
     */
    @NotNull(message = "账单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long billId;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 分账金额
     */
    @NotNull(message = "分账金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long splitAmount;

    /**
     * 
     */
    private String remark;


}
