package org.dromara.waterfee.meterReading.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;
import org.dromara.waterfee.meterReading.domain.bo.WaterfeeReadingTaskBo;
import org.dromara.waterfee.meterReading.mapper.WaterfeeReadingTaskMapper;
import org.dromara.waterfee.meterReading.service.IWaterfeeReadingTaskService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 抄表任务定时调度器
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Slf4j
@Component
@RestController
@RequestMapping("/task")
@RequiredArgsConstructor
public class ReadingTaskScheduler {

    private final WaterfeeReadingTaskMapper waterfeeReadingTaskMapper;
    private final IWaterfeeReadingTaskService waterfeeReadingTaskService;

    /**
     * 每个季度的首月1日0点20分执行，为循环任务创建新的任务记录
     */
    @Scheduled(cron = "0 20 0 1 3,6,9,12 ?")
    @Transactional(rollbackFor = Exception.class)
    public void createQuarterlyTasks() {
        log.info("开始执行抄表任务自动创建定时任务...");

        try {
            // 查询所有正常状态的循环任务
            List<WaterfeeReadingTask> cycleTasks = waterfeeReadingTaskMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<WaterfeeReadingTask>()
                    .eq(WaterfeeReadingTask::getIsCycle, "1")  // 循环任务
                    .eq(WaterfeeReadingTask::getTaskStatus, "1")  // 正常状态
                    .eq(WaterfeeReadingTask::getIsAudited, "1")  // 已审核
                    .isNull(WaterfeeReadingTask::getEndDate)  // 无结束日期
                    .or()
                    .gt(WaterfeeReadingTask::getEndDate, new Date())  // 或结束日期大于当前日期
            );

            if (cycleTasks.isEmpty()) {
                log.info("没有需要处理的循环任务");
                return;
            }

            log.info("找到{}个需要处理的循环任务", cycleTasks.size());

            // 获取当前月份
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
            String currentMonth = monthFormat.format(calendar.getTime());

            // 处理每个循环任务
            for (WaterfeeReadingTask cycleTask : cycleTasks) {
                try {
                    // 检查本月是否已经创建过任务
                    boolean exists = checkTaskExistsForMonth(cycleTask.getTaskId(), currentMonth);
                    if (exists) {
                        log.info("任务[{}]在本月已经创建过，跳过", cycleTask.getTaskName());
                        continue;
                    }

                    // 创建新任务
                    WaterfeeReadingTaskBo newTaskBo = createNewTaskFromCycle(cycleTask, calendar);

                    // 保存新任务
                    boolean success = waterfeeReadingTaskService.insertByBo(newTaskBo);
                    if (success) {
                        log.info("成功为循环任务[{}]创建新任务，ID: {}", cycleTask.getTaskName(), newTaskBo.getTaskId());
                    } else {
                        log.error("为循环任务[{}]创建新任务失败", cycleTask.getTaskName());
                    }
                } catch (Exception e) {
                    log.error("处理循环任务[{}]时发生错误", cycleTask.getTaskName(), e);
                }
            }

            log.info("抄表任务自动创建定时任务执行完成");
        } catch (Exception e) {
            log.error("执行抄表任务自动创建定时任务时发生错误", e);
        }
    }

    /**
     * 检查指定任务在指定月份是否已经创建过任务
     *
     * @param parentTaskId 父任务ID
     * @param month        月份，格式：yyyy-MM
     * @return 是否存在
     */
    private boolean checkTaskExistsForMonth(Long parentTaskId, String month) {
        int count = waterfeeReadingTaskMapper.selectCount(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<WaterfeeReadingTask>()
                .eq(WaterfeeReadingTask::getReadingMonth, month)
                .eq(WaterfeeReadingTask::getTaskId, parentTaskId)
        ).intValue();

        return count > 0;
    }

    /**
     * 根据循环任务创建新任务
     *
     * @param cycleTask 循环任务
     * @param calendar  当前日历
     * @return 新任务
     */
    private WaterfeeReadingTaskBo createNewTaskFromCycle(WaterfeeReadingTask cycleTask, Calendar calendar) {
        WaterfeeReadingTaskBo newTaskBo = new WaterfeeReadingTaskBo();

        // 复制基本信息
        newTaskBo.setTaskName(cycleTask.getTaskName() + "-" + new SimpleDateFormat("yyyyMM").format(calendar.getTime()));
        newTaskBo.setBusinessAreaId(cycleTask.getBusinessAreaId());
        newTaskBo.setMeterBookId(cycleTask.getMeterBookId());
        newTaskBo.setReaderId(cycleTask.getReaderId());
        newTaskBo.setReaderName(cycleTask.getReaderName());
        newTaskBo.setReadingMethod(cycleTask.getReadingMethod());
        newTaskBo.setReadingCycle(cycleTask.getReadingCycle());
        newTaskBo.setReadingDay(cycleTask.getReadingDay());
        newTaskBo.setBaseDay(cycleTask.getBaseDay());
        newTaskBo.setIsCycle("0");  // 新任务不是循环任务
        newTaskBo.setTaskStatus("1");  // 正常状态
        newTaskBo.setPlanReadingNum(cycleTask.getPlanReadingNum());
        newTaskBo.setBookUserNum(cycleTask.getBookUserNum());
        newTaskBo.setRemark("由循环任务自动创建，原任务ID: " + cycleTask.getTaskId());

        // 设置抄表月份
        newTaskBo.setReadingMonth(new SimpleDateFormat("yyyy-MM").format(calendar.getTime()));

        // 计算开始日期和结束日期
        Calendar startCal = (Calendar) calendar.clone();
        startCal.set(Calendar.DAY_OF_MONTH, cycleTask.getReadingDay());

        Calendar endCal = (Calendar) calendar.clone();
        endCal.add(Calendar.MONTH, 1);
        endCal.set(Calendar.DAY_OF_MONTH, 1);
        endCal.add(Calendar.DATE, -1);

        newTaskBo.setStartDate(startCal.getTime());
        newTaskBo.setEndDate(endCal.getTime());

        // 计算下次执行时间
        Calendar nextExecCal = (Calendar) startCal.clone();
        if (nextExecCal.getTime().before(new Date())) {
            // 如果开始日期已经过去，设置为当前日期
            nextExecCal.setTime(new Date());
        }
        newTaskBo.setNextExecuteTime(nextExecCal.getTime());

        return newTaskBo;
    }

    /**
     * 手动触发定时任务（仅用于测试）
     */
    @GetMapping("/createMonthlyTasks")
    public String manualTrigger() {
        try {
            createQuarterlyTasks();
            return "成功触发定时任务";
        } catch (Exception e) {
            log.error("手动触发定时任务失败", e);
            return "触发定时任务失败: " + e.getMessage();
        }
    }
}
