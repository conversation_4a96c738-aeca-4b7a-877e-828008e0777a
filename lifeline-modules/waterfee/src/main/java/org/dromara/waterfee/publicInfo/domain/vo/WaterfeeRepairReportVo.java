package org.dromara.waterfee.publicInfo.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.publicInfo.domain.WaterfeeRepairReport;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 报修管理视图对象 waterfee_repair_report
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeRepairReport.class)
public class WaterfeeRepairReportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long repairId;

    /**
     * 上报问题
     */
    @ExcelProperty(value = "上报问题")
    private String reportContent;

    /**
     * 报修人姓名
     */
    @ExcelProperty(value = "报修人姓名")
    private String reporterName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 报修时间
     */
    @ExcelProperty(value = "报修时间")
    private Date reportTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
