package org.dromara.waterfee.meterReading.service;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 账单处理性能管理器
 * 负责性能监控、配置管理和优化建议
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillProcessingPerformanceManager {

    private final PerformanceConfig performanceConfig;
    
    // 性能统计数据
    private final Map<String, PerformanceMetrics> metricsMap = new ConcurrentHashMap<>();
    private final LongAdder totalProcessedRecords = new LongAdder();
    private final LongAdder totalProcessingTime = new LongAdder();
    private final AtomicLong lastOptimizationTime = new AtomicLong(System.currentTimeMillis());

    /**
     * 记录处理开始
     */
    public void recordProcessingStart(String processId, int recordCount) {
        PerformanceMetrics metrics = new PerformanceMetrics();
        metrics.setProcessId(processId);
        metrics.setRecordCount(recordCount);
        metrics.setStartTime(System.currentTimeMillis());
        metrics.setStartDateTime(LocalDateTime.now());
        
        metricsMap.put(processId, metrics);
        log.debug("开始记录处理性能，进程ID: {}, 记录数: {}", processId, recordCount);
    }

    /**
     * 记录处理完成
     */
    public void recordProcessingComplete(String processId, int successCount, int failCount, 
                                       int billsGenerated, int paymentsSucceeded) {
        PerformanceMetrics metrics = metricsMap.get(processId);
        if (metrics == null) {
            log.warn("未找到处理进程的性能记录: {}", processId);
            return;
        }

        long endTime = System.currentTimeMillis();
        long processingTime = endTime - metrics.getStartTime();
        
        metrics.setEndTime(endTime);
        metrics.setEndDateTime(LocalDateTime.now());
        metrics.setProcessingTime(processingTime);
        metrics.setSuccessCount(successCount);
        metrics.setFailCount(failCount);
        metrics.setBillsGenerated(billsGenerated);
        metrics.setPaymentsSucceeded(paymentsSucceeded);
        
        // 计算性能指标
        calculatePerformanceIndicators(metrics);
        
        // 更新全局统计
        totalProcessedRecords.add(metrics.getRecordCount());
        totalProcessingTime.add(processingTime);
        
        // 性能分析和建议
        analyzePerformanceAndSuggest(metrics);
        
        log.info("处理性能记录完成 - 进程ID: {}, 耗时: {}ms, 吞吐量: {}/s, 成功率: {}%", 
            processId, processingTime, metrics.getThroughput(), metrics.getSuccessRate());
    }

    /**
     * 计算性能指标
     */
    private void calculatePerformanceIndicators(PerformanceMetrics metrics) {
        if (metrics.getProcessingTime() > 0) {
            // 吞吐量（记录数/秒）
            double throughput = (double) metrics.getRecordCount() * 1000 / metrics.getProcessingTime();
            metrics.setThroughput(throughput);
            
            // 成功率
            int totalProcessed = metrics.getSuccessCount() + metrics.getFailCount();
            if (totalProcessed > 0) {
                double successRate = (double) metrics.getSuccessCount() * 100 / totalProcessed;
                metrics.setSuccessRate(successRate);
            }
            
            // 平均处理时间（毫秒/记录）
            double avgProcessingTime = (double) metrics.getProcessingTime() / metrics.getRecordCount();
            metrics.setAvgProcessingTimePerRecord(avgProcessingTime);
            
            // 账单生成效率
            if (metrics.getBillsGenerated() > 0) {
                double billGenerationRate = (double) metrics.getBillsGenerated() * 1000 / metrics.getProcessingTime();
                metrics.setBillGenerationRate(billGenerationRate);
            }
            
            // 支付成功效率
            if (metrics.getPaymentsSucceeded() > 0) {
                double paymentSuccessRate = (double) metrics.getPaymentsSucceeded() * 1000 / metrics.getProcessingTime();
                metrics.setPaymentSuccessRate(paymentSuccessRate);
            }
        }
    }

    /**
     * 性能分析和建议
     */
    private void analyzePerformanceAndSuggest(PerformanceMetrics metrics) {
        List<String> suggestions = new ArrayList<>();
        
        // 吞吐量分析
        if (metrics.getThroughput() < performanceConfig.getMinThroughput()) {
            suggestions.add("吞吐量偏低，建议增加批处理大小或并发数");
        } else if (metrics.getThroughput() > performanceConfig.getMaxThroughput()) {
            suggestions.add("吞吐量很高，性能表现优秀");
        }
        
        // 成功率分析
        if (metrics.getSuccessRate() < performanceConfig.getMinSuccessRate()) {
            suggestions.add("成功率偏低，建议检查错误日志和数据质量");
        }
        
        // 处理时间分析
        if (metrics.getProcessingTime() > performanceConfig.getMaxProcessingTime()) {
            suggestions.add("处理时间过长，建议优化SQL查询或增加数据库连接池");
        }
        
        // 内存使用分析
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        
        if (memoryUsagePercent > performanceConfig.getMaxMemoryUsagePercent()) {
            suggestions.add("内存使用率过高(" + String.format("%.1f%%", memoryUsagePercent) + 
                          ")，建议减少批处理大小或增加JVM内存");
        }
        
        metrics.setOptimizationSuggestions(suggestions);
        
        // 如果有重要建议，记录警告日志
        if (!suggestions.isEmpty()) {
            log.warn("性能优化建议 - 进程ID: {}, 建议: {}", metrics.getProcessId(), suggestions);
        }
    }

    /**
     * 获取性能统计报告
     */
    public PerformanceReport getPerformanceReport() {
        PerformanceReport report = new PerformanceReport();
        
        // 基础统计
        report.setTotalProcessedRecords(totalProcessedRecords.sum());
        report.setTotalProcessingTime(totalProcessingTime.sum());
        
        if (report.getTotalProcessingTime() > 0) {
            double overallThroughput = (double) report.getTotalProcessedRecords() * 1000 / report.getTotalProcessingTime();
            report.setOverallThroughput(overallThroughput);
        }
        
        // 最近的性能指标
        List<PerformanceMetrics> recentMetrics = getRecentMetrics(10);
        report.setRecentMetrics(recentMetrics);
        
        // 性能趋势分析
        analyzeTrends(report, recentMetrics);
        
        // 系统资源状态
        analyzeSystemResources(report);
        
        return report;
    }

    /**
     * 获取最近的性能指标
     */
    private List<PerformanceMetrics> getRecentMetrics(int limit) {
        return metricsMap.values().stream()
            .sorted((m1, m2) -> Long.compare(m2.getStartTime(), m1.getStartTime()))
            .limit(limit)
            .toList();
    }

    /**
     * 分析性能趋势
     */
    private void analyzeTrends(PerformanceReport report, List<PerformanceMetrics> recentMetrics) {
        if (recentMetrics.size() < 2) {
            return;
        }
        
        // 计算吞吐量趋势
        double avgRecentThroughput = recentMetrics.stream()
            .mapToDouble(PerformanceMetrics::getThroughput)
            .average()
            .orElse(0.0);
        
        double avgEarlierThroughput = recentMetrics.stream()
            .skip(recentMetrics.size() / 2)
            .mapToDouble(PerformanceMetrics::getThroughput)
            .average()
            .orElse(0.0);
        
        if (avgRecentThroughput > avgEarlierThroughput * 1.1) {
            report.setThroughputTrend("IMPROVING");
        } else if (avgRecentThroughput < avgEarlierThroughput * 0.9) {
            report.setThroughputTrend("DECLINING");
        } else {
            report.setThroughputTrend("STABLE");
        }
        
        // 计算成功率趋势
        double avgRecentSuccessRate = recentMetrics.stream()
            .mapToDouble(PerformanceMetrics::getSuccessRate)
            .average()
            .orElse(0.0);
        
        report.setAvgRecentSuccessRate(avgRecentSuccessRate);
        report.setAvgRecentThroughput(avgRecentThroughput);
    }

    /**
     * 分析系统资源状态
     */
    private void analyzeSystemResources(PerformanceReport report) {
        Runtime runtime = Runtime.getRuntime();
        
        // 内存使用情况
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        long usedMemory = totalMemory - freeMemory;
        
        report.setTotalMemory(totalMemory);
        report.setUsedMemory(usedMemory);
        report.setFreeMemory(freeMemory);
        report.setMaxMemory(maxMemory);
        report.setMemoryUsagePercent((double) usedMemory / maxMemory * 100);
        
        // CPU核心数
        report.setAvailableProcessors(runtime.availableProcessors());
        
        // 生成系统建议
        generateSystemSuggestions(report);
    }

    /**
     * 生成系统优化建议
     */
    private void generateSystemSuggestions(PerformanceReport report) {
        List<String> systemSuggestions = new ArrayList<>();
        
        if (report.getMemoryUsagePercent() > 80) {
            systemSuggestions.add("内存使用率过高，建议增加JVM堆内存大小");
        }
        
        if (report.getAvgRecentThroughput() < performanceConfig.getMinThroughput()) {
            systemSuggestions.add("整体吞吐量偏低，建议检查数据库连接池配置和索引优化");
        }
        
        if ("DECLINING".equals(report.getThroughputTrend())) {
            systemSuggestions.add("性能呈下降趋势，建议进行系统维护和优化");
        }
        
        report.setSystemSuggestions(systemSuggestions);
    }

    /**
     * 清理过期的性能数据
     */
    public void cleanupExpiredMetrics() {
        long expireTime = System.currentTimeMillis() - performanceConfig.getMetricsRetentionTime();
        
        metricsMap.entrySet().removeIf(entry -> 
            entry.getValue().getStartTime() < expireTime);
        
        log.info("清理过期性能数据完成，当前保留数据量: {}", metricsMap.size());
    }

    /**
     * 性能指标数据
     */
    @Data
    public static class PerformanceMetrics {
        private String processId;
        private int recordCount;
        private long startTime;
        private long endTime;
        private LocalDateTime startDateTime;
        private LocalDateTime endDateTime;
        private long processingTime;
        private int successCount;
        private int failCount;
        private int billsGenerated;
        private int paymentsSucceeded;
        private double throughput; // 吞吐量（记录数/秒）
        private double successRate; // 成功率（%）
        private double avgProcessingTimePerRecord; // 平均处理时间（毫秒/记录）
        private double billGenerationRate; // 账单生成速率（账单数/秒）
        private double paymentSuccessRate; // 支付成功速率（支付数/秒）
        private List<String> optimizationSuggestions = new ArrayList<>();
    }

    /**
     * 性能报告
     */
    @Data
    public static class PerformanceReport {
        private long totalProcessedRecords;
        private long totalProcessingTime;
        private double overallThroughput;
        private String throughputTrend; // IMPROVING, DECLINING, STABLE
        private double avgRecentThroughput;
        private double avgRecentSuccessRate;
        private List<PerformanceMetrics> recentMetrics;
        
        // 系统资源信息
        private long totalMemory;
        private long usedMemory;
        private long freeMemory;
        private long maxMemory;
        private double memoryUsagePercent;
        private int availableProcessors;
        
        // 优化建议
        private List<String> systemSuggestions = new ArrayList<>();
    }

    /**
     * 性能配置
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "waterfee.bill-processing.performance")
    public static class PerformanceConfig {
        private double minThroughput = 100.0; // 最小吞吐量（记录数/秒）
        private double maxThroughput = 2000.0; // 最大吞吐量（记录数/秒）
        private double minSuccessRate = 95.0; // 最小成功率（%）
        private long maxProcessingTime = 300000; // 最大处理时间（毫秒）
        private double maxMemoryUsagePercent = 85.0; // 最大内存使用率（%）
        private long metricsRetentionTime = 24 * 60 * 60 * 1000; // 性能数据保留时间（毫秒）
        private boolean enableAutoOptimization = true; // 是否启用自动优化
        private int autoOptimizationInterval = 60; // 自动优化检查间隔（分钟）
    }
}
