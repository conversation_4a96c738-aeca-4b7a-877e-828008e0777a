package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageUserRelation;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageUserRelationVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageUserRelationBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 依托用户关系维护Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManageUserRelationService {

    /**
     * 查询依托用户关系维护
     *
     * @param id 主键
     * @return 依托用户关系维护
     */
    WaterfeeChargeManageUserRelation queryById(Long id);

    /**
     * 分页查询依托用户关系维护列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 依托用户关系维护分页列表
     */
    TableDataInfo<WaterfeeChargeManageUserRelationVo> queryPageList(WaterfeeChargeManageUserRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的依托用户关系维护列表
     *
     * @param bo 查询条件
     * @return 依托用户关系维护列表
     */
    List<WaterfeeChargeManageUserRelationVo> queryList(WaterfeeChargeManageUserRelationBo bo);

    /**
     * 新增依托用户关系维护
     *
     * @param bo 依托用户关系维护
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManageUserRelationBo bo);

    /**
     * 修改依托用户关系维护
     *
     * @param bo 依托用户关系维护
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManageUserRelationBo bo);

    /**
     * 校验并批量删除依托用户关系维护信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
