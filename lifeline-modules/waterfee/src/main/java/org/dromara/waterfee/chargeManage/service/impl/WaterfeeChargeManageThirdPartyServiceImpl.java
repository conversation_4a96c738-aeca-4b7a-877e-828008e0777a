package org.dromara.waterfee.chargeManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageThirdPartyBo;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageThirdPartyVo;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageThirdParty;
import org.dromara.waterfee.chargeManage.mapper.WaterfeeChargeManageThirdPartyMapper;
import org.dromara.waterfee.chargeManage.service.IWaterfeeChargeManageThirdPartyService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 第三方对账记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class WaterfeeChargeManageThirdPartyServiceImpl implements IWaterfeeChargeManageThirdPartyService {

    private final WaterfeeChargeManageThirdPartyMapper baseMapper;

    /**
     * 查询第三方对账记录
     *
     * @param id 主键
     * @return 第三方对账记录
     */
    @Override
    public WaterfeeChargeManageThirdParty queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询第三方对账记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 第三方对账记录分页列表
     */
    @Override
    public TableDataInfo<WaterfeeChargeManageThirdPartyVo> queryPageList(WaterfeeChargeManageThirdPartyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeChargeManageThirdParty> lqw = buildQueryWrapper(bo);
        Page<WaterfeeChargeManageThirdPartyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的第三方对账记录列表
     *
     * @param bo 查询条件
     * @return 第三方对账记录列表
     */
    @Override
    public List<WaterfeeChargeManageThirdPartyVo> queryList(WaterfeeChargeManageThirdPartyBo bo) {
        LambdaQueryWrapper<WaterfeeChargeManageThirdParty> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeChargeManageThirdParty> buildQueryWrapper(WaterfeeChargeManageThirdPartyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeChargeManageThirdParty> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeChargeManageThirdParty::getCreateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getChannelCode()), WaterfeeChargeManageThirdParty::getChannelCode, bo.getChannelCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLocalOrderNo()), WaterfeeChargeManageThirdParty::getLocalOrderNo, bo.getLocalOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdOrderNo()), WaterfeeChargeManageThirdParty::getThirdOrderNo, bo.getThirdOrderNo());
        lqw.eq(bo.getAmount() != null, WaterfeeChargeManageThirdParty::getAmount, bo.getAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WaterfeeChargeManageThirdParty::getStatus, bo.getStatus());
        lqw.eq(bo.getReconciliationDate() != null, WaterfeeChargeManageThirdParty::getReconciliationDate, bo.getReconciliationDate());
        return lqw;
    }

    /**
     * 新增第三方对账记录
     *
     * @param bo 第三方对账记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeChargeManageThirdPartyBo bo) {
        WaterfeeChargeManageThirdParty add = MapstructUtils.convert(bo, WaterfeeChargeManageThirdParty.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改第三方对账记录
     *
     * @param bo 第三方对账记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeChargeManageThirdPartyBo bo) {
        WaterfeeChargeManageThirdParty update = MapstructUtils.convert(bo, WaterfeeChargeManageThirdParty.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeChargeManageThirdParty entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除第三方对账记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
