package org.dromara.waterfee.chargeManage.service;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageThirdParty;
import org.dromara.waterfee.chargeManage.domain.vo.WaterfeeChargeManageThirdPartyVo;
import org.dromara.waterfee.chargeManage.domain.bo.WaterfeeChargeManageThirdPartyBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 第三方对账记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IWaterfeeChargeManageThirdPartyService {

    /**
     * 查询第三方对账记录
     *
     * @param id 主键
     * @return 第三方对账记录
     */
    WaterfeeChargeManageThirdParty queryById(Long id);

    /**
     * 分页查询第三方对账记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 第三方对账记录分页列表
     */
    TableDataInfo<WaterfeeChargeManageThirdPartyVo> queryPageList(WaterfeeChargeManageThirdPartyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的第三方对账记录列表
     *
     * @param bo 查询条件
     * @return 第三方对账记录列表
     */
    List<WaterfeeChargeManageThirdPartyVo> queryList(WaterfeeChargeManageThirdPartyBo bo);

    /**
     * 新增第三方对账记录
     *
     * @param bo 第三方对账记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeChargeManageThirdPartyBo bo);

    /**
     * 修改第三方对账记录
     *
     * @param bo 第三方对账记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeChargeManageThirdPartyBo bo);

    /**
     * 校验并批量删除第三方对账记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
