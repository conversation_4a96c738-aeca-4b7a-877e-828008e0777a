package org.dromara.waterfee.priceManage.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.waterfee.priceManage.domain.WaterfeeLiquidatedDamagesConfigs;
import org.dromara.waterfee.priceManage.domain.WaterfeeStandardPrice;
import org.dromara.waterfee.priceManage.domain.WaterfeeSurchargeConfigs; // Added import
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeStandardPriceBo;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeStandardPriceVo;
import org.dromara.waterfee.priceManage.mapper.WaterfeeLiquidatedDamagesConfigsMapper;
import org.dromara.waterfee.priceManage.mapper.WaterfeeStandardPriceMapper;
import org.dromara.waterfee.priceManage.mapper.WaterfeeSurchargeConfigsMapper;
import org.dromara.waterfee.priceManage.service.IWaterfeeStandardPriceService;
import org.springframework.stereotype.Service;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.*; // Added import for Objects, Collectors
import java.util.stream.Collectors; // Added specific import

/**
 * 标准价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RequiredArgsConstructor
@Service
public class WaterfeeStandardPriceServiceImpl implements IWaterfeeStandardPriceService {

    private final WaterfeeStandardPriceMapper baseMapper;

    private final WaterfeeLiquidatedDamagesConfigsMapper waterfeeLiquidatedDamagesConfigsMapper;
    private final WaterfeeSurchargeConfigsMapper waterfeeSurchargeConfigsMapper;

    /**
     * 查询标准价格
     *
     * @param id 主键
     * @return 标准价格
     */
    @Override
    public WaterfeeStandardPrice queryById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 分页查询标准价格列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 标准价格分页列表
     */
    @Override
    public TableDataInfo<WaterfeeStandardPriceVo> queryPageList(WaterfeeStandardPriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeeStandardPrice> lqw = buildQueryWrapper(bo);
        Page<WaterfeeStandardPriceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        List<WaterfeeStandardPriceVo> list = result.getRecords();
        if (!list.isEmpty()) {
            // --- Penalty Handling ---
            List<Long> penaltyIds = list.stream()
                .map(WaterfeeStandardPriceVo::getPenaltyId)
                .filter(Objects::nonNull) // Avoid NPE
                .distinct()
                .toList();
            Map<Long, String> penaltyMap = Collections.emptyMap();
            if (!penaltyIds.isEmpty()) {
                List<WaterfeeLiquidatedDamagesConfigs> penaltyConfigs = waterfeeLiquidatedDamagesConfigsMapper.selectBatchIds(penaltyIds);
                penaltyMap = penaltyConfigs.stream().collect(
                    Collectors.toMap(WaterfeeLiquidatedDamagesConfigs::getId, WaterfeeLiquidatedDamagesConfigs::getName));
            }

            // --- Surcharge Handling ---
            // 1. Collect all unique surcharge IDs from the comma-separated strings
            List<Long> allSurchargeIds = list.stream()
                .map(WaterfeeStandardPriceVo::getAdditionalFeeIds) // Get the comma-separated string
                .filter(StringUtils::isNotBlank) // Filter out blank strings
                .map(idsStr -> Arrays.asList(idsStr.split(","))) // Split string into list of strings
                .flatMap(Collection::stream) // Flatten into a stream of string IDs
                .map(String::trim) // Trim whitespace
                .filter(StringUtils::isNotBlank) // Filter out blank strings after trim
                .distinct() // Get unique string IDs
                .map(idStr -> { // Try to convert to Long, return null on error
                    try {
                        return Long.parseLong(idStr);
                    } catch (NumberFormatException e) {
                        // Log error or handle invalid ID format if necessary
                        System.err.println("Invalid surcharge ID format: " + idStr);
                        return null;
                    }
                })
                .filter(Objects::nonNull) // Filter out nulls (conversion errors)
                .distinct() // Ensure uniqueness after conversion
                .toList();

            // 2. Fetch surcharge names if any valid IDs were found
            Map<Long, String> surchargeMap = Collections.emptyMap();
            if (!allSurchargeIds.isEmpty()) {
                List<WaterfeeSurchargeConfigs> surchargeConfigs = waterfeeSurchargeConfigsMapper.selectBatchIds(allSurchargeIds);
                surchargeMap = surchargeConfigs.stream().collect(
                    Collectors.toMap(WaterfeeSurchargeConfigs::getId, WaterfeeSurchargeConfigs::getName));
            }

            // 3. Set names in each VO
            final Map<Long, String> finalPenaltyMap = penaltyMap; // Final for lambda
            final Map<Long, String> finalSurchargeMap = surchargeMap; // Final for lambda
            list.forEach(vo -> {
                // Set penalty name
                if (vo.getPenaltyId() != null) {
                    vo.setPenaltyName(finalPenaltyMap.get(vo.getPenaltyId()));
                }

                // Set surcharge names (comma-separated)
                String additionalFeeIdsStr = vo.getAdditionalFeeIds();
                if (StringUtils.isNotBlank(additionalFeeIdsStr)) {
                    String surchargeNames = Arrays.stream(additionalFeeIdsStr.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .map(idStr -> {
                            try {
                                return Long.parseLong(idStr);
                            } catch (NumberFormatException e) {
                                return null; // Ignore invalid IDs when looking up names
                            }
                        })
                        .filter(Objects::nonNull)
                        .map(finalSurchargeMap::get) // Get name from map using ID
                        .filter(Objects::nonNull) // Filter out if name not found for a valid ID
                        .collect(Collectors.joining(", ")); // Join names with comma and space
                    vo.setAdditionalFeeNames(surchargeNames);
                } else {
                    vo.setAdditionalFeeNames(""); // Set empty string if no IDs
                }
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的标准价格列表
     *
     * @param bo 查询条件
     * @return 标准价格列表
     */
    @Override
    public List<WaterfeeStandardPriceVo> queryList(WaterfeeStandardPriceBo bo) {
        LambdaQueryWrapper<WaterfeeStandardPrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WaterfeeStandardPrice> buildQueryWrapper(WaterfeeStandardPriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WaterfeeStandardPrice> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WaterfeeStandardPrice::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getName()), WaterfeeStandardPrice::getName, bo.getName());
        lqw.eq(bo.getPrice() != null, WaterfeeStandardPrice::getPrice, bo.getPrice());
        lqw.eq(bo.getPenaltyId() != null, WaterfeeStandardPrice::getPenaltyId, bo.getPenaltyId());
        return lqw;
    }

    /**
     * 新增标准价格
     *
     * @param bo 标准价格
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WaterfeeStandardPriceBo bo) {
        WaterfeeStandardPrice add = MapstructUtils.convert(bo, WaterfeeStandardPrice.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改标准价格
     *
     * @param bo 标准价格
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WaterfeeStandardPriceBo bo) {
        WaterfeeStandardPrice update = MapstructUtils.convert(bo, WaterfeeStandardPrice.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WaterfeeStandardPrice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除标准价格信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
