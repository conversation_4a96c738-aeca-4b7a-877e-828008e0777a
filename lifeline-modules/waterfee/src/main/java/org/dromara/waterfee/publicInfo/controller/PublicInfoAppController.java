package org.dromara.waterfee.publicInfo.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.waterfee.publicInfo.domain.vo.WaterfeePublicInfoVo;
import org.dromara.waterfee.publicInfo.service.IWaterfeePublicInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 移动端公共信息控制器
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/publicInfo")
public class PublicInfoAppController extends BaseController {

  private final IWaterfeePublicInfoService publicInfoService;

  /**
   * 查询停水通知列表
   */
  @GetMapping("/waterOutage")
  public R<List<WaterfeePublicInfoVo>> getWaterOutageList() {
    return R.ok(publicInfoService.queryListByType("1"));
  }

  /**
   * 查询供水公告列表
   */
  @GetMapping("/waterSupplyNotice")
  public R<List<WaterfeePublicInfoVo>> getWaterSupplyNoticeList() {
    return R.ok(publicInfoService.queryListByType("2"));
  }

  /**
   * 查询业务常识列表
   */
  @GetMapping("/businessKnowledge")
  public R<List<WaterfeePublicInfoVo>> getBusinessKnowledgeList() {
    return R.ok(publicInfoService.queryListByType("3"));
  }

  /**
   * 查询水费标准列表
   */
  @GetMapping("/waterFeeStandard")
  public R<List<WaterfeePublicInfoVo>> getWaterFeeStandardList() {
    return R.ok(publicInfoService.queryListByType("4"));
  }

  /**
   * 查询营商环境列表
   */
  @GetMapping("/businessEnvironment")
  public R<List<WaterfeePublicInfoVo>> getBusinessEnvironmentList() {
    return R.ok(publicInfoService.queryListByType("5"));
  }

  /**
   * 查询公司简介列表
   */
  @GetMapping("/companyProfile")
  public R<List<WaterfeePublicInfoVo>> getCompanyProfileList() {
    return R.ok(publicInfoService.queryListByType("6"));
  }

  /**
   * 获取公共信息详细信息
   *
   * @param infoId 主键
   */
  @GetMapping("/{infoId}")
  public R<WaterfeePublicInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long infoId) {
    // 增加点击量
    publicInfoService.increaseViewCount(infoId);
    return R.ok(publicInfoService.queryById(infoId));
  }
}