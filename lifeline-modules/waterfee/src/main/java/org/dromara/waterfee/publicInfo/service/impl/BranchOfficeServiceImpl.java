package org.dromara.waterfee.publicInfo.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.waterfee.publicInfo.domain.bo.BranchOfficeBo;
import org.dromara.waterfee.publicInfo.domain.vo.BranchOfficeVo;
import org.dromara.waterfee.publicInfo.domain.BranchOffice;
import org.dromara.waterfee.publicInfo.mapper.BranchOfficeMapper;
import org.dromara.waterfee.publicInfo.service.IBranchOfficeService;
import org.dromara.common.satoken.utils.LoginHelper;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 营业网点Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RequiredArgsConstructor
@Service
public class BranchOfficeServiceImpl implements IBranchOfficeService {

    private final BranchOfficeMapper baseMapper;

    /**
     * 查询营业网点
     *
     * @param branchId 主键
     * @return 营业网点
     */
    @Override
    public BranchOffice queryById(Long branchId){
        return baseMapper.selectById(branchId);
    }

    /**
     * 分页查询营业网点列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 营业网点分页列表
     */
    @Override
    public TableDataInfo<BranchOfficeVo> queryPageList(BranchOfficeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BranchOffice> lqw = buildQueryWrapper(bo);
        Page<BranchOfficeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的营业网点列表
     *
     * @param bo 查询条件
     * @return 营业网点列表
     */
    @Override
    public List<BranchOfficeVo> queryList(BranchOfficeBo bo) {
        LambdaQueryWrapper<BranchOffice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BranchOffice> buildQueryWrapper(BranchOfficeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BranchOffice> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(BranchOffice::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getBranchName()), BranchOffice::getBranchName, bo.getBranchName());
        lqw.like(StringUtils.isNotBlank(bo.getAddress()), BranchOffice::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), BranchOffice::getContactPhone, bo.getContactPhone());
        return lqw;
    }

    /**
     * 新增营业网点
     *
     * @param bo 营业网点
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BranchOfficeBo bo) {
        BranchOffice add = MapstructUtils.convert(bo, BranchOffice.class);
        validEntityBeforeSave(add);
        add.setCreateBy(LoginHelper.getUserId());
        add.setCreateTime(DateUtils.getNowDate());
        add.setCreateDept(LoginHelper.getDeptId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBranchId(add.getBranchId());
        }
        return flag;
    }

    /**
     * 修改营业网点
     *
     * @param bo 营业网点
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BranchOfficeBo bo) {
        BranchOffice update = MapstructUtils.convert(bo, BranchOffice.class);
        validEntityBeforeSave(update);
        update.setUpdateBy(LoginHelper.getUserId());
        update.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchOffice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除营业网点信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
