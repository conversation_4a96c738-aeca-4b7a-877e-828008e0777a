package org.dromara.waterfee.publicInfo.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 偷漏水举报对象 waterfee_leak_report
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_leak_report")
public class WaterfeeLeakReport extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "report_id")
    private Long reportId;

    /**
     * 上报人姓名
     */
    private String reporterName;

    /**
     * 上报人电话
     */
    private String reporterPhone;

    /**
     * 举报时间
     */
    private Date reportTime;

    /**
     * 问题描述
     */
    private String description;

    /**
     * 附件
     */
    private String file;

    /**
     * 经度
     */
    private String lon;

    /**
     * 维度
     */
    private String lat;

    /**
     * 备注
     */
    private String remark;


}
