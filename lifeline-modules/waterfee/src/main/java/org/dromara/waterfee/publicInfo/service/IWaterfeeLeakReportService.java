package org.dromara.waterfee.publicInfo.service;

import org.dromara.waterfee.publicInfo.domain.WaterfeeLeakReport;
import org.dromara.waterfee.publicInfo.domain.vo.WaterfeeLeakReportVo;
import org.dromara.waterfee.publicInfo.domain.bo.WaterfeeLeakReportBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 偷漏水举报Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IWaterfeeLeakReportService {

    /**
     * 查询偷漏水举报
     *
     * @param reportId 主键
     * @return 偷漏水举报
     */
    WaterfeeLeakReport queryById(Long reportId);

    /**
     * 分页查询偷漏水举报列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 偷漏水举报分页列表
     */
    TableDataInfo<WaterfeeLeakReportVo> queryPageList(WaterfeeLeakReportBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的偷漏水举报列表
     *
     * @param bo 查询条件
     * @return 偷漏水举报列表
     */
    List<WaterfeeLeakReportVo> queryList(WaterfeeLeakReportBo bo);

    /**
     * 新增偷漏水举报
     *
     * @param bo 偷漏水举报
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeLeakReportBo bo);

    /**
     * 修改偷漏水举报
     *
     * @param bo 偷漏水举报
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeLeakReportBo bo);

    /**
     * 校验并批量删除偷漏水举报信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
