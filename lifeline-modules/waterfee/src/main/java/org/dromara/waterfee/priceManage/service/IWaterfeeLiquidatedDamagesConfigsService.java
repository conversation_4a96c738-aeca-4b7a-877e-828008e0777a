package org.dromara.waterfee.priceManage.service;

import org.dromara.waterfee.priceManage.domain.WaterfeeLiquidatedDamagesConfigs;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeLiquidatedDamagesConfigsVo;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeLiquidatedDamagesConfigsBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 违约金配置Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface IWaterfeeLiquidatedDamagesConfigsService {

    /**
     * 查询违约金配置
     *
     * @param id 主键
     * @return 违约金配置
     */
    WaterfeeLiquidatedDamagesConfigs queryById(Long id);

    /**
     * 分页查询违约金配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 违约金配置分页列表
     */
    TableDataInfo<WaterfeeLiquidatedDamagesConfigsVo> queryPageList(WaterfeeLiquidatedDamagesConfigsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的违约金配置列表
     *
     * @param bo 查询条件
     * @return 违约金配置列表
     */
    List<WaterfeeLiquidatedDamagesConfigsVo> queryList(WaterfeeLiquidatedDamagesConfigsBo bo);

    /**
     * 新增违约金配置
     *
     * @param bo 违约金配置
     * @return 是否新增成功
     */
    Boolean insertByBo(WaterfeeLiquidatedDamagesConfigsBo bo);

    /**
     * 修改违约金配置
     *
     * @param bo 违约金配置
     * @return 是否修改成功
     */
    Boolean updateByBo(WaterfeeLiquidatedDamagesConfigsBo bo);

    /**
     * 校验并批量删除违约金配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
