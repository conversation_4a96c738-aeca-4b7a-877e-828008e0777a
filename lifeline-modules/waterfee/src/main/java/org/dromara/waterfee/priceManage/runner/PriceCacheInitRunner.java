package org.dromara.waterfee.priceManage.runner;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.priceManage.service.WaterfeePriceCacheService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 价格方案缓存初始化
 *
 * <AUTHOR> Assistant
 * @date 2023-11-15
 */
@Slf4j
@RequiredArgsConstructor
@Component
@Order(1)
public class PriceCacheInitRunner implements ApplicationRunner {

    private final WaterfeePriceCacheService priceCacheService;

    @Override
    public void run(ApplicationArguments args) {
        log.info("开始初始化价格方案缓存...");
        try {
            priceCacheService.initPriceCache();
            log.info("价格方案缓存初始化完成");
        } catch (Exception e) {
            log.error("价格方案缓存初始化失败", e);
        }
    }
}
