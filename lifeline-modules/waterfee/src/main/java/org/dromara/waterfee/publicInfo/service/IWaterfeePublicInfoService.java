package org.dromara.waterfee.publicInfo.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.waterfee.publicInfo.domain.bo.WaterfeePublicInfoBo;
import org.dromara.waterfee.publicInfo.domain.vo.WaterfeePublicInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 公共信息服务接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface IWaterfeePublicInfoService {

  /**
   * 查询公共信息
   *
   * @param infoId 公共信息主键
   * @return 公共信息
   */
  WaterfeePublicInfoVo queryById(Long infoId);

  /**
   * 查询公共信息列表
   *
   * @param bo 公共信息
   * @return 公共信息集合
   */
  TableDataInfo<WaterfeePublicInfoVo> queryPageList(WaterfeePublicInfoBo bo, PageQuery pageQuery);

  /**
   * 查询公共信息列表
   *
   * @param bo 公共信息
   * @return 公共信息集合
   */
  List<WaterfeePublicInfoVo> queryList(WaterfeePublicInfoBo bo);

  /**
   * 根据信息类型查询公共信息列表
   *
   * @param infoType 信息类型
   * @return 公共信息集合
   */
  List<WaterfeePublicInfoVo> queryListByType(String infoType);

  /**
   * 新增公共信息
   *
   * @param bo 公共信息
   * @return 结果
   */
  Boolean insertByBo(WaterfeePublicInfoBo bo);

  /**
   * 新增公共信息并同时生成草稿信息并发布草稿到微信
   *
   * @param bo 公共信息
   * @return 结果
   */
  Boolean insertAndCreateWechatDraft(WaterfeePublicInfoBo bo);

  /**
   * 修改公共信息
   *
   * @param bo 公共信息
   * @return 结果
   */
  Boolean updateByBo(WaterfeePublicInfoBo bo);

  /**
   * 修改公共信息并同时修改草稿信息
   *
   * @param bo 公共信息
   * @return 结果
   */
  Boolean updateAndSyncWechatDraft(WaterfeePublicInfoBo bo);

  /**
   * 增加点击量
   *
   * @param infoId 信息ID
   * @return 结果
   */
  Boolean increaseViewCount(Long infoId);

  /**
   * 发布公共信息
   *
   * @param infoId 信息ID
   * @return 结果
   */
  Boolean publishInfo(Long infoId);

  /**
   * 发布公共信息并同时发布微信草稿成公众号推文
   *
   * @param infoId 信息ID
   * @return 结果
   */
  Boolean publishInfoAndWechatArticle(Long infoId);

  /**
   * 批量删除公共信息
   *
   * @param infoIds 需要删除的公共信息主键集合
   * @return 结果
   */
  Boolean deleteWithValidByIds(Collection<Long> infoIds);

  /**
   * 删除公共信息及关联的微信草稿和推文
   *
   * @param infoIds 需要删除的公共信息主键集合
   * @return 结果
   */
  Boolean deleteWithWechatDraftByIds(Collection<Long> infoIds);
}
