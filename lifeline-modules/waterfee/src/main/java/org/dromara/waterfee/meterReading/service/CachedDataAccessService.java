package org.dromara.waterfee.meterReading.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.waterfee.bill.domain.vo.WaterfeeBillVo;
import org.dromara.waterfee.bill.service.IWaterfeeBillService;
import org.dromara.waterfee.meter.domain.vo.WaterfeeMeterVo;
import org.dromara.waterfee.meter.service.IWaterfeeMeterService;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingRecord;
import org.dromara.waterfee.meterReading.mapper.OptimizedBatchMapper;
import org.dromara.waterfee.user.domain.vo.WaterfeeUserVo;
import org.dromara.waterfee.user.service.IWaterfeeUserService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 基于缓存的超高性能数据访问服务
 * 使用多级缓存、预热、批量加载等技术
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CachedDataAccessService {

    private final OptimizedBatchMapper optimizedBatchMapper;
    private final IWaterfeeBillService waterfeeBillService;
    private final IWaterfeeMeterService waterfeeMeterService;
    private final IWaterfeeUserService waterfeeUserService;

    // 缓存配置
    private static final String CACHE_PREFIX = "waterfee:perf:";
    private static final String METER_CACHE_KEY = CACHE_PREFIX + "meter:";
    private static final String USER_CACHE_KEY = CACHE_PREFIX + "user:";
    private static final String BILL_CACHE_KEY = CACHE_PREFIX + "bill:";
    private static final String LATEST_RECORD_CACHE_KEY = CACHE_PREFIX + "latest_record:";
    private static final String PRICE_CONFIG_CACHE_KEY = CACHE_PREFIX + "price_config:";

    private static final Duration CACHE_TTL = Duration.ofMinutes(30); // 缓存30分钟
    private static final Duration HOT_DATA_TTL = Duration.ofHours(2); // 热点数据缓存2小时

    // 本地缓存 - 减少Redis访问
    private final Map<String, Object> localCache = new ConcurrentHashMap<>();
    private final Map<String, Long> localCacheTimestamp = new ConcurrentHashMap<>();
    private static final long LOCAL_CACHE_TTL = 5 * 60 * 1000; // 本地缓存5分钟

    /**
     * 超高性能批量获取水表信息
     */
    public Map<String, WaterfeeMeterVo> ultraFastGetMeterInfo(List<String> meterNos) {
        if (meterNos.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, WaterfeeMeterVo> result = new ConcurrentHashMap<>();
        List<String> cacheMisses = new ArrayList<>();

        // 1. 先从本地缓存获取
        for (String meterNo : meterNos) {
            WaterfeeMeterVo cached = getFromLocalCache(METER_CACHE_KEY + meterNo, WaterfeeMeterVo.class);
            if (cached != null) {
                result.put(meterNo, cached);
            } else {
                cacheMisses.add(meterNo);
            }
        }

        if (cacheMisses.isEmpty()) {
            return result;
        }

        // 2. 从Redis缓存获取
        List<String> redisMisses = new ArrayList<>();
        for (String meterNo : cacheMisses) {
            String cacheKey = METER_CACHE_KEY + meterNo;
            WaterfeeMeterVo cached = RedisUtils.getCacheObject(cacheKey);
            if (cached != null) {
                result.put(meterNo, cached);
                putToLocalCache(cacheKey, cached);
            } else {
                redisMisses.add(meterNo);
            }
        }

        if (redisMisses.isEmpty()) {
            return result;
        }

        // 3. 从数据库批量获取
        try {
            List<WaterfeeMeterVo> meters = optimizedBatchMapper.batchSelectMetersByNos(redisMisses);
            for (WaterfeeMeterVo meter : meters) {
                result.put(meter.getMeterNo(), meter);
                
                // 异步缓存到Redis和本地缓存
                String cacheKey = METER_CACHE_KEY + meter.getMeterNo();
                CompletableFuture.runAsync(() -> {
                    RedisUtils.setCacheObject(cacheKey, meter, CACHE_TTL);
                    putToLocalCache(cacheKey, meter);
                });
            }
        } catch (Exception e) {
            log.error("批量获取水表信息失败", e);
        }

        return result;
    }

    /**
     * 超高性能批量获取最新抄表记录
     */
    public Map<String, WaterfeeMeterReadingRecord> ultraFastGetLatestRecords(List<String> meterNos) {
        if (meterNos.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, WaterfeeMeterReadingRecord> result = new ConcurrentHashMap<>();
        List<String> cacheMisses = new ArrayList<>();

        // 1. 批量从Redis获取
        List<String> cacheKeys = meterNos.stream()
            .map(meterNo -> LATEST_RECORD_CACHE_KEY + meterNo)
            .collect(Collectors.toList());

        Map<String, WaterfeeMeterReadingRecord> cachedRecords = RedisUtils.getMultiCacheMapValue(cacheKeys);
        
        for (String meterNo : meterNos) {
            String cacheKey = LATEST_RECORD_CACHE_KEY + meterNo;
            WaterfeeMeterReadingRecord cached = cachedRecords.get(cacheKey);
            if (cached != null) {
                result.put(meterNo, cached);
            } else {
                cacheMisses.add(meterNo);
            }
        }

        if (cacheMisses.isEmpty()) {
            return result;
        }

        // 2. 从数据库批量获取
        try {
            List<WaterfeeMeterReadingRecord> records = optimizedBatchMapper.batchSelectLatestRecords(cacheMisses);
            
            // 批量缓存到Redis
            Map<String, WaterfeeMeterReadingRecord> cacheMap = new HashMap<>();
            for (WaterfeeMeterReadingRecord record : records) {
                result.put(record.getMeterNo(), record);
                cacheMap.put(LATEST_RECORD_CACHE_KEY + record.getMeterNo(), record);
            }
            
            // 异步批量缓存
            CompletableFuture.runAsync(() -> {
                RedisUtils.setMultiCacheMapValue(cacheMap, CACHE_TTL);
            });
            
        } catch (Exception e) {
            log.error("批量获取最新抄表记录失败", e);
        }

        return result;
    }

    /**
     * 超高性能批量获取账单信息
     */
    public Map<Long, WaterfeeBillVo> ultraFastGetBillInfo(List<Long> billIds) {
        if (billIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, WaterfeeBillVo> result = new ConcurrentHashMap<>();
        List<Long> cacheMisses = new ArrayList<>();

        // 1. 批量从Redis获取
        for (Long billId : billIds) {
            String cacheKey = BILL_CACHE_KEY + billId;
            WaterfeeBillVo cached = RedisUtils.getCacheObject(cacheKey);
            if (cached != null) {
                result.put(billId, cached);
            } else {
                cacheMisses.add(billId);
            }
        }

        if (cacheMisses.isEmpty()) {
            return result;
        }

        // 2. 从数据库批量获取
        try {
            List<WaterfeeBillVo> bills = optimizedBatchMapper.batchSelectBillsByIds(cacheMisses);
            for (WaterfeeBillVo bill : bills) {
                result.put(bill.getBillId(), bill);
                
                // 异步缓存
                String cacheKey = BILL_CACHE_KEY + bill.getBillId();
                CompletableFuture.runAsync(() -> {
                    RedisUtils.setCacheObject(cacheKey, bill, CACHE_TTL);
                });
            }
        } catch (Exception e) {
            log.error("批量获取账单信息失败", e);
        }

        return result;
    }

    /**
     * 超高性能批量获取用户信息
     */
    public Map<String, Map<String, Object>> ultraFastGetUserInfo(List<String> meterNos) {
        if (meterNos.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Map<String, Object>> result = new ConcurrentHashMap<>();
        List<String> cacheMisses = new ArrayList<>();

        // 1. 从Redis批量获取
        for (String meterNo : meterNos) {
            String cacheKey = USER_CACHE_KEY + meterNo;
            Map<String, Object> cached = RedisUtils.getCacheObject(cacheKey);
            if (cached != null) {
                result.put(meterNo, cached);
            } else {
                cacheMisses.add(meterNo);
            }
        }

        if (cacheMisses.isEmpty()) {
            return result;
        }

        // 2. 从数据库批量获取
        try {
            List<Map<String, Object>> userInfoList = optimizedBatchMapper.batchSelectUserInfoByMeterNos(cacheMisses);
            for (Map<String, Object> userInfo : userInfoList) {
                String meterNo = (String) userInfo.get("meter_no");
                result.put(meterNo, userInfo);
                
                // 异步缓存
                String cacheKey = USER_CACHE_KEY + meterNo;
                CompletableFuture.runAsync(() -> {
                    RedisUtils.setCacheObject(cacheKey, userInfo, CACHE_TTL);
                });
            }
        } catch (Exception e) {
            log.error("批量获取用户信息失败", e);
        }

        return result;
    }

    /**
     * 预热缓存
     */
    public void warmupCache(List<String> meterNos) {
        if (meterNos.isEmpty()) {
            return;
        }

        log.info("开始预热缓存，水表数量: {}", meterNos.size());

        // 异步预热，不阻塞主流程
        CompletableFuture.runAsync(() -> {
            try {
                // 预热水表信息
                ultraFastGetMeterInfo(meterNos);
                
                // 预热最新抄表记录
                ultraFastGetLatestRecords(meterNos);
                
                // 预热用户信息
                ultraFastGetUserInfo(meterNos);
                
                log.info("缓存预热完成");
            } catch (Exception e) {
                log.error("缓存预热失败", e);
            }
        });
    }

    /**
     * 智能缓存失效
     */
    public void invalidateCache(String meterNo) {
        // 删除相关缓存
        RedisUtils.deleteObject(METER_CACHE_KEY + meterNo);
        RedisUtils.deleteObject(LATEST_RECORD_CACHE_KEY + meterNo);
        RedisUtils.deleteObject(USER_CACHE_KEY + meterNo);
        
        // 删除本地缓存
        localCache.remove(METER_CACHE_KEY + meterNo);
        localCache.remove(LATEST_RECORD_CACHE_KEY + meterNo);
        localCache.remove(USER_CACHE_KEY + meterNo);
        
        localCacheTimestamp.remove(METER_CACHE_KEY + meterNo);
        localCacheTimestamp.remove(LATEST_RECORD_CACHE_KEY + meterNo);
        localCacheTimestamp.remove(USER_CACHE_KEY + meterNo);
    }

    /**
     * 批量缓存失效
     */
    public void batchInvalidateCache(List<String> meterNos) {
        List<String> keysToDelete = new ArrayList<>();
        
        for (String meterNo : meterNos) {
            keysToDelete.add(METER_CACHE_KEY + meterNo);
            keysToDelete.add(LATEST_RECORD_CACHE_KEY + meterNo);
            keysToDelete.add(USER_CACHE_KEY + meterNo);
        }
        
        // 批量删除Redis缓存
        RedisUtils.deleteObject(keysToDelete);
        
        // 清理本地缓存
        for (String key : keysToDelete) {
            localCache.remove(key);
            localCacheTimestamp.remove(key);
        }
    }

    /**
     * 从本地缓存获取
     */
    @SuppressWarnings("unchecked")
    private <T> T getFromLocalCache(String key, Class<T> clazz) {
        Long timestamp = localCacheTimestamp.get(key);
        if (timestamp == null || System.currentTimeMillis() - timestamp > LOCAL_CACHE_TTL) {
            localCache.remove(key);
            localCacheTimestamp.remove(key);
            return null;
        }
        
        Object cached = localCache.get(key);
        if (cached != null && clazz.isInstance(cached)) {
            return (T) cached;
        }
        
        return null;
    }

    /**
     * 放入本地缓存
     */
    private void putToLocalCache(String key, Object value) {
        if (localCache.size() > 10000) { // 限制本地缓存大小
            // 清理过期的缓存项
            cleanupLocalCache();
        }
        
        localCache.put(key, value);
        localCacheTimestamp.put(key, System.currentTimeMillis());
    }

    /**
     * 清理本地缓存
     */
    private void cleanupLocalCache() {
        long now = System.currentTimeMillis();
        List<String> expiredKeys = new ArrayList<>();
        
        for (Map.Entry<String, Long> entry : localCacheTimestamp.entrySet()) {
            if (now - entry.getValue() > LOCAL_CACHE_TTL) {
                expiredKeys.add(entry.getKey());
            }
        }
        
        for (String key : expiredKeys) {
            localCache.remove(key);
            localCacheTimestamp.remove(key);
        }
        
        log.debug("清理本地缓存，清理数量: {}, 剩余数量: {}", expiredKeys.size(), localCache.size());
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("localCacheSize", localCache.size());
        stats.put("localCacheTimestampSize", localCacheTimestamp.size());
        
        // 计算缓存命中率等统计信息
        return stats;
    }
}
