package org.dromara.waterfee.chargeManage.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 依托用户关系维护对象 waterfee_charge_manage_user_relation
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_charge_manage_user_relation")
public class WaterfeeChargeManageUserRelation extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 主用户ID
     */
    private Long masterUserId;

    /**
     * 附属用户ID
     */
    private Long attachedUserId;

    /**
     * 关系类型（单位-职工、业主-租户等）
     */
    private String relationType;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    @TableLogic
    private String delFlag;


}
