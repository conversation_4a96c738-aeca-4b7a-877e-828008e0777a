package org.dromara.waterfee.priceManage.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.priceManage.domain.WaterfeeLiquidatedDamagesConfigs;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 违约金配置视图对象 waterfee_liquidated_damages_configs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeLiquidatedDamagesConfigs.class)
public class WaterfeeLiquidatedDamagesConfigsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 违约金名称
     */
    @ExcelProperty(value = "违约金名称")
    private String name;

    /**
     * 计算方式 (e.g., daily_rate, fixed_amount)
     */
    @ExcelProperty(value = "计算方式 (e.g., daily_rate, fixed_amount)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "calculation_method")
    private String calculationMethod;

    /**
     * 固定金额 (if calculation_method is fixed_amount)
     */
    @ExcelProperty(value = "固定金额 (if calculation_method is fixed_amount)")
    private Long fixedAmount;

    /**
     * 利率(%), null if not applicable
     */
    @ExcelProperty(value = "利率(%), null if not applicable")
    private Long interestRatePercent;

    /**
     * 收取方式 (e.g., term, next_month)
     */
    @ExcelProperty(value = "收取方式 (e.g., term, next_month)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "collection_method")
    private String collectionMethod;

    /**
     * 开始日期 (if applicable)
     */
    @ExcelProperty(value = "开始日期 (if applicable)")
    private Date startDate;

    /**
     * 是否大于本金 (True/False)
     */
    @ExcelProperty(value = "是否大于本金 (True/False)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long canExceedPrincipal;

    /**
     * 免除违约金启用状态
     */
    @ExcelProperty(value = "免除违约金启用状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long waiverEnabled;

    /**
     * 免除截止时间 (if waiver_enabled is true)
     */
    @ExcelProperty(value = "免除截止时间 (if waiver_enabled is true)")
    private Date waiverTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;


}
