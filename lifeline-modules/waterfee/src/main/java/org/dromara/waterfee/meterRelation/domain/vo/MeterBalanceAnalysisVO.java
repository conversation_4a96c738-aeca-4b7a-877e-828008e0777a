package org.dromara.waterfee.meterRelation.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:08
 **/


@Data
public class MeterBalanceAnalysisVO {

    @Schema(description = "总表ID")
    private Long parentMeterId;

    @Schema(description = "总表编号")
    private String parentMeterNo;

    @Schema(description = "总表用水量")
    private Double parentWaterUsage;

    @Schema(description = "分表总用水量")
    private Double totalChildWaterUsage;

    @Schema(description = "差值")
    private Double diffWater;

    @Schema(description = "漏损率（百分比）")
    private Double leakRate;

    @Schema(description = "是否异常")
    private Boolean abnormal;

    @Schema(description = "异常原因")
    private String abnormalReason;

    @Schema(description = "对比时间")
    private LocalDateTime readingTime;

    @Schema(description = "分表明细")
    private List<ChildMeterReadingVO> children;

    @Data
    public static class ChildMeterReadingVO {
        private Long meterId;
        private String meterNo;
        private Double waterUsage;
    }
}
