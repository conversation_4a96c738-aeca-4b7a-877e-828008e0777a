package org.dromara.waterfee.meterReading.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingManual;
import org.dromara.waterfee.meterReading.domain.vo.WaterfeeMeterReadingManualVo;

import java.util.Date;
import java.util.List;

/**
 * 抄表补录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface WaterfeeMeterReadingManualMapper extends BaseMapperPlus<WaterfeeMeterReadingManual, WaterfeeMeterReadingManualVo> {

    /**
     * 查询抄表补录列表，包含关联信息
     *
     * @param meterNo   水表编号
     * @param beginTime 开始日期
     * @param endTime   结束日期
     * @return 抄表补录列表
     */
    List<WaterfeeMeterReadingManualVo> selectManualListWithRelated(@Param("meterNo") String meterNo, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 查询抄表补录详细信息，包含关联信息
     *
     * @param manualId 补录ID
     * @return 抄表补录详细信息
     */
    WaterfeeMeterReadingManualVo selectManualDetailWithRelated(@Param("manualId") Long manualId);
}
