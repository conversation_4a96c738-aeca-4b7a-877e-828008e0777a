package org.dromara.waterfee.publicInfo.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.tenant.core.TenantEntity;
import org.dromara.waterfee.publicInfo.domain.entity.WaterfeePublicInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 公共信息视图对象 waterfee_public_info
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@AutoMapper(target = WaterfeePublicInfo.class)
public class WaterfeePublicInfoVo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 信息ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long infoId;

    /**
     * 信息标题
     */
    private String title;

    /**
     * 信息内容
     */
    private String content;

    /**
     * 信息类型（1-停水通知 2-供水公告 3-业务常识 4-水费标准 5-营商环境 6-公司简介）
     */
    private String infoType;

    /**
     * 信息类型名称
     */
    private String infoTypeName;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 截止时间（针对停水通知有效）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 点击量
     */
    private Long viewCount;

    /**
     * 状态（0-未发布 1-已发布）
     */
    private String status;

    /**
     * 关联的微信草稿ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wechatDraftId;

    /**
     * 微信草稿状态
     */
    private String wechatDraftStatus;

    /**
     * 微信草稿状态名称
     */
    private String wechatDraftStatusName;

    /**
     * 微信文章状态
     */
    private String wechatArticleStatus;

    /**
     * 微信文章状态名称
     */
    private String wechatArticleStatusName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 备注
     */
    private String remark;
}
