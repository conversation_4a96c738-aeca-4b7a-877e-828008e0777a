package org.dromara.waterfee.meterRelation.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 关系 DTO
 * @author: wangjs
 * @create: 2025-05-13 09:58
 **/


@Data
public class MeterRelationAddDTO {
    @Schema(description = "总表ID", required = true)
    private Long parentMeterId;

    @Schema(description = "分表ID", required = true)
    private Long childMeterId;
}
