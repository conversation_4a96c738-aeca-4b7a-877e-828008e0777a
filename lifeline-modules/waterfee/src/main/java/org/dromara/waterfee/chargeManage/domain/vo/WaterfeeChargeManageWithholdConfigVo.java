package org.dromara.waterfee.chargeManage.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 代扣配置信息视图对象 waterfee_charge_manage_withhold_config
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeChargeManageWithholdConfig.class)
public class WaterfeeChargeManageWithholdConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long userId;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String bankAccount;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String bankName;

    /**
     * 是否签约
     */
    @ExcelProperty(value = "是否签约")
    private Long signed;

    /**
     * 签约时间
     */
    @ExcelProperty(value = "签约时间")
    private Date signTime;

    /**
     * 取消时间
     */
    @ExcelProperty(value = "取消时间")
    private Date cancelTime;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private String remark;


}
