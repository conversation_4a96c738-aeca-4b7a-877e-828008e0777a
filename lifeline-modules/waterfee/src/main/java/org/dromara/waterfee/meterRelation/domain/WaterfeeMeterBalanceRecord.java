package org.dromara.waterfee.meterRelation.domain;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:47
 **/
@Data
public class WaterfeeMeterBalanceRecord {

    private Long id;

    private Long parentMeterId;

    private String parentMeterNo;

    private LocalDateTime readingTime;

    private Double parentUsage;

    private Double childUsage;

    private Double diffUsage;

    private Double leakRate;

    private String isAbnormal;

    private String abnormalReason;

    private LocalDateTime createTime;

    private String tenantId;

    private String delFlag;
}
