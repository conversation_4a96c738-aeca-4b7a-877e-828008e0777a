package org.dromara.waterfee.meterReading.domain.vo;

import lombok.Data;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时任务执行性能指标VO
 * 用于结构化输出任务执行的详细性能数据
 */
@Data
@Builder
public class TaskExecutionMetrics {

    /**
     * 执行时间信息
     */
    private ExecutionTimeInfo executionTime;

    /**
     * 任务处理统计
     */
    private TaskProcessingStats taskStats;

    /**
     * 水表处理统计
     */
    private MeterProcessingStats meterStats;

    /**
     * 账单处理统计
     */
    private BillingStats billingStats;

    /**
     * 系统累计统计
     */
    private SystemStats systemStats;

    /**
     * 性能分析
     */
    private PerformanceAnalysis performanceAnalysis;

    /**
     * 执行时间信息
     */
    @Data
    @Builder
    public static class ExecutionTimeInfo {
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private long totalDurationMs;
        private double totalDurationSeconds;
        private double averageTaskDurationMs;
    }

    /**
     * 任务处理统计
     */
    @Data
    @Builder
    public static class TaskProcessingStats {
        private int totalTasks;
        private int successfulTasks;
        private int failedTasks;
        private double successRate;
        private int activeTasks;
    }

    /**
     * 水表处理统计
     */
    @Data
    @Builder
    public static class MeterProcessingStats {
        private int totalMetersProcessed;
        private double processingSpeedPerSecond;
        private int averageMetersPerTask;
        private double averageProcessingTimePerMeter;
    }

    /**
     * 账单处理统计
     */
    @Data
    @Builder
    public static class BillingStats {
        private int billsGenerated;
        private int billsAudited;
        private double billAuditRate;
        private int paymentsProcessed;
        private int paymentsSucceeded;
        private int paymentsFailed;
        private double paymentSuccessRate;
    }

    /**
     * 系统累计统计
     */
    @Data
    @Builder
    public static class SystemStats {
        private int totalTasksExecuted;
        private int totalMetersProcessed;
        private double overallSuccessRate;
        private long averageTaskDuration;
        private long averageProcessingTimePerMeter;
        private int totalBillsGenerated;
        private int totalPaymentsSucceeded;
    }

    /**
     * 性能分析
     */
    @Data
    @Builder
    public static class PerformanceAnalysis {
        private String performanceLevel;  // excellent, good, normal, poor
        private double performanceScore;  // 0-100
        private List<String> strengths;
        private List<String> weaknesses;
        private List<String> recommendations;
        private TrendAnalysis trendAnalysis;
    }

    /**
     * 趋势分析
     */
    @Data
    @Builder
    public static class TrendAnalysis {
        private double performanceChangePercent;
        private String trendDirection;  // improving, stable, declining
        private String trendDescription;
    }

    /**
     * 获取性能等级描述
     */
    public String getPerformanceLevelDescription() {
        if (performanceAnalysis == null) return "未知";
        
        switch (performanceAnalysis.getPerformanceLevel()) {
            case "excellent":
                return "优秀 - 系统运行非常高效";
            case "good":
                return "良好 - 系统运行效率较高";
            case "normal":
                return "正常 - 系统运行平稳";
            case "poor":
                return "较差 - 需要优化系统性能";
            default:
                return "未知";
        }
    }

    /**
     * 获取健康状态
     */
    public String getHealthStatus() {
        if (taskStats == null) return "unknown";
        
        if (taskStats.getSuccessRate() >= 98 && 
            (billingStats == null || billingStats.getPaymentSuccessRate() >= 95)) {
            return "healthy";
        } else if (taskStats.getSuccessRate() >= 90 && 
                  (billingStats == null || billingStats.getPaymentSuccessRate() >= 85)) {
            return "warning";
        } else {
            return "critical";
        }
    }

    /**
     * 获取性能摘要文本
     */
    public String getPerformanceSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (executionTime != null) {
            summary.append(String.format("执行耗时: %.2f秒", executionTime.getTotalDurationSeconds()));
        }
        
        if (taskStats != null) {
            summary.append(String.format(", 任务成功率: %.1f%%", taskStats.getSuccessRate()));
        }
        
        if (meterStats != null) {
            summary.append(String.format(", 处理速度: %.1f个/秒", meterStats.getProcessingSpeedPerSecond()));
        }
        
        if (billingStats != null && billingStats.getPaymentsProcessed() > 0) {
            summary.append(String.format(", 支付成功率: %.1f%%", billingStats.getPaymentSuccessRate()));
        }
        
        return summary.toString();
    }

    /**
     * 转换为监控告警格式
     */
    public MonitoringAlert toMonitoringAlert() {
        return MonitoringAlert.builder()
            .timestamp(LocalDateTime.now())
            .healthStatus(getHealthStatus())
            .performanceScore(performanceAnalysis != null ? performanceAnalysis.getPerformanceScore() : 0)
            .successRate(taskStats != null ? taskStats.getSuccessRate() : 0)
            .paymentSuccessRate(billingStats != null ? billingStats.getPaymentSuccessRate() : 0)
            .processingSpeed(meterStats != null ? meterStats.getProcessingSpeedPerSecond() : 0)
            .activeTasks(taskStats != null ? taskStats.getActiveTasks() : 0)
            .alerts(performanceAnalysis != null ? performanceAnalysis.getRecommendations() : null)
            .build();
    }

    /**
     * 监控告警实体
     */
    @Data
    @Builder
    public static class MonitoringAlert {
        private LocalDateTime timestamp;
        private String healthStatus;
        private double performanceScore;
        private double successRate;
        private double paymentSuccessRate;
        private double processingSpeed;
        private int activeTasks;
        private List<String> alerts;
    }

    /**
     * 创建性能指标构建器
     */
    public static TaskExecutionMetricsBuilder createBuilder() {
        return TaskExecutionMetrics.builder();
    }

    /**
     * 格式化输出为表格字符串
     */
    public String toTableString() {
        StringBuilder table = new StringBuilder();
        table.append("┌─────────────────────────────────┬─────────────────────────────────────────────┐\n");
        table.append("│ 指标项                          │ 数值                                        │\n");
        table.append("├─────────────────────────────────┼─────────────────────────────────────────────┤\n");
        
        if (executionTime != null) {
            table.append(String.format("│ 执行总耗时                      │ %,d ms (%.2f 秒)                          │\n", 
                executionTime.getTotalDurationMs(), executionTime.getTotalDurationSeconds()));
        }
        
        if (taskStats != null) {
            table.append(String.format("│ 任务成功率                      │ %.2f%% (%d/%d)                            │\n", 
                taskStats.getSuccessRate(), taskStats.getSuccessfulTasks(), taskStats.getTotalTasks()));
        }
        
        if (meterStats != null) {
            table.append(String.format("│ 水表处理速度                    │ %.2f 个/秒                                │\n", 
                meterStats.getProcessingSpeedPerSecond()));
        }
        
        if (billingStats != null) {
            table.append(String.format("│ 支付成功率                      │ %.2f%% (%d/%d)                            │\n", 
                billingStats.getPaymentSuccessRate(), billingStats.getPaymentsSucceeded(), billingStats.getPaymentsProcessed()));
        }
        
        table.append("└─────────────────────────────────┴─────────────────────────────────────────────┘");
        return table.toString();
    }
}
