package org.dromara.waterfee.publicInfo.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.publicInfo.domain.ComplaintSuggestion;
import org.dromara.waterfee.publicInfo.domain.vo.ComplaintSuggestionVo;
import org.dromara.waterfee.publicInfo.domain.bo.ComplaintSuggestionBo;
import org.dromara.waterfee.publicInfo.service.IComplaintSuggestionService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 投诉建议
 * 前端访问路由地址为:/waterfee/complaintSuggestion
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/complaintSuggestion")
public class ComplaintSuggestionController extends BaseController {

    private final IComplaintSuggestionService complaintSuggestionService;

    /**
     * 查询投诉建议列表
     */
    @SaCheckPermission("waterfee:complaintSuggestion:list")
    @GetMapping("/list")
    public TableDataInfo<ComplaintSuggestionVo> list(ComplaintSuggestionBo bo, PageQuery pageQuery) {
        return complaintSuggestionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出投诉建议列表
     */
    @SaCheckPermission("waterfee:complaintSuggestion:export")
    @Log(title = "投诉建议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ComplaintSuggestionBo bo, HttpServletResponse response) {
        List<ComplaintSuggestionVo> list = complaintSuggestionService.queryList(bo);
        ExcelUtil.exportExcel(list, "投诉建议", ComplaintSuggestionVo.class, response);
    }

    /**
     * 获取投诉建议详细信息
     *
     * @param complaintSuggestionId 主键
     */
    @SaCheckPermission("waterfee:complaintSuggestion:query")
    @GetMapping("/{complaintSuggestionId}")
    public R<ComplaintSuggestion> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("complaintSuggestionId") Long complaintSuggestionId) {
        return R.ok(complaintSuggestionService.queryById(complaintSuggestionId));
    }

    /**
     * 新增投诉建议
     */
    @SaCheckPermission("waterfee:complaintSuggestion:add")
    @Log(title = "投诉建议", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ComplaintSuggestionBo bo) {
        return toAjax(complaintSuggestionService.insertByBo(bo));
    }

    /**
     * 修改投诉建议
     */
    @SaCheckPermission("waterfee:complaintSuggestion:edit")
    @Log(title = "投诉建议", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ComplaintSuggestionBo bo) {
        return toAjax(complaintSuggestionService.updateByBo(bo));
    }

    /**
     * 删除投诉建议
     *
     * @param complaintSuggestionIds 主键串
     */
    @SaCheckPermission("waterfee:complaintSuggestion:remove")
    @Log(title = "投诉建议", businessType = BusinessType.DELETE)
    @DeleteMapping("/{complaintSuggestionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("complaintSuggestionIds") Long[] complaintSuggestionIds) {
        return toAjax(complaintSuggestionService.deleteWithValidByIds(List.of(complaintSuggestionIds), true));
    }
}
