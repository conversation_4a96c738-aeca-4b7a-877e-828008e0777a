package org.dromara.waterfee.priceManage.domain.bo;

import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceTier;

import java.util.List;

/**
 * 阶梯价格配置业务对象 waterfee_price_config
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeePriceConfig.class, reverseConvertGenerate = false)
public class WaterfeePriceConfigBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 价格名称 (例如：居民阶梯价)
     */
    @NotBlank(message = "价格名称 (例如：居民阶梯价)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 用水性质 (例如：'1' 代表居民)
     */
    @NotBlank(message = "用水性质 (例如：'1' 代表居民)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String waterUseType;

    /**
     * 计算方式 (例如：按年、按月、按人口)
     */
    private String calculationMethod;

    /**
     * 是否按人口计算 (0:否, 1:是)
     */
    private Long isPopulation;

    /**
     * 人口数量 (当is_population为1时有效)
     */
    private Long populationCount;

    /**
     * 描述
     */
    private String description;

    /**
     * 阶梯价格列表
     */
    private List<WaterfeePriceTier> priceTiers;


}
