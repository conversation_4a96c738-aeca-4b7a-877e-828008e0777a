package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MechanicalMeterNonResidentUsersPayMeterReadingVO {
    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编码（用水编码）")
    private String userNo;

    /**
     * 用水户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用水地址
     */
    @ExcelProperty(value = "用户地址")
    private String address;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "电话")
    private String phoneNumber;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "身份证号（信用代码）")
    private String certificateNumber;

    /**
     * 抄表时间
     */
    @ExcelProperty(value = "抄表时间")
    private String readingTime;

    /**
     * 用水量
     */
    @ExcelProperty(value = "用水量")
    private Double waterUsage;

    /**
     * 应收金额
     */
    @ExcelProperty(value = "应收金额")
    private BigDecimal totalAmount;

    /**
     * 实收金额
     */
    @ExcelProperty(value = "实收金额")
    private BigDecimal amountPaid;

    /**
     * 欠费金额
     */
    @ExcelProperty(value = "欠费金额")
    private BigDecimal balanceDue;

    /**
     * 缴费时间
     */
    @ExcelProperty(value = "缴费时间")
    private String paymentTime;
}
