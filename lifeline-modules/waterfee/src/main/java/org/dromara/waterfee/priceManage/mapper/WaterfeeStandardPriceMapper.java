package org.dromara.waterfee.priceManage.mapper;

import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.common.mybatis.annotation.DataColumn;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.waterfee.priceManage.domain.WaterfeeStandardPrice;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeStandardPriceVo;

/**
 * 标准价格Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface WaterfeeStandardPriceMapper extends BaseMapperPlus<WaterfeeStandardPrice, WaterfeeStandardPriceVo> {

    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "create_dept"),
        @DataColumn(key = "userName", value = "create_by")
    })
    default <P extends IPage<WaterfeeStandardPriceVo>> P selectVoPage(IPage<WaterfeeStandardPrice> page, Wrapper<WaterfeeStandardPrice> wrapper) {
        return selectVoPage(page, wrapper, this.currentVoClass());
    }

}
