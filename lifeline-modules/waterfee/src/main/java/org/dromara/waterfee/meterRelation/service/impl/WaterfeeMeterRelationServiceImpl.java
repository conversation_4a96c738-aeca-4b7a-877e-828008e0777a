package org.dromara.waterfee.meterRelation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.waterfee.meter.domain.WaterfeeMeter;
import org.dromara.waterfee.meter.mapper.WaterfeeMeterMapper;
import org.dromara.waterfee.meterRelation.domain.WaterfeeMeterRelation;
import org.dromara.waterfee.meterRelation.domain.vo.MeterRelationTreeVO;
import org.dromara.waterfee.meterRelation.mapper.WaterfeeMeterRelationMapper;
import org.dromara.waterfee.meterRelation.service.WaterfeeMeterRelationService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:01
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class WaterfeeMeterRelationServiceImpl implements WaterfeeMeterRelationService {

    private final WaterfeeMeterMapper meterMapper;
    private final WaterfeeMeterRelationMapper relationMapper;

    @Override
    public List<MeterRelationTreeVO> buildMeterTree() {
        List<WaterfeeMeter> allMeters = meterMapper.selectList(new LambdaQueryWrapper<WaterfeeMeter>()
            .eq(WaterfeeMeter::getDelFlag, "0"));
        Map<Long, WaterfeeMeter> meterMap = allMeters.stream().collect(Collectors.toMap(WaterfeeMeter::getMeterId, m -> m));

        // 查询所有关系
        List<WaterfeeMeterRelation> relations = relationMapper.selectList(new LambdaQueryWrapper<WaterfeeMeterRelation>()
            .eq(WaterfeeMeterRelation::getDelFlag, "0"));

        // 构建图结构（parent -> list of children）
        Map<Long, List<Long>> treeMap = new HashMap<>();
        for (WaterfeeMeterRelation rel : relations) {
            treeMap.computeIfAbsent(rel.getParentMeterId(), k -> new ArrayList<>()).add(rel.getChildMeterId());
        }

        // 找出顶层水表（没有作为子表出现过的）
        Set<Long> childIds = relations.stream().map(WaterfeeMeterRelation::getChildMeterId).collect(Collectors.toSet());
        List<Long> rootIds = allMeters.stream().map(WaterfeeMeter::getMeterId)
            .filter(id -> !childIds.contains(id)).collect(Collectors.toList());

        // 构建树
        List<MeterRelationTreeVO> treeList = new ArrayList<>();
        for (Long rootId : rootIds) {
            treeList.add(buildSubTree(rootId, meterMap, treeMap));
        }
        return treeList;
    }

    private MeterRelationTreeVO buildSubTree(Long meterId, Map<Long, WaterfeeMeter> meterMap, Map<Long, List<Long>> treeMap) {
        WaterfeeMeter meter = meterMap.get(meterId);
        MeterRelationTreeVO node = new MeterRelationTreeVO();
        node.setMeterId(meter.getMeterId());
        node.setMeterNo(meter.getMeterNo());

        List<Long> childIds = treeMap.getOrDefault(meterId, new ArrayList<>());
        node.setChildren(childIds.stream()
            .map(childId -> buildSubTree(childId, meterMap, treeMap))
            .collect(Collectors.toList()));
        return node;
    }

}
