package org.dromara.waterfee.meterReading.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.meterReading.domain.WaterfeeReadingTask;

import java.util.Date;

/**
 * 抄表任务业务对象 waterfee_meter_reading_task
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeReadingTask.class, reverseConvertGenerate = false)
public class WaterfeeReadingTaskBo extends BaseEntity {

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空", groups = {EditGroup.class})
    private Long taskId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String taskName;

    /**
     * 营业区域ID
     */
    @NotNull(message = "营业区域不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long businessAreaId;

    /**
     * 抄表手册ID
     */
    @NotNull(message = "抄表手册不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long meterBookId;

    /**
     * 抄表员ID
     */
    @NotNull(message = "抄表员不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long readerId;

    /**
     * 抄表员姓名
     */
    private String readerName;

    /**
     * 抄表方式（字典waterfee_reading_method）
     */
    @NotBlank(message = "抄表方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String readingMethod;

    /**
     * 抄表周期（字典waterfee_reading_cycle）
     */
    @NotBlank(message = "抄表周期不能为空", groups = {AddGroup.class, EditGroup.class})
    private String readingCycle;

    /**
     * 抄表例日（具体安排哪一天进行抄表，通常是设置成每个月固定的一天，比如每月5号）
     */
    @NotNull(message = "抄表例日不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer readingDay;

    /**
     * 抄表基准日（系统计算账单的基准日期，一般也设置成每月的某一天，比如20号）
     */
    @NotNull(message = "抄表基准日不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer baseDay;

    /**
     * 是否循环（0-否 1-是）
     */
    @NotBlank(message = "是否循环不能为空", groups = {AddGroup.class, EditGroup.class})
    private String isCycle;

    /**
     * 任务状态（0-暂停 1-正常）
     */
    private String taskStatus;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 上次执行时间
     */
    private Date lastExecuteTime;

    /**
     * 下次执行时间
     */
    private Date nextExecuteTime;

    /**
     * 表册关联用户数量
     */
    private Integer bookUserNum;

    /**
     * 表册关联水表数量
     */
    private Integer planReadingNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否审核（0-未审核 1-已审核）
     */
    private String isAudited;

    /**
     * 实际抄表数
     */
    private Integer actualReadingNum;

    /**
     * 抄表月份，格式：yyyy-MM
     */
    private String readingMonth;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核人姓名
     */
    private String auditorName;
}
