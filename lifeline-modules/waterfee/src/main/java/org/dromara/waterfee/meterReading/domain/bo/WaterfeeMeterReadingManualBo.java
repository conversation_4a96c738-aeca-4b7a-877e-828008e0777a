package org.dromara.waterfee.meterReading.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingManual;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 抄表补录业务对象 waterfee_meter_reading_manual
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeMeterReadingManual.class, reverseConvertGenerate = false)
public class WaterfeeMeterReadingManualBo extends BaseEntity {

    /**
     * 补录ID
     */
    @NotNull(message = "补录ID不能为空", groups = {EditGroup.class})
    private Long manualId;

    /**
     * 水表编号
     */
    @NotBlank(message = "水表编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String meterNo;

    /**
     * 本期抄表读数
     */
    @NotNull(message = "本期抄表读数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Double currentReading;

    /**
     * 补录时间
     */
    @NotNull(message = "补录时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date readingTime;

    /**
     * 补录原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 旧表止数（非数据库字段，用于计算水量）
     */
    private Double oldMeterStopReading;

    /**
     * 任务ID（非数据库字段，用于关联抄表任务）
     */
    private Long taskId;

    /**
     * 开始日期（非数据库字段，用于查询）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    /**
     * 结束日期（非数据库字段，用于查询）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    private Long meterBookId;
}
