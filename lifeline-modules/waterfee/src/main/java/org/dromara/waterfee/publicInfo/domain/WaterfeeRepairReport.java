package org.dromara.waterfee.publicInfo.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 报修管理对象 waterfee_repair_report
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("waterfee_repair_report")
public class WaterfeeRepairReport extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "repair_id")
    private Long repairId;

    /**
     * 上报问题
     */
    private String reportContent;

    /**
     * 报修人姓名
     */
    private String reporterName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 报修时间
     */
    private Date reportTime;

    /**
     * 备注
     */
    private String remark;


}
