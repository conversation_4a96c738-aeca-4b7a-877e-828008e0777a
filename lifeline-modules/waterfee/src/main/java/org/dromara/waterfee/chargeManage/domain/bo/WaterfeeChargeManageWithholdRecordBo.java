package org.dromara.waterfee.chargeManage.domain.bo;

import org.dromara.waterfee.chargeManage.domain.WaterfeeChargeManageWithholdRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 代扣记录业务对象 waterfee_charge_manage_withhold_record
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WaterfeeChargeManageWithholdRecord.class, reverseConvertGenerate = false)
public class WaterfeeChargeManageWithholdRecordBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 扣款时间
     */
    @NotNull(message = "扣款时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date withholdTime;

    /**
     * 状态（PENDING、SUCCESS、FAILED）
     */
    private String status;

    /**
     * 
     */
    private String remark;


}
