package org.dromara.waterfee.meterBook.service;

import org.dromara.waterfee.meterBook.domain.vo.MeterBookVo;
import org.dromara.waterfee.meterBook.domain.bo.MeterBookBo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 抄表手册服务接口
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface IMeterBookService {

    /**
     * 查询抄表手册
     *
     * @param id 抄表手册主键
     * @return 抄表手册
     */
    MeterBookVo queryById(Long id);

    /**
     * 根据多个表册ID查询表册信息
     *
     * @param bookIds 表册ID集合
     * @return 表册信息集合
     */
    List<MeterBookVo> queryByIds(Collection<Long> bookIds);

    /**
     * 查询抄表手册列表
     *
     * @param bo 抄表手册
     * @return 抄表手册集合
     */
    TableDataInfo<MeterBookVo> queryPageList(MeterBookBo bo, PageQuery pageQuery);

    /**
     * 查询抄表手册列表
     *
     * @param bo 抄表手册
     * @return 抄表手册集合
     */
    List<MeterBookVo> queryList(MeterBookBo bo);

    /**
     * 新增抄表手册
     *
     * @param bo 抄表手册
     * @return 结果
     */
    Boolean insertByBo(MeterBookBo bo);

    /**
     * 修改抄表手册
     *
     * @param bo 抄表手册
     * @return 结果
     */
    Boolean updateByBo(MeterBookBo bo);

    /**
     * 校验并批量删除抄表手册信息
     *
     * @param ids     需要删除的抄表手册主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
