package org.dromara.waterfee.statisticalReport.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TableBookStatisticsNumberOfUsersVO {
    /**
     * 表册名称
     */
    @ExcelProperty(value = "区册名（小区名称）")
    private String bookName;

    /**
     * 总户数
     */
    @ExcelProperty(value = "总户数")
    private Integer totalCount;

    /**
     * 居民户数
     */
    @ExcelProperty(value = "居民")
    private Integer residentCount;

    /**
     * 非居民户数
     */
    @ExcelProperty(value = "非居民")
    private Integer businessCount;

    /**
     * 特种户数
     */
    @ExcelProperty(value = "特种")
    private Integer specialType;

    /**
     * 公共绿化户数
     */
    @ExcelProperty(value = "公共绿化")
    private Integer publicGreeningCount;

    /**
     * 小区绿化户数
     */
    @ExcelProperty(value = "小区绿化")
    private Integer communityGreeningCount;
}
