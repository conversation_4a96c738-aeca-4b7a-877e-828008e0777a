package org.dromara.waterfee.publicInfo.service;

import org.dromara.waterfee.publicInfo.domain.BranchOffice;
import org.dromara.waterfee.publicInfo.domain.vo.BranchOfficeVo;
import org.dromara.waterfee.publicInfo.domain.bo.BranchOfficeBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 营业网点Service接口
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IBranchOfficeService {

    /**
     * 查询营业网点
     *
     * @param branchId 主键
     * @return 营业网点
     */
    BranchOffice queryById(Long branchId);

    /**
     * 分页查询营业网点列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 营业网点分页列表
     */
    TableDataInfo<BranchOfficeVo> queryPageList(BranchOfficeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的营业网点列表
     *
     * @param bo 查询条件
     * @return 营业网点列表
     */
    List<BranchOfficeVo> queryList(BranchOfficeBo bo);

    /**
     * 新增营业网点
     *
     * @param bo 营业网点
     * @return 是否新增成功
     */
    Boolean insertByBo(BranchOfficeBo bo);

    /**
     * 修改营业网点
     *
     * @param bo 营业网点
     * @return 是否修改成功
     */
    Boolean updateByBo(BranchOfficeBo bo);

    /**
     * 校验并批量删除营业网点信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
