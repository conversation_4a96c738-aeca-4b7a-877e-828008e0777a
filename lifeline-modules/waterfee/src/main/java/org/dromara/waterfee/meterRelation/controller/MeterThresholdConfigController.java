package org.dromara.waterfee.meterRelation.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.waterfee.meterRelation.domain.dto.MeterThresholdConfigDTO;
import org.dromara.waterfee.meterRelation.domain.vo.MeterThresholdConfigVO;
import org.dromara.waterfee.meterRelation.service.MeterThresholdConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:28
 **/

@RestController
@RequestMapping("/api/meter/threshold")
@RequiredArgsConstructor
@Tag(name = "总分水表预警阈值")
public class MeterThresholdConfigController {

    private final MeterThresholdConfigService configService;

    @PostMapping("/save")
    @Operation(summary = "保存/更新总分表预警阈值")
    public R<Void> save(@RequestBody MeterThresholdConfigDTO dto) {
        configService.saveOrUpdate(dto);
        return R.ok();
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有阈值配置")
    public R<List<MeterThresholdConfigVO>> list() {
        return R.ok(configService.listAll());
    }
}
