package org.dromara.waterfee.priceManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.priceManage.domain.WaterfeePriceConfig;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeePriceConfigVo;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeePriceConfigBo;
import org.dromara.waterfee.priceManage.service.IWaterfeePriceConfigService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 阶梯价格配置
 * 前端访问路由地址为:/priceManage/priceConfig
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/priceConfig")
public class WaterfeePriceConfigController extends BaseController {

    private final IWaterfeePriceConfigService waterfeePriceConfigService;

    /**
     * 查询阶梯价格配置列表
     */
    @SaCheckPermission("priceManage:priceConfig:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeePriceConfigVo> list(WaterfeePriceConfigBo bo, PageQuery pageQuery) {
        return waterfeePriceConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出阶梯价格配置列表
     */
    @SaCheckPermission("priceManage:priceConfig:export")
    @Log(title = "阶梯价格配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeePriceConfigBo bo, HttpServletResponse response) {
        List<WaterfeePriceConfigVo> list = waterfeePriceConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "阶梯价格配置", WaterfeePriceConfigVo.class, response);
    }

    /**
     * 获取阶梯价格配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("priceManage:priceConfig:query")
    @GetMapping("/{id}")
    public R<WaterfeePriceConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable("id") Long id) {
        return R.ok(waterfeePriceConfigService.queryById(id));
    }

    /**
     * 新增阶梯价格配置
     */
    @SaCheckPermission("priceManage:priceConfig:add")
    @Log(title = "阶梯价格配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeePriceConfigBo bo) {
        return toAjax(waterfeePriceConfigService.insertByBo(bo));
    }

    /**
     * 修改阶梯价格配置
     */
    @SaCheckPermission("priceManage:priceConfig:edit")
    @Log(title = "阶梯价格配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeePriceConfigBo bo) {
        return toAjax(waterfeePriceConfigService.updateByBo(bo));
    }

    /**
     * 删除阶梯价格配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("priceManage:priceConfig:remove")
    @Log(title = "阶梯价格配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeePriceConfigService.deleteWithValidByIds(List.of(ids), true));
    }
}
