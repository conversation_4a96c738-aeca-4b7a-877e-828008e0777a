package org.dromara.waterfee.meterRelation.service;

import org.dromara.waterfee.meterRelation.domain.vo.BalanceAnalyzeResultVO;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: wangjs
 * @create: 2025-05-13 10:09
 **/


public interface MeterBalanceService {

    /**
     * 计算某总表在某时间点的用水平衡情况
     */
    BalanceAnalyzeResultVO analyzeBalance(Long parentMeterId, LocalDateTime readingTime);

}
