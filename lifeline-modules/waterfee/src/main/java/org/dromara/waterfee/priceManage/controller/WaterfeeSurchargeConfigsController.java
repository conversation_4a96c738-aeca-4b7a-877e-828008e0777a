package org.dromara.waterfee.priceManage.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.waterfee.priceManage.domain.WaterfeeSurchargeConfigs;
import org.dromara.waterfee.priceManage.domain.vo.WaterfeeSurchargeConfigsVo;
import org.dromara.waterfee.priceManage.domain.bo.WaterfeeSurchargeConfigsBo;
import org.dromara.waterfee.priceManage.service.IWaterfeeSurchargeConfigsService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 附加费配置 (Surcharge Configurations)
 * 前端访问路由地址为:/waterfee/surchargeConfigs
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/surchargeConfigs")
public class WaterfeeSurchargeConfigsController extends BaseController {

    private final IWaterfeeSurchargeConfigsService waterfeeSurchargeConfigsService;

    /**
     * 查询附加费配置 (Surcharge Configurations)列表
     */
    @SaCheckPermission("waterfee:surchargeConfigs:list")
    @GetMapping("/list")
    public TableDataInfo<WaterfeeSurchargeConfigsVo> list(WaterfeeSurchargeConfigsBo bo, PageQuery pageQuery) {
        return waterfeeSurchargeConfigsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出附加费配置 (Surcharge Configurations)列表
     */
    @SaCheckPermission("waterfee:surchargeConfigs:export")
    @Log(title = "附加费配置 (Surcharge Configurations)", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WaterfeeSurchargeConfigsBo bo, HttpServletResponse response) {
        List<WaterfeeSurchargeConfigsVo> list = waterfeeSurchargeConfigsService.queryList(bo);
        ExcelUtil.exportExcel(list, "附加费配置 (Surcharge Configurations)", WaterfeeSurchargeConfigsVo.class, response);
    }

    /**
     * 获取附加费配置 (Surcharge Configurations)详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("waterfee:surchargeConfigs:query")
    @GetMapping("/{id}")
    public R<WaterfeeSurchargeConfigs> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(waterfeeSurchargeConfigsService.queryById(id));
    }

    /**
     * 新增附加费配置 (Surcharge Configurations)
     */
    @SaCheckPermission("waterfee:surchargeConfigs:add")
    @Log(title = "附加费配置 (Surcharge Configurations)", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WaterfeeSurchargeConfigsBo bo) {
        return toAjax(waterfeeSurchargeConfigsService.insertByBo(bo));
    }

    /**
     * 修改附加费配置 (Surcharge Configurations)
     */
    @SaCheckPermission("waterfee:surchargeConfigs:edit")
    @Log(title = "附加费配置 (Surcharge Configurations)", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WaterfeeSurchargeConfigsBo bo) {
        return toAjax(waterfeeSurchargeConfigsService.updateByBo(bo));
    }

    /**
     * 删除附加费配置 (Surcharge Configurations)
     *
     * @param ids 主键串
     */
    @SaCheckPermission("waterfee:surchargeConfigs:remove")
    @Log(title = "附加费配置 (Surcharge Configurations)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(waterfeeSurchargeConfigsService.deleteWithValidByIds(List.of(ids), true));
    }
}
