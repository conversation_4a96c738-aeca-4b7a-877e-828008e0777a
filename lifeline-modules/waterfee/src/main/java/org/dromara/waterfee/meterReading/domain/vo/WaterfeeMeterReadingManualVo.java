package org.dromara.waterfee.meterReading.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.waterfee.meterReading.domain.WaterfeeMeterReadingManual;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 抄表补录视图对象 waterfee_meter_reading_manual
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WaterfeeMeterReadingManual.class)
public class WaterfeeMeterReadingManualVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 补录ID
     */
    @ExcelProperty(value = "补录ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long manualId;

    /**
     * 水表编号
     */
    @ExcelProperty(value = "水表编号")
    private String meterNo;

    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private String userNo;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    private String userName;

    /**
     * 营业区域
     */
    @ExcelProperty(value = "营业区域")
    private String businessAreaName;

    /**
     * 抄表手册
     */
    @ExcelProperty(value = "抄表手册")
    private String meterBookName;

    /**
     * 上期抄表读数
     */
    @ExcelProperty(value = "上期抄表读数")
    private Double lastReading;

    /**
     * 上期抄表时间
     */
    @ExcelProperty(value = "上期抄表时间")
    private Date lastReadingTime;

    /**
     * 本期抄表读数
     */
    @ExcelProperty(value = "本期抄表读数")
    private Double currentReading;

    /**
     * 旧表止数
     */
    @ExcelProperty(value = "旧表止数")
    private Double oldMeterStopReading;

    /**
     * 本期水量
     */
    @ExcelProperty(value = "本期水量")
    private Double waterUsage;

    /**
     * 补录时间
     */
    @ExcelProperty(value = "补录时间")
    private Date readingTime;

    /**
     * 补录原因
     */
    @ExcelProperty(value = "补录原因")
    private String reason;

    /**
     * 补录人
     */
    @ExcelProperty(value = "补录人")
    private String operator;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}
