package org.dromara.waterfee.publicInfo.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteConfigService;
import org.dromara.waterfee.publicInfo.domain.bo.WaterfeePublicInfoBo;
import org.dromara.waterfee.publicInfo.domain.entity.WaterfeePublicInfo;
import org.dromara.waterfee.publicInfo.domain.vo.WaterfeePublicInfoVo;
import org.dromara.waterfee.publicInfo.mapper.WaterfeePublicInfoMapper;
import org.dromara.waterfee.publicInfo.service.IWaterfeePublicInfoService;
import org.dromara.waterfee.wechat.domain.bo.WechatDraftBo;
import org.dromara.waterfee.wechat.domain.vo.WechatDraftVo;
import org.dromara.waterfee.wechat.enums.ArticleStatusEnum;
import org.dromara.waterfee.wechat.enums.DraftStatusEnum;
import org.dromara.waterfee.wechat.enums.DraftTypeEnum;
import org.dromara.waterfee.wechat.service.IWechatDraftService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 公共信息服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@RequiredArgsConstructor
@Service
public class WaterfeePublicInfoServiceImpl implements IWaterfeePublicInfoService {

    private final WaterfeePublicInfoMapper baseMapper;
    private final IWechatDraftService wechatDraftService;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    /**
     * 查询公共信息
     *
     * @param infoId 公共信息主键
     * @return 公共信息
     */
    @Override
    public WaterfeePublicInfoVo queryById(Long infoId) {
        WaterfeePublicInfoVo vo = baseMapper.selectVoById(infoId);
        if (vo != null) {
            setInfoTypeName(vo);
            setWechatDraftInfo(vo);
        }
        return vo;
    }

    /**
     * 查询公共信息列表
     *
     * @param bo 公共信息
     * @return 公共信息
     */
    @Override
    public TableDataInfo<WaterfeePublicInfoVo> queryPageList(WaterfeePublicInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WaterfeePublicInfo> lqw = buildQueryWrapper(bo);
        Page<WaterfeePublicInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        for (WaterfeePublicInfoVo vo : result.getRecords()) {
            setInfoTypeName(vo);
            setWechatDraftInfo(vo);
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询公共信息列表
     *
     * @param bo 公共信息
     * @return 公共信息
     */
    @Override
    public List<WaterfeePublicInfoVo> queryList(WaterfeePublicInfoBo bo) {
        LambdaQueryWrapper<WaterfeePublicInfo> lqw = buildQueryWrapper(bo);
        List<WaterfeePublicInfoVo> list = baseMapper.selectVoList(lqw);
        for (WaterfeePublicInfoVo vo : list) {
            setInfoTypeName(vo);
            setWechatDraftInfo(vo);
        }
        return list;
    }

    /**
     * 根据信息类型查询公共信息列表
     *
     * @param infoType 信息类型
     * @return 公共信息集合
     */
    @Override
    public List<WaterfeePublicInfoVo> queryListByType(String infoType) {
        LambdaQueryWrapper<WaterfeePublicInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(infoType), WaterfeePublicInfo::getInfoType, infoType)
            .eq(WaterfeePublicInfo::getStatus, "1")
            .orderByDesc(WaterfeePublicInfo::getPublishTime);
        List<WaterfeePublicInfoVo> list = baseMapper.selectVoList(lqw);
        for (WaterfeePublicInfoVo vo : list) {
            setInfoTypeName(vo);
            setWechatDraftInfo(vo);
        }
        return list;
    }

    private LambdaQueryWrapper<WaterfeePublicInfo> buildQueryWrapper(WaterfeePublicInfoBo bo) {
        LambdaQueryWrapper<WaterfeePublicInfo> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), WaterfeePublicInfo::getTitle, bo.getTitle())
            .eq(StringUtils.isNotBlank(bo.getInfoType()), WaterfeePublicInfo::getInfoType, bo.getInfoType())
            .eq(StringUtils.isNotBlank(bo.getStatus()), WaterfeePublicInfo::getStatus, bo.getStatus())
            .orderByDesc(WaterfeePublicInfo::getCreateTime);
        return lqw;
    }

    /**
     * 新增公共信息
     *
     * @param bo 公共信息
     * @return 结果
     */
    @Override
    public Boolean insertByBo(WaterfeePublicInfoBo bo) {
        WaterfeePublicInfo add = MapstructUtils.convert(bo, WaterfeePublicInfo.class);
        // 默认未发布状态
        if (StringUtils.isEmpty(add.getStatus())) {
            add.setStatus("0");
        }
        if(bo.getInfoType().equals("1")) {
            //发布停水通知
            WechatDraftBo wechatDraft =  new WechatDraftBo();
            wechatDraft.setTitle(bo.getTitle());
            wechatDraft.setDraftType(DraftTypeEnum.WATER_OUTAGE.getCode());
            wechatDraft.setContent(bo.getContent());
            wechatDraft.setAuthor("水务公司");
            wechatDraft.setDigest("紧急停水通知，请提前准备");
            wechatDraft.setNeedOpenComment(false);
            wechatDraft.setOnlyFansCanComment(false);
        }else if(bo.getInfoType().equals("2")) {
            //发布供水通知
            WechatDraftBo wechatDraft =  new WechatDraftBo();
            wechatDraft.setTitle(bo.getTitle());
            wechatDraft.setDraftType(DraftTypeEnum.WATER_SUPPLY.getCode());
            wechatDraft.setContent(bo.getContent());
            wechatDraft.setAuthor("水务公司");
            wechatDraft.setDigest("供水恢复通知");
            wechatDraft.setNeedOpenComment(false);
            wechatDraft.setOnlyFansCanComment(false);
        }
        // 默认点击量为0
        add.setViewCount(0L);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改公共信息
     *
     * @param bo 公共信息
     * @return 结果
     */
    @Override
    public Boolean updateByBo(WaterfeePublicInfoBo bo) {
        WaterfeePublicInfo update = MapstructUtils.convert(bo, WaterfeePublicInfo.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 增加点击量
     *
     * @param infoId 信息ID
     * @return 结果
     */
    @Override
    public Boolean increaseViewCount(Long infoId) {
        WaterfeePublicInfo info = baseMapper.selectById(infoId);
        if (info != null) {
            info.setViewCount(info.getViewCount() + 1);
            return baseMapper.updateById(info) > 0;
        }
        return false;
    }

    /**
     * 发布公共信息
     *
     * @param infoId 信息ID
     * @return 结果
     */
    @Override
    public Boolean publishInfo(Long infoId) {
        WaterfeePublicInfo info = baseMapper.selectById(infoId);
        if (info != null) {
            info.setStatus("1");
            info.setPublishTime(new Date());
            info.setPublisher(LoginHelper.getUsername());
            return baseMapper.updateById(info) > 0;
        }
        return false;
    }

    /**
     * 批量删除公共信息
     *
     * @param infoIds 需要删除的公共信息主键
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> infoIds) {
        return baseMapper.deleteBatchIds(infoIds) > 0;
    }

    /**
     * 新增公共信息并同时生成草稿信息并发布草稿到微信
     */
    @Override
    public Boolean insertAndCreateWechatDraft(WaterfeePublicInfoBo bo) {
        // 1. 先新增公共信息
        Boolean insertResult = insertByBo(bo);
        if (!insertResult) {
            return false;
        }

        try {
            // 2. 创建微信草稿
            WechatDraftBo draftBo = createWechatDraftFromPublicInfo(bo);
            Boolean draftResult = wechatDraftService.insertAndPublishByBo(draftBo);

            if (draftResult) {
                // 3. 更新公共信息的微信草稿关联ID
                WaterfeePublicInfo info = baseMapper.selectById(bo.getInfoId());
                info.setWechatDraftId(draftBo.getDraftId());
                baseMapper.updateById(info);
                return true;
            }
        } catch (Exception e) {
            // 如果微信草稿创建失败，不影响公共信息的创建
            // 可以记录日志，但不抛出异常
            System.err.println("创建微信草稿失败: " + e.getMessage());
        }

        return true; // 公共信息创建成功即可
    }

    /**
     * 修改公共信息并同时修改草稿信息
     */
    @Override
    public Boolean updateAndSyncWechatDraft(WaterfeePublicInfoBo bo) {
        // 1. 先更新公共信息
        Boolean updateResult = updateByBo(bo);
        if (!updateResult) {
            return false;
        }

        // 2. 如果有关联的微信草稿，则同步更新
        WaterfeePublicInfo info = baseMapper.selectById(bo.getInfoId());
        if (info.getWechatDraftId() != null) {
            try {
                WechatDraftBo draftBo = createWechatDraftFromPublicInfo(bo);
                draftBo.setDraftId(info.getWechatDraftId());
                wechatDraftService.updateAndSyncByBo(draftBo);
            } catch (Exception e) {
                // 微信草稿更新失败不影响公共信息更新
                System.err.println("同步更新微信草稿失败: " + e.getMessage());
            }
        }

        return true;
    }

    /**
     * 发布公共信息并同时发布微信草稿成公众号推文
     */
    @Override
    public Boolean publishInfoAndWechatArticle(Long infoId) {
        // 1. 先发布公共信息
        Boolean publishResult = publishInfo(infoId);
        if (!publishResult) {
            return false;
        }

        // 2. 如果有关联的微信草稿，则发布为文章
        WaterfeePublicInfo info = baseMapper.selectById(infoId);
        if (info.getWechatDraftId() != null) {
            try {
                wechatDraftService.publishArticle(info.getWechatDraftId());
            } catch (Exception e) {
                // 微信文章发布失败不影响公共信息发布
                System.err.println("发布微信文章失败: " + e.getMessage());
            }
        }

        return true;
    }

    /**
     * 删除公共信息及关联的微信草稿和推文
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithWechatDraftByIds(Collection<Long> infoIds) {
        for (Long infoId : infoIds) {
            WaterfeePublicInfo info = baseMapper.selectById(infoId);
            if (info != null && info.getWechatDraftId() != null) {
                try {
                    // 检查微信草稿状态，决定删除策略
                    WechatDraftVo draft = wechatDraftService.queryById(info.getWechatDraftId());
                    if (draft != null) {
                        if (ArticleStatusEnum.PUBLISHED.getCode().equals(draft.getArticleStatus())) {
                            // 已发布成推文，需要删除推文
                            wechatDraftService.deletePublishedArticle(info.getWechatDraftId());
                        } else {
                            // 未发布成推文，删除微信草稿
                            if (draft.getMediaId() != null) {
                                wechatDraftService.deleteWechatDraft(draft.getMediaId());
                            }
                            // 删除本地草稿记录
                            wechatDraftService.deleteWithValidByIds(List.of(info.getWechatDraftId()));
                        }
                    }
                    baseMapper.deleteById(infoId);
                } catch (Exception e) {
                    System.err.println("删除微信草稿失败: " + e.getMessage());
                    throw new RuntimeException("删除失败");
                }
            }
        }

        // 删除公共信息记录
        return baseMapper.deleteBatchIds(infoIds) > 0;
    }

    /**
     * 根据公共信息创建微信草稿对象
     */
    private WechatDraftBo createWechatDraftFromPublicInfo(WaterfeePublicInfoBo publicInfo) {
        WechatDraftBo draftBo = new WechatDraftBo();
        draftBo.setTitle(publicInfo.getTitle());
        draftBo.setContent(publicInfo.getContent());
        draftBo.setAuthor("水务公司");

        //从参数配置获取微信推文参数
        String wechatArticleConfig = remoteConfigService.selectConfigByKey("waterfee.wechat_article.param");
        if(org.apache.commons.lang.StringUtils.isBlank(wechatArticleConfig)) {
            throw new ServiceException("缺少微信推文必要参数，请检查系统参数配置");
        }
        JSONObject jsonConfig = JSONObject.parseObject(wechatArticleConfig);
        /** 停水通知封面图片媒体ID */
        String water_outage_thumb_media_id;
        /** 供水通知封面图片媒体ID */
        String water_supply_thumb_media_id;
        if(jsonConfig.containsKey("water_outage_thumb_media_id")) {
            water_outage_thumb_media_id = jsonConfig.getString("water_outage_thumb_media_id");
        }else {
            throw new ServiceException("缺少停水通知封面图片媒体ID参数，请检查系统参数配置");
        }
        if(jsonConfig.containsKey("water_supply_thumb_media_id")) {
            water_supply_thumb_media_id = jsonConfig.getString("water_supply_thumb_media_id");
        }else {
            throw new ServiceException("缺少供水通知封面图片媒体ID参数，请检查系统参数配置");
        }
        // 根据公共信息类型设置草稿类型
        if ("1".equals(publicInfo.getInfoType())) {
            draftBo.setDraftType(DraftTypeEnum.WATER_OUTAGE.getCode());
            draftBo.setThumbMediaId(water_outage_thumb_media_id);
            draftBo.setDigest("紧急停水通知，请提前准备");
            draftBo.setThumbMediaId(water_outage_thumb_media_id);
        } else if ("2".equals(publicInfo.getInfoType())) {
            draftBo.setDraftType(DraftTypeEnum.WATER_SUPPLY.getCode());
            draftBo.setThumbMediaId(water_supply_thumb_media_id);
            draftBo.setDigest("供水恢复通知");
            draftBo.setThumbMediaId(water_supply_thumb_media_id);
        }

        // 设置默认值
//        draftBo.setShowCoverPic(true);
        draftBo.setNeedOpenComment(false);
        draftBo.setOnlyFansCanComment(false);

        return draftBo;
    }

    /**
     * 设置微信草稿信息
     */
    private void setWechatDraftInfo(WaterfeePublicInfoVo vo) {
        if (vo != null && vo.getWechatDraftId() != null) {
            try {
                WechatDraftVo draft = wechatDraftService.queryById(vo.getWechatDraftId());
                if (draft != null) {
                    vo.setWechatDraftStatus(draft.getDraftStatus());
                    vo.setWechatDraftStatusName(draft.getDraftStatusName());
                    vo.setWechatArticleStatus(draft.getArticleStatus());
                    vo.setWechatArticleStatusName(draft.getArticleStatusName());
                }
            } catch (Exception e) {
                // 获取微信草稿信息失败，不影响主要功能
                System.err.println("获取微信草稿信息失败: " + e.getMessage());
            }
        }
    }

    /**
     * 设置信息类型名称
     *
     * @param vo 公共信息视图对象
     */
    private void setInfoTypeName(WaterfeePublicInfoVo vo) {
        if (vo != null && StringUtils.isNotEmpty(vo.getInfoType())) {
            String typeName = "";
            switch (vo.getInfoType()) {
                case "1" -> typeName = "停水通知";
                case "2" -> typeName = "供水公告";
                case "3" -> typeName = "业务常识";
                case "4" -> typeName = "水费标准";
                case "5" -> typeName = "营商环境";
                case "6" -> typeName = "公司简介";
                default -> typeName = "其他";
            }
            vo.setInfoTypeName(typeName);
        }
    }
}
