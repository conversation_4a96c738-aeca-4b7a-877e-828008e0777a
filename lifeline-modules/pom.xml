<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.dromara</groupId>
        <artifactId>lifeline-cloud-plus</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>lifeline-system</module>
        <module>lifeline-gen</module>
        <module>lifeline-job</module>
        <module>lifeline-resource</module>
        <module>lifeline-workflow</module>
        <module>waterfee</module>
    </modules>

    <artifactId>lifeline-modules</artifactId>
    <packaging>pom</packaging>

    <description>
        lifeline-modules业务模块
    </description>

    <dependencies>
        <!-- 自定义负载均衡(多团队开发使用) -->
<!--        <dependency>-->
<!--            <groupId>org.dromara</groupId>-->
<!--            <artifactId>lifeline-common-loadbalancer</artifactId>-->
<!--        </dependency>-->

        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>org.dromara</groupId>-->
<!--            <artifactId>lifeline-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>org.dromara</groupId>-->
<!--            <artifactId>lifeline-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>org.dromara</groupId>-->
<!--            <artifactId>lifeline-common-prometheus</artifactId>-->
<!--        </dependency>-->

    </dependencies>

</project>
