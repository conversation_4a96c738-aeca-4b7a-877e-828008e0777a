<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ruoyi-auth" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="ruoyi/ruoyi-auth:2.3.0" />
        <option name="buildOnly" value="true" />
        <option name="sourceFilePath" value="ruoyi-auth/Dockerfile" />
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
</component>
