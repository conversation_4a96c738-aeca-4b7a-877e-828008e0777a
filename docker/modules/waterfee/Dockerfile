FROM gz01-srdart.srdcloud.cn/public/public-docker-virtual/openjdk:17.0.2-jdk

WORKDIR /app

EXPOSE 8207
ADD /lifeline-modules/waterfee/target/waterfee.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom \
           #-Dskywalking.agent.service_name=ruoyi-seata-server \
           #-Dskywalking.plugin.seata.server=true \
           #-javaagent:/ruoyi/skywalking/agent/skywalking-agent.jar \
           -jar app.jar

