FROM gz01-srdart.srdcloud.cn/public/public-docker-virtual/openjdk:17.0.2-jdk

WORKDIR /app

EXPOSE 8205
ADD /lifeline-modules/lifeline-resource/target/lifeline-resource.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom \
           #-Dskywalking.agent.service_name=ruoyi-seata-server \
           #-Dskywalking.plugin.seata.server=true \
           #-javaagent:/ruoyi/skywalking/agent/skywalking-agent.jar \
           -jar app.jar

